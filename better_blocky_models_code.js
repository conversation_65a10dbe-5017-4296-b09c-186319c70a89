// === BETTER BLOCKY CHARACTER MODELS ===
// Add this to create more detailed, Minecraft-style characters

// 1. Advanced Player Character with accessories
function createDetailedPlayer() {
    const player = new THREE.Group();
    
    // === HEAD ===
    const headGeometry = new THREE.BoxGeometry(0.8, 0.8, 0.8);
    const headMaterial = new THREE.MeshLambertMaterial({ color: 0xFFDBAC }); // Skin color
    const head = new THREE.Mesh(headGeometry, headMaterial);
    head.position.y = 1.0;
    
    // Hair/Hat
    const hairGeometry = new THREE.BoxGeometry(0.85, 0.3, 0.85);
    const hairMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 }); // Brown hair
    const hair = new THREE.Mesh(hairGeometry, hairMaterial);
    hair.position.set(0, 1.25, 0);
    
    // Eyes
    const eyeGeometry = new THREE.BoxGeometry(0.1, 0.1, 0.1);
    const eyeMaterial = new THREE.MeshLambertMaterial({ color: 0x000000 });
    const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
    const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
    leftEye.position.set(-0.15, 1.1, 0.35);
    rightEye.position.set(0.15, 1.1, 0.35);
    
    // === BODY ===
    const bodyGeometry = new THREE.BoxGeometry(0.8, 1.2, 0.4);
    const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x0066CC }); // Blue shirt
    const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
    
    // Belt
    const beltGeometry = new THREE.BoxGeometry(0.85, 0.1, 0.45);
    const beltMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 }); // Brown belt
    const belt = new THREE.Mesh(beltGeometry, beltMaterial);
    belt.position.y = -0.3;
    
    // === ARMS ===
    const armGeometry = new THREE.BoxGeometry(0.4, 1.2, 0.4);
    const armMaterial = new THREE.MeshLambertMaterial({ color: 0xFFDBAC }); // Skin color
    const leftArm = new THREE.Mesh(armGeometry, armMaterial);
    const rightArm = new THREE.Mesh(armGeometry, armMaterial);
    leftArm.position.set(-0.6, 0, 0);
    rightArm.position.set(0.6, 0, 0);
    
    // Sleeves
    const sleeveGeometry = new THREE.BoxGeometry(0.45, 0.6, 0.45);
    const sleeveMaterial = new THREE.MeshLambertMaterial({ color: 0x004499 }); // Darker blue
    const leftSleeve = new THREE.Mesh(sleeveGeometry, sleeveMaterial);
    const rightSleeve = new THREE.Mesh(sleeveGeometry, sleeveMaterial);
    leftSleeve.position.set(-0.6, 0.3, 0);
    rightSleeve.position.set(0.6, 0.3, 0);
    
    // === LEGS ===
    const legGeometry = new THREE.BoxGeometry(0.4, 1.2, 0.4);
    const legMaterial = new THREE.MeshLambertMaterial({ color: 0x000080 }); // Dark blue pants
    const leftLeg = new THREE.Mesh(legGeometry, legMaterial);
    const rightLeg = new THREE.Mesh(legGeometry, legMaterial);
    leftLeg.position.set(-0.2, -1.2, 0);
    rightLeg.position.set(0.2, -1.2, 0);
    
    // Shoes
    const shoeGeometry = new THREE.BoxGeometry(0.5, 0.2, 0.6);
    const shoeMaterial = new THREE.MeshLambertMaterial({ color: 0x654321 }); // Brown shoes
    const leftShoe = new THREE.Mesh(shoeGeometry, shoeMaterial);
    const rightShoe = new THREE.Mesh(shoeGeometry, shoeMaterial);
    leftShoe.position.set(-0.2, -1.9, 0.1);
    rightShoe.position.set(0.2, -1.9, 0.1);
    
    // === WEAPON (Optional) ===
    const swordGeometry = new THREE.BoxGeometry(0.1, 1.5, 0.1);
    const swordMaterial = new THREE.MeshLambertMaterial({ color: 0xC0C0C0 }); // Silver sword
    const sword = new THREE.Mesh(swordGeometry, swordMaterial);
    sword.position.set(0.8, 0.5, 0);
    sword.rotation.z = Math.PI / 6; // Angled
    
    const swordHandle = new THREE.BoxGeometry(0.15, 0.3, 0.15);
    const handleMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 }); // Brown handle
    const handle = new THREE.Mesh(swordHandle, handleMaterial);
    handle.position.set(0.8, -0.3, 0);
    handle.rotation.z = Math.PI / 6;
    
    // Add everything to group
    player.add(head, hair, leftEye, rightEye);
    player.add(body, belt);
    player.add(leftArm, rightArm, leftSleeve, rightSleeve);
    player.add(leftLeg, rightLeg, leftShoe, rightShoe);
    player.add(sword, handle);
    
    return player;
}

// 2. Advanced Orc Enemy with armor and weapons
function createDetailedOrc() {
    const orc = new THREE.Group();
    
    // === HEAD ===
    const headGeometry = new THREE.BoxGeometry(0.9, 0.9, 0.9);
    const headMaterial = new THREE.MeshLambertMaterial({ color: 0x4a5d23 }); // Green skin
    const head = new THREE.Mesh(headGeometry, headMaterial);
    head.position.y = 1.2;
    
    // Horns
    const hornGeometry = new THREE.ConeGeometry(0.1, 0.4, 6);
    const hornMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
    const leftHorn = new THREE.Mesh(hornGeometry, hornMaterial);
    const rightHorn = new THREE.Mesh(hornGeometry, hornMaterial);
    leftHorn.position.set(-0.3, 1.6, 0);
    rightHorn.position.set(0.3, 1.6, 0);
    
    // Eyes (glowing red)
    const eyeGeometry = new THREE.BoxGeometry(0.15, 0.15, 0.15);
    const eyeMaterial = new THREE.MeshLambertMaterial({ 
        color: 0xFF0000, 
        emissive: 0x330000 
    });
    const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
    const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
    leftEye.position.set(-0.2, 1.3, 0.4);
    rightEye.position.set(0.2, 1.3, 0.4);
    
    // Tusks
    const tuskGeometry = new THREE.ConeGeometry(0.05, 0.3, 4);
    const tuskMaterial = new THREE.MeshLambertMaterial({ color: 0xFFFFF0 });
    const leftTusk = new THREE.Mesh(tuskGeometry, tuskMaterial);
    const rightTusk = new THREE.Mesh(tuskGeometry, tuskMaterial);
    leftTusk.position.set(-0.15, 0.9, 0.4);
    rightTusk.position.set(0.15, 0.9, 0.4);
    leftTusk.rotation.x = Math.PI;
    rightTusk.rotation.x = Math.PI;
    
    // === BODY ===
    const bodyGeometry = new THREE.BoxGeometry(1.2, 1.8, 0.6);
    const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x654321 }); // Brown armor
    const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
    
    // Armor plates
    const armorGeometry = new THREE.BoxGeometry(1.3, 0.3, 0.7);
    const armorMaterial = new THREE.MeshLambertMaterial({ color: 0x2F4F2F }); // Dark gray
    const chestArmor = new THREE.Mesh(armorGeometry, armorMaterial);
    chestArmor.position.y = 0.4;
    
    // === ARMS ===
    const armGeometry = new THREE.BoxGeometry(0.5, 1.6, 0.5);
    const armMaterial = new THREE.MeshLambertMaterial({ color: 0x3a4d1a }); // Dark green
    const leftArm = new THREE.Mesh(armGeometry, armMaterial);
    const rightArm = new THREE.Mesh(armGeometry, armMaterial);
    leftArm.position.set(-0.85, 0.1, 0);
    rightArm.position.set(0.85, 0.1, 0);
    
    // Shoulder spikes
    const spikeGeometry = new THREE.ConeGeometry(0.15, 0.4, 6);
    const spikeMaterial = new THREE.MeshLambertMaterial({ color: 0x2F4F2F });
    const leftSpike = new THREE.Mesh(spikeGeometry, spikeMaterial);
    const rightSpike = new THREE.Mesh(spikeGeometry, spikeMaterial);
    leftSpike.position.set(-0.85, 0.9, 0);
    rightSpike.position.set(0.85, 0.9, 0);
    
    // === WEAPON ===
    const axeHandleGeometry = new THREE.BoxGeometry(0.1, 1.8, 0.1);
    const handleMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
    const axeHandle = new THREE.Mesh(axeHandleGeometry, handleMaterial);
    axeHandle.position.set(-1.2, 0.5, 0);
    
    const axeHeadGeometry = new THREE.BoxGeometry(0.8, 0.4, 0.2);
    const axeHeadMaterial = new THREE.MeshLambertMaterial({ color: 0x696969 });
    const axeHead = new THREE.Mesh(axeHeadGeometry, axeHeadMaterial);
    axeHead.position.set(-1.2, 1.2, 0);
    
    // === LEGS ===
    const legGeometry = new THREE.BoxGeometry(0.5, 1.6, 0.5);
    const legMaterial = new THREE.MeshLambertMaterial({ color: 0x2a3d13 });
    const leftLeg = new THREE.Mesh(legGeometry, legMaterial);
    const rightLeg = new THREE.Mesh(legGeometry, legMaterial);
    leftLeg.position.set(-0.3, -1.7, 0);
    rightLeg.position.set(0.3, -1.7, 0);
    
    // Boots with spikes
    const bootGeometry = new THREE.BoxGeometry(0.6, 0.3, 0.8);
    const bootMaterial = new THREE.MeshLambertMaterial({ color: 0x2F4F2F });
    const leftBoot = new THREE.Mesh(bootGeometry, bootMaterial);
    const rightBoot = new THREE.Mesh(bootGeometry, bootMaterial);
    leftBoot.position.set(-0.3, -2.65, 0.1);
    rightBoot.position.set(0.3, -2.65, 0.1);
    
    // Add everything to group
    orc.add(head, leftHorn, rightHorn, leftEye, rightEye, leftTusk, rightTusk);
    orc.add(body, chestArmor);
    orc.add(leftArm, rightArm, leftSpike, rightSpike);
    orc.add(axeHandle, axeHead);
    orc.add(leftLeg, rightLeg, leftBoot, rightBoot);
    
    return orc;
}

// 3. Animated Character (basic bobbing animation)
function animateCharacter(characterMesh, deltaTime) {
    // Simple bobbing animation when moving
    characterMesh.position.y += Math.sin(Date.now() * 0.005) * 0.02;
    
    // Arm swinging
    if (characterMesh.children[4]) { // Left arm
        characterMesh.children[4].rotation.x = Math.sin(Date.now() * 0.008) * 0.3;
    }
    if (characterMesh.children[5]) { // Right arm
        characterMesh.children[5].rotation.x = Math.sin(Date.now() * 0.008 + Math.PI) * 0.3;
    }
}

// 4. How to replace his existing character creation:
/*
// In his Orc class, replace createMesh() with:
createMesh() {
    this.mesh = createDetailedOrc();
    this.mesh.position.copy(this.position);
    scene.add(this.mesh);
    this.createHealthBar();
}

// In his update function, add animation:
update(deltaTime, playerPosition) {
    // ... existing code ...
    
    // Add animation
    if (this.mesh && this.state === 'chasing') {
        animateCharacter(this.mesh, deltaTime);
    }
}
*/

// 5. Create different orc variants
function createOrcVariant(type) {
    const baseOrc = createDetailedOrc();
    
    switch(type) {
        case 'warrior':
            // Add extra armor
            baseOrc.scale.set(1.1, 1.1, 1.1);
            break;
        case 'archer':
            // Remove axe, add bow
            baseOrc.children[10].visible = false; // Hide axe
            baseOrc.children[11].visible = false;
            // Add bow code here
            break;
        case 'chief':
            // Bigger and with crown
            baseOrc.scale.set(1.3, 1.3, 1.3);
            const crown = new THREE.CylinderGeometry(0.4, 0.5, 0.2, 8);
            const crownMaterial = new THREE.MeshLambertMaterial({ color: 0xFFD700 });
            const crownMesh = new THREE.Mesh(crown, crownMaterial);
            crownMesh.position.y = 1.8;
            baseOrc.add(crownMesh);
            break;
    }
    
    return baseOrc;
}