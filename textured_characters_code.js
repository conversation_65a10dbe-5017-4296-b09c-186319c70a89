// === TEXTURED CHARACTERS CODE ===
// Add this to create characters with his 2D art as textures

// 1. Create canvas-based textures from his 2D art
function createCharacterTexture(characterType) {
    const canvas = document.createElement('canvas');
    canvas.width = 64;
    canvas.height = 64;
    const ctx = canvas.getContext('2d');
    
    if (characterType === 'player') {
        // Player face texture - he can customize these colors/patterns
        ctx.fillStyle = '#FFDBAC'; // Skin color
        ctx.fillRect(0, 0, 64, 64);
        
        // Eyes
        ctx.fillStyle = '#000000';
        ctx.fillRect(16, 20, 8, 8); // Left eye
        ctx.fillRect(40, 20, 8, 8); // Right eye
        
        // Mouth
        ctx.fillStyle = '#8B4513';
        ctx.fillRect(24, 40, 16, 4);
        
        // Hair
        ctx.fillStyle = '#8B4513';
        ctx.fillRect(8, 8, 48, 16);
        
    } else if (characterType === 'orc') {
        // Orc face texture
        ctx.fillStyle = '#4a5d23'; // Green skin
        ctx.fillRect(0, 0, 64, 64);
        
        // Red eyes
        ctx.fillStyle = '#FF0000';
        ctx.fillRect(16, 20, 8, 8);
        ctx.fillRect(40, 20, 8, 8);
        
        // Angry mouth
        ctx.fillStyle = '#000000';
        ctx.fillRect(20, 40, 24, 6);
        
        // Tusks
        ctx.fillStyle = '#FFFFFF';
        ctx.fillRect(24, 46, 4, 8);
        ctx.fillRect(36, 46, 4, 8);
    }
    
    const texture = new THREE.CanvasTexture(canvas);
    texture.magFilter = THREE.NearestFilter; // Pixelated look
    texture.minFilter = THREE.NearestFilter;
    return texture;
}

// 2. Create textured materials
function createCharacterMaterials(characterType) {
    const faceTexture = createCharacterTexture(characterType);
    
    const materials = {
        face: new THREE.MeshLambertMaterial({ map: faceTexture }),
        body: new THREE.MeshLambertMaterial({ 
            color: characterType === 'player' ? 0x0066CC : 0x4a5d23 
        }),
        arms: new THREE.MeshLambertMaterial({ 
            color: characterType === 'player' ? 0xFFDBAC : 0x3a4d1a 
        }),
        legs: new THREE.MeshLambertMaterial({ 
            color: characterType === 'player' ? 0x000080 : 0x2a3d13 
        })
    };
    
    return materials;
}

// 3. Create textured player character
function createTexturedPlayer() {
    const materials = createCharacterMaterials('player');
    
    // Body
    const bodyGeometry = new THREE.BoxGeometry(0.8, 1.2, 0.4);
    const body = new THREE.Mesh(bodyGeometry, materials.body);
    
    // Head with face texture
    const headGeometry = new THREE.BoxGeometry(0.8, 0.8, 0.8);
    const head = new THREE.Mesh(headGeometry, materials.face);
    head.position.y = 1.0;
    
    // Arms
    const armGeometry = new THREE.BoxGeometry(0.4, 1.2, 0.4);
    const leftArm = new THREE.Mesh(armGeometry, materials.arms);
    const rightArm = new THREE.Mesh(armGeometry, materials.arms);
    leftArm.position.set(-0.6, 0, 0);
    rightArm.position.set(0.6, 0, 0);
    
    // Legs
    const legGeometry = new THREE.BoxGeometry(0.4, 1.2, 0.4);
    const leftLeg = new THREE.Mesh(legGeometry, materials.legs);
    const rightLeg = new THREE.Mesh(legGeometry, materials.legs);
    leftLeg.position.set(-0.2, -1.2, 0);
    rightLeg.position.set(0.2, -1.2, 0);
    
    // Group everything
    const player = new THREE.Group();
    player.add(body);
    player.add(head);
    player.add(leftArm);
    player.add(rightArm);
    player.add(leftLeg);
    player.add(rightLeg);
    
    return player;
}

// 4. Create textured orc enemy
function createTexturedOrc() {
    const materials = createCharacterMaterials('orc');
    
    // Body (bigger than player)
    const bodyGeometry = new THREE.BoxGeometry(1.0, 1.6, 0.5);
    const body = new THREE.Mesh(bodyGeometry, materials.body);
    
    // Head with orc face
    const headGeometry = new THREE.BoxGeometry(0.8, 0.8, 0.8);
    const head = new THREE.Mesh(headGeometry, materials.face);
    head.position.y = 1.2;
    
    // Arms (muscular)
    const armGeometry = new THREE.BoxGeometry(0.5, 1.4, 0.5);
    const leftArm = new THREE.Mesh(armGeometry, materials.arms);
    const rightArm = new THREE.Mesh(armGeometry, materials.arms);
    leftArm.position.set(-0.75, 0.1, 0);
    rightArm.position.set(0.75, 0.1, 0);
    
    // Legs
    const legGeometry = new THREE.BoxGeometry(0.4, 1.4, 0.4);
    const leftLeg = new THREE.Mesh(legGeometry, materials.legs);
    const rightLeg = new THREE.Mesh(legGeometry, materials.legs);
    leftLeg.position.set(-0.25, -1.5, 0);
    rightLeg.position.set(0.25, -1.5, 0);
    
    // Group everything
    const orc = new THREE.Group();
    orc.add(body);
    orc.add(head);
    orc.add(leftArm);
    orc.add(rightArm);
    orc.add(leftLeg);
    orc.add(rightLeg);
    
    return orc;
}

// 5. How to use in his existing code:
// Replace the createMesh() function in his Orc class with:
/*
createMesh() {
    this.mesh = createTexturedOrc();
    this.mesh.position.copy(this.position);
    scene.add(this.mesh);
    this.createHealthBar(); // Keep existing health bar code
}
*/

// 6. For loading his own 2D art as textures:
function loadCustomTexture(imagePath) {
    const loader = new THREE.TextureLoader();
    const texture = loader.load(imagePath);
    texture.magFilter = THREE.NearestFilter;
    texture.minFilter = THREE.NearestFilter;
    return texture;
}

// Example: Use his character art file
/*
const customTexture = loadCustomTexture('path/to/his/character.png');
const material = new THREE.MeshLambertMaterial({ map: customTexture });
*/