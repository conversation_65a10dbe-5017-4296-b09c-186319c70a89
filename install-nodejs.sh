#!/bin/bash

echo "🚀 Installing Node.js for Beetle3D..."

# Method 1: Using Node Version Manager (NVM) - No sudo required
echo "📦 Installing Node.js using NVM (recommended)..."

# Download and install NVM
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

# Reload bash profile
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"

# Install latest LTS Node.js
nvm install --lts
nvm use --lts
nvm alias default lts/*

echo "✅ Node.js installation complete!"
echo "📋 Versions installed:"
node --version
npm --version

echo ""
echo "🎮 Now you can run the Beetle3D server:"
echo "cd server"
echo "npm install"
echo "npm start"

echo ""
echo "🔧 If this doesn't work, try these manual steps:"
echo ""
echo "MANUAL INSTALLATION (if script fails):"
echo "1. Download Node.js from: https://nodejs.org/"
echo "2. Or use package manager:"
echo "   sudo apt update"
echo "   sudo apt install nodejs npm"
echo "3. Or use snap:"
echo "   sudo snap install node --classic"
echo ""
echo "ALTERNATIVE: Use system package manager"
echo "sudo apt update && sudo apt install nodejs npm"
echo ""
echo "VERIFY INSTALLATION:"
echo "node --version"
echo "npm --version"
