#!/bin/bash

echo "🚀 Setting up Beetle3D Server..."

# Function to test command
test_command() {
    if command -v "$1" >/dev/null 2>&1; then
        echo "✅ $1 found: $(command -v $1)"
        return 0
    else
        echo "❌ $1 not found"
        return 1
    fi
}

# Test Node.js installation (VirtualMin compatible)
echo "🔍 Checking Node.js installation..."
NODE_CMD=""
if test_command node; then
    NODE_CMD="node"
elif test_command nodejs; then
    NODE_CMD="nodejs"
else
    echo ""
    echo "❌ Node.js not detected"
    echo ""
    echo "🔧 To install Node.js:"
    echo "1. Via package manager:"
    echo "   sudo apt update && sudo apt install nodejs npm"
    echo ""
    echo "2. Via NodeSource (recommended):"
    echo "   curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -"
    echo "   sudo apt-get install -y nodejs"
    echo ""
    echo "3. Via VirtualMin Panel (if using VirtualMin):"
    echo "   System Settings → Software Packages → Search 'nodejs'"
    echo ""
    exit 1
fi

# Test npm
echo "🔍 Checking npm..."
NPM_CMD=""
if test_command npm; then
    NPM_CMD="npm"
else
    echo "❌ npm not found. Installing npm..."
    sudo apt install npm -y
    NPM_CMD="npm"
fi

# Show versions
echo ""
echo "✅ Node.js version: $($NODE_CMD --version)"
echo "✅ npm version: $($NPM_CMD --version)"

# Test MariaDB/MySQL
echo ""
echo "🔍 Checking database..."
DB_CMD=""
if test_command mysql; then
    DB_CMD="mysql"
    echo "✅ MySQL client found"
elif test_command mariadb; then
    DB_CMD="mariadb"
    echo "✅ MariaDB client found"
else
    echo "❌ Database client not found"
    echo "Installing MariaDB client..."
    sudo apt install mariadb-client -y
    DB_CMD="mysql"
fi

# Test database connection
echo ""
echo "🔍 Testing database connection..."
echo "Database: beetle"
echo "User: beetle"
echo "Password: 4SrTF6CYdFhKYhG"

if $DB_CMD -u beetle -p4SrTF6CYdFhKYhG -e "SELECT 1;" 2>/dev/null; then
    echo "✅ Database connection successful"

    # Check if database exists
    if $DB_CMD -u beetle -p4SrTF6CYdFhKYhG -e "USE beetle; SELECT 1;" 2>/dev/null; then
        echo "✅ Database 'beetle' exists"
    else
        echo "⚠️  Database 'beetle' doesn't exist. Creating..."
        $DB_CMD -u beetle -p4SrTF6CYdFhKYhG -e "CREATE DATABASE beetle;" 2>/dev/null
    fi
else
    echo "❌ Database connection failed"
    echo ""
    echo "🔧 Database setup required:"
    echo "1. Make sure MariaDB/MySQL is running"
    echo "2. Create database and user:"
    echo "   sudo mysql"
    echo "   CREATE DATABASE beetle;"
    echo "   CREATE USER 'beetle'@'localhost' IDENTIFIED BY '4SrTF6CYdFhKYhG';"
    echo "   GRANT ALL PRIVILEGES ON beetle.* TO 'beetle'@'localhost';"
    echo "   FLUSH PRIVILEGES;"
    echo "   EXIT;"
fi

# Navigate to server directory
cd server

# Install dependencies
echo "📦 Installing server dependencies..."
$NPM_CMD install

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please check server/.env file."
    exit 1
fi

echo "✅ .env file found"

# Setup database with SQL dump
echo "🗄️ Setting up database..."
if [ -f "database.sql" ]; then
    echo "📋 Found database.sql file"

    # Create database if it doesn't exist
    echo "Creating database 'beetle'..."
    $DB_CMD -u beetle -p4SrTF6CYdFhKYhG -e "CREATE DATABASE IF NOT EXISTS beetle;" 2>/dev/null

    # Import SQL dump
    echo "📥 Importing database schema..."
    if $DB_CMD -u beetle -p4SrTF6CYdFhKYhG beetle < database.sql; then
        echo "✅ Database setup complete"
    else
        echo "⚠️  Database import had issues, but continuing..."
    fi
else
    echo "⚠️  database.sql not found. Database setup skipped."
fi

# Final database connection test
echo "🔍 Final database connection test..."
if $DB_CMD -u beetle -p4SrTF6CYdFhKYhG -e "USE beetle; SHOW TABLES;" >/dev/null 2>&1; then
    echo "✅ Database is ready"
else
    echo "⚠️  Database connection issues. Server may not work properly."
    echo ""
    echo "🔧 Manual database setup:"
    echo "   $DB_CMD -u beetle -p4SrTF6CYdFhKYhG"
    echo "   CREATE DATABASE IF NOT EXISTS beetle;"
    echo "   USE beetle;"
    echo "   SOURCE database.sql;"
fi

# Start server
echo ""
echo "🚀 Starting Beetle3D server..."
echo "📡 Server will run on http://localhost:3001"
echo "🎮 Game will be available at http://localhost:3001"
echo "🌐 API endpoints at http://localhost:3001/api"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

$NPM_CMD start
