#!/bin/bash

echo "📦 Creating Beetle3D Deployment Package..."

# Create deployment directory
DEPLOY_DIR="beetle3d-deployment"
rm -rf $DEPLOY_DIR
mkdir -p $DEPLOY_DIR

echo "📁 Creating directory structure..."

# Create frontend directory structure
mkdir -p $DEPLOY_DIR/frontend/js
mkdir -p $DEPLOY_DIR/frontend/images
mkdir -p $DEPLOY_DIR/frontend/libs

# Create backend directory
mkdir -p $DEPLOY_DIR/backend

echo "📋 Copying frontend files..."

# Copy main HTML file
cp index.html $DEPLOY_DIR/frontend/

# Copy JavaScript files
cp js/auth.js $DEPLOY_DIR/frontend/js/
cp js/saveload.js $DEPLOY_DIR/frontend/js/
cp js/inventory.js $DEPLOY_DIR/frontend/js/
cp js/player.js $DEPLOY_DIR/frontend/js/
cp js/world.js $DEPLOY_DIR/frontend/js/
cp js/enemies.js $DEPLOY_DIR/frontend/js/
cp js/items.js $DEPLOY_DIR/frontend/js/
cp js/characters.js $DEPLOY_DIR/frontend/js/
cp js/ui.js $DEPLOY_DIR/frontend/js/
cp js/noise.js $DEPLOY_DIR/frontend/js/
cp js/main.js $DEPLOY_DIR/frontend/js/

# Copy images
if [ -d "images" ]; then
    cp -r images/* $DEPLOY_DIR/frontend/images/
fi

# Copy Three.js library
if [ -f "libs/three.min.js" ]; then
    cp libs/three.min.js $DEPLOY_DIR/frontend/libs/
else
    echo "⚠️  Warning: three.min.js not found. You'll need to download it separately."
fi

echo "🗄️ Copying backend files..."

# Copy server files
cp server/package.json $DEPLOY_DIR/backend/
cp server/server.js $DEPLOY_DIR/backend/
cp server/database.sql $DEPLOY_DIR/backend/
cp server/.env $DEPLOY_DIR/backend/

echo "📝 Creating deployment files..."

# Copy deployment instructions
cp deployment-instructions.md $DEPLOY_DIR/
cp setup-server.sh $DEPLOY_DIR/backend/

# Create upload instructions
cat > $DEPLOY_DIR/UPLOAD-INSTRUCTIONS.txt << 'EOF'
BEETLE3D UPLOAD INSTRUCTIONS
============================

1. FRONTEND FILES (Upload to your web root directory):
   - Upload everything in the 'frontend/' folder to your public_html or web root
   - Make sure index.html is in the root directory
   - Ensure file permissions are 644 for files, 755 for directories

2. BACKEND FILES (Upload to server directory):
   - Upload everything in the 'backend/' folder to a server directory
   - Install Node.js dependencies: cd backend && npm install
   - Start the server: npm start

3. DATABASE SETUP:
   - Connect to MySQL: mysql -u beetle -p4SrTF6CYdFhKYhG
   - Run: CREATE DATABASE IF NOT EXISTS beetle;
   - Run: USE beetle;
   - Run: SOURCE database.sql;

4. CONFIGURATION:
   - Update backend/.env with your domain settings
   - Ensure ports 3001 and 3306 are open
   - Test the game at https://beetle3d.com

5. TROUBLESHOOTING:
   - Check browser console (F12) for errors
   - Check server logs for backend issues
   - Verify database connection works

Your game includes:
✅ Complete 3D Minecraft-style gameplay
✅ User authentication and database
✅ Save/load system
✅ Inventory and hotbar with drag & drop
✅ Block placement system
✅ Multiplayer-ready architecture

For detailed instructions, see deployment-instructions.md
EOF

# Create file structure overview
cat > $DEPLOY_DIR/FILE-STRUCTURE.txt << 'EOF'
BEETLE3D FILE STRUCTURE
=======================

frontend/                    # Upload to web root (public_html)
├── index.html              # Main game file
├── js/                     # Game scripts
│   ├── auth.js            # Authentication
│   ├── saveload.js        # Save/load system
│   ├── inventory.js       # Inventory & hotbar
│   ├── player.js          # Player controls
│   ├── world.js           # World generation
│   ├── enemies.js         # Enemy system
│   ├── items.js           # Items & blocks
│   ├── characters.js      # Character models
│   ├── ui.js              # User interface
│   ├── noise.js           # Terrain generation
│   └── main.js            # Main game loop
├── images/                # Game assets
│   └── mycharacter.png    # Custom character
└── libs/                  # Libraries
    └── three.min.js       # 3D graphics

backend/                    # Upload to server directory
├── package.json           # Dependencies
├── server.js             # API server
├── database.sql          # Database schema
├── .env                  # Configuration
└── setup-server.sh       # Setup script

DEPLOYMENT STEPS:
1. Upload frontend/ contents to web root
2. Upload backend/ to server directory
3. Setup database with database.sql
4. Install Node.js dependencies
5. Start server and test
EOF

echo "🗜️ Creating ZIP archive..."

# Create ZIP file
cd $DEPLOY_DIR
zip -r ../beetle3d-deployment.zip . -x "*.DS_Store" "*.git*"
cd ..

echo "✅ Deployment package created successfully!"
echo ""
echo "📦 Package: beetle3d-deployment.zip"
echo "📁 Contents: $DEPLOY_DIR/"
echo ""
echo "🚀 Ready to upload to beetle3d.com!"
echo ""
echo "Next steps:"
echo "1. Extract beetle3d-deployment.zip"
echo "2. Upload frontend/ contents to your web root"
echo "3. Upload backend/ to your server"
echo "4. Follow UPLOAD-INSTRUCTIONS.txt"
echo ""
echo "🎮 Your complete Minecraft-style game with database backend is ready!"

# Show package contents
echo ""
echo "📋 Package contents:"
find $DEPLOY_DIR -type f | sort
