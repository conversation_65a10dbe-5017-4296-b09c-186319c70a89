#!/bin/bash

echo "🎮 Creating Frontend-Only Beetle3D Package..."
echo "This version works without Node.js - just upload and play!"

# Create frontend-only directory
FRONTEND_DIR="beetle3d-frontend-only"
rm -rf $FRONTEND_DIR
mkdir -p $FRONTEND_DIR

echo "📁 Creating frontend package..."

# Copy main game files
cp index.html $FRONTEND_DIR/
cp -r js/ $FRONTEND_DIR/
cp -r images/ $FRONTEND_DIR/

# Create a simple instructions file
cat > $FRONTEND_DIR/UPLOAD-INSTRUCTIONS.txt << 'EOF'
BEETLE3D FRONTEND-ONLY UPLOAD INSTRUCTIONS
==========================================

🎮 SIMPLE UPLOAD - NO SERVER SETUP NEEDED!

1. UPLOAD FILES:
   - Upload ALL files in this folder to your web root (public_html)
   - Make sure index.html is in the root directory
   - Ensure file permissions are 644 for files, 755 for directories

2. ACCESS YOUR GAME:
   - Visit: https://beetle3d.com
   - Game loads instantly - no setup required!

3. FEATURES INCLUDED:
   ✅ Complete 3D Minecraft-style world
   ✅ Player movement (WASD + mouse)
   ✅ Block placement system (right-click)
   ✅ Inventory with 20 slots + 6-slot hotbar
   ✅ Drag & drop between inventory and hotbar
   ✅ Enemy spawning and combat
   ✅ Custom character system
   ✅ Local save/load (browser storage)

4. WHAT'S DIFFERENT:
   - Uses browser localStorage instead of database
   - No user accounts (guest play only)
   - Progress saves locally per browser
   - Still fully playable and fun!

5. FUTURE UPGRADES:
   - Can add Node.js backend later for accounts
   - Can add multiplayer features later
   - This version is fully compatible with upgrades

🚀 READY TO PLAY!
Just upload these files and your game is live at beetle3d.com!

No Node.js, no database setup, no complications - just pure gaming fun! 🎯
EOF

# Create a simple README
cat > $FRONTEND_DIR/README.md << 'EOF'
# Beetle3D - Frontend Only Version

## 🎮 Complete Minecraft-Style Game

This is a fully functional 3D Minecraft-style game that runs entirely in the browser.

### Features:
- ✅ 3D world with terrain generation
- ✅ Player movement and controls
- ✅ Block placement system
- ✅ Inventory management with drag & drop
- ✅ Enemy combat system
- ✅ Custom character support
- ✅ Local save/load system

### Installation:
1. Upload all files to your web server
2. Visit your domain
3. Start playing immediately!

### No Requirements:
- No Node.js needed
- No database setup
- No server configuration
- Just upload and play!

### Browser Compatibility:
- Chrome ✅
- Firefox ✅
- Safari ✅
- Edge ✅

Enjoy your Beetle3D game! 🚀
EOF

# Create compressed package
echo "🗜️ Creating compressed package..."
tar -czf beetle3d-frontend-only.tar.gz $FRONTEND_DIR/

echo "✅ Frontend-only package created!"
echo ""
echo "📦 Package: beetle3d-frontend-only.tar.gz"
echo "📁 Folder: $FRONTEND_DIR/"
echo ""
echo "🚀 SIMPLE DEPLOYMENT:"
echo "1. Extract the package"
echo "2. Upload contents to beetle3d.com web root"
echo "3. Visit beetle3d.com - game works immediately!"
echo ""
echo "🎮 FEATURES:"
echo "✅ Complete 3D Minecraft-style gameplay"
echo "✅ No server setup required"
echo "✅ Works on any web hosting"
echo "✅ Instant deployment"
echo "✅ Fully playable game"
echo ""
echo "🔮 FUTURE: Can upgrade to full backend later!"

# Show package contents
echo ""
echo "📋 Package contents:"
find $FRONTEND_DIR -type f | sort
