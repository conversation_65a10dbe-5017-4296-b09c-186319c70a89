# Beetle3D Deployment Instructions

## 📦 Deployment Package Contents

### Frontend Files (Upload to web directory)
```
/
├── index.html                 # Main game file
├── js/                       # Game JavaScript files
│   ├── auth.js              # Authentication system
│   ├── saveload.js          # Save/load system
│   ├── inventory.js         # Inventory and hotbar
│   ├── player.js            # Player controls and block placement
│   ├── world.js             # World generation
│   ├── enemies.js           # Enemy system
│   ├── items.js             # Items and blocks
│   ├── characters.js        # Character models
│   ├── ui.js                # User interface
│   ├── noise.js             # Terrain generation
│   └── main.js              # Main game loop
├── images/                   # Game assets
│   └── mycharacter.png      # Custom character image
└── libs/                     # Third-party libraries
    └── three.min.js         # Three.js 3D library
```

### Backend Files (Upload to server directory)
```
server/
├── package.json             # Node.js dependencies
├── server.js               # Main server file
├── database.sql            # Database schema
├── .env                    # Environment configuration
└── setup-server.sh        # Server setup script
```

## 🚀 Deployment Steps

### 1. Database Setup
```sql
-- Connect to MySQL as beetle user
mysql -u beetle -p4SrTF6CYdFhKYhG

-- Create database and tables
CREATE DATABASE IF NOT EXISTS beetle;
USE beetle;
SOURCE server/database.sql;
```

### 2. Upload Frontend Files
- Upload all files in root directory to your web root (public_html)
- Ensure file permissions are set correctly (644 for files, 755 for directories)

### 3. Upload Backend Files
- Upload server/ directory to your server
- Install Node.js if not already installed
- Run: `cd server && npm install`
- Start server: `npm start`

### 4. Configure Domain
- Update .env file with your domain: `beetle3d.com`
- Update client URLs in server.js if needed
- Ensure port 3001 is open for API access

### 5. Test Deployment
- Visit: https://beetle3d.com
- Test user registration and login
- Test game save/load functionality
- Check browser console for any errors

## 🔧 Server Configuration

### Environment Variables (.env)
```
DB_HOST=localhost
DB_USER=beetle
DB_PASSWORD=4SrTF6CYdFhKYhG
DB_NAME=beetle
PORT=3001
DOMAIN=beetle3d.com
```

### Required Ports
- Port 80/443: Web server (frontend)
- Port 3001: API server (backend)
- Port 3306: MySQL database

## 🎮 Features Included

### ✅ Complete Game System
- 3D Minecraft-style world
- Player movement and controls
- Block placement system
- Inventory and hotbar with drag & drop
- Enemy spawning and combat
- Custom character system

### ✅ Database Backend
- User authentication (register/login)
- Game save/load system
- Cross-device progress sync
- Multiplayer-ready architecture

### ✅ Security Features
- Password hashing with bcrypt
- JWT token authentication
- CORS protection
- SQL injection prevention

## 🔍 Troubleshooting

### Common Issues
1. **Database connection fails**: Check MySQL credentials and database exists
2. **CORS errors**: Verify domain configuration in server.js
3. **File permissions**: Ensure web files are readable (644)
4. **Node.js errors**: Check Node.js version (requires 14+)

### Debug Commands
```bash
# Check database connection
mysql -u beetle -p4SrTF6CYdFhKYhG -e "SHOW DATABASES;"

# Check server logs
cd server && npm start

# Test API endpoints
curl http://beetle3d.com:3001/api/register -X POST -H "Content-Type: application/json" -d '{"username":"test","password":"test123"}'
```

## 📞 Support
If you encounter issues:
1. Check browser console (F12) for JavaScript errors
2. Check server logs for backend errors
3. Verify database connection and tables exist
4. Ensure all files uploaded correctly

## 🎯 Next Steps
After deployment:
1. Test all game features
2. Create admin accounts
3. Monitor server performance
4. Plan multiplayer features (Ctrl+J)
5. Add more game content

Your Beetle3D game is ready for production! 🚀
