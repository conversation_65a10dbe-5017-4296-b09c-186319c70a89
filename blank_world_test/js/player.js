// === SIMPLE PLAYER SYSTEM ===
// Basic player movement and camera controls

class Player {
    constructor() {
        this.position = new THREE.Vector3(0, 20, 0);
        this.velocity = new THREE.Vector3(0, 0, 0);
        this.onGround = false;
        this.height = 1.8;
        this.isRunning = false;
        
        this.keys = {};
        this.rotation = { x: 0, y: 0 };
        this.isMoving = false;

        this.setupControls();
    }
    
    setupControls() {
        // Keyboard controls
        document.addEventListener('keydown', (event) => {
            this.keys[event.code] = true;
            
            if (event.code === 'KeyR') {
                this.isRunning = true;
            }
        });

        document.addEventListener('keyup', (event) => {
            this.keys[event.code] = false;
            
            if (event.code === 'KeyR') {
                this.isRunning = false;
            }
        });

        // Mouse controls
        let isPointerLocked = false;
        
        document.addEventListener('click', () => {
            if (!isPointerLocked) {
                document.body.requestPointerLock();
            }
        });

        document.addEventListener('pointerlockchange', () => {
            isPointerLocked = document.pointerLockElement === document.body;
        });

        document.addEventListener('mousemove', (event) => {
            if (isPointerLocked) {
                const sensitivity = 0.002;
                this.rotation.y -= event.movementX * sensitivity;
                this.rotation.x -= event.movementY * sensitivity;
                
                // Limit vertical rotation
                this.rotation.x = Math.max(-Math.PI/2, Math.min(Math.PI/2, this.rotation.x));
            }
        });
    }

    update(deltaTime) {
        this.handleMovement(deltaTime);
        this.updateCamera();
        this.updateUI();
    }

    handleMovement(deltaTime) {
        const moveSpeed = this.isRunning ? 12 : 6;
        const jumpPower = 8;
        
        // Get movement direction based on camera
        const forward = new THREE.Vector3(0, 0, -1);
        const right = new THREE.Vector3(1, 0, 0);
        
        forward.applyAxisAngle(new THREE.Vector3(0, 1, 0), this.rotation.y);
        right.applyAxisAngle(new THREE.Vector3(0, 1, 0), this.rotation.y);
        
        const movement = new THREE.Vector3(0, 0, 0);
        
        if (this.keys['KeyW']) movement.add(forward);
        if (this.keys['KeyS']) movement.sub(forward);
        if (this.keys['KeyA']) movement.sub(right);
        if (this.keys['KeyD']) movement.add(right);
        
        if (movement.length() > 0) {
            movement.normalize();
            this.velocity.x = movement.x * moveSpeed;
            this.velocity.z = movement.z * moveSpeed;
            this.isMoving = true;
        } else {
            this.velocity.x *= 0.8; // Friction
            this.velocity.z *= 0.8;
            this.isMoving = false;
        }

        // Jumping
        if (this.keys['Space'] && this.onGround) {
            this.velocity.y = jumpPower;
            this.onGround = false;
        }

        // Gravity
        this.velocity.y -= 25 * deltaTime;

        // Update position
        this.position.add(this.velocity.clone().multiplyScalar(deltaTime));

        // Ground collision
        const groundHeight = world.getHeightAt(this.position.x, this.position.z);
        if (this.position.y <= groundHeight + this.height) {
            this.position.y = groundHeight + this.height;
            this.velocity.y = 0;
            this.onGround = true;
        }
    }

    updateCamera() {
        // Update camera position and rotation
        camera.position.copy(this.position);
        camera.position.y += 0.6; // Eye level
        
        camera.rotation.x = this.rotation.x;
        camera.rotation.y = this.rotation.y;
        camera.rotation.z = 0;
    }

    updateUI() {
        // Update position display
        const pos = this.position;
        document.getElementById('position').textContent = 
            `${Math.round(pos.x)}, ${Math.round(pos.y)}, ${Math.round(pos.z)}`;
    }
}
