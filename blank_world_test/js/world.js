// === BLANK WORLD GENERATION ===
// Simple terrain generation without any objects

// Game constants
const CHUNK_SIZE = 16;
const WORLD_HEIGHT = 24;
const SEA_LEVEL = 12;
const RENDER_DISTANCE = 3;

// Biomes
const BIOMES = {
    PLAINS: 'plains',
    FOREST: 'forest',
    DESERT: 'desert',
    SNOW: 'snow',
    MOUNTAINS: 'mountains'
};

function getBiome(x, z) {
    const temp = noise.octaveNoise(x * 0.01, z * 0.01, 2);
    const humidity = noise.octaveNoise(x * 0.008 + 1000, z * 0.008 + 1000, 2);
    const height = getHeightAt(x, z);
    
    if (height > 20) return BIOMES.MOUNTAINS;
    if (temp < 0.3) return BIOMES.SNOW;
    if (humidity < 0.3) return BIOMES.DESERT;
    if (humidity > 0.7) return BIOMES.FOREST;
    return BIOMES.PLAINS;
}

function getHeightAt(x, z) {
    // Simple terrain height using noise
    const baseHeight = noise.octaveNoise(x * 0.01, z * 0.01, 4) * 8 + SEA_LEVEL;
    const mountainNoise = noise.octaveNoise(x * 0.005, z * 0.005, 2) * 12;
    const detailNoise = noise.octaveNoise(x * 0.05, z * 0.05, 2) * 2;
    
    return Math.max(SEA_LEVEL - 2, baseHeight + mountainNoise + detailNoise);
}

// World class - simplified for blank world
class World {
    constructor() {
        this.chunks = new Map();
        this.loadedChunks = new Set();
        this.heightMap = new Map();
        
        console.log('🌍 Blank world initialized');
    }

    update(playerPosition) {
        const playerChunkX = Math.floor(playerPosition.x / CHUNK_SIZE);
        const playerChunkZ = Math.floor(playerPosition.z / CHUNK_SIZE);

        // Load chunks around player
        for (let x = playerChunkX - RENDER_DISTANCE; x <= playerChunkX + RENDER_DISTANCE; x++) {
            for (let z = playerChunkZ - RENDER_DISTANCE; z <= playerChunkZ + RENDER_DISTANCE; z++) {
                const chunkKey = `${x},${z}`;
                if (!this.loadedChunks.has(chunkKey)) {
                    this.generateChunk(x, z);
                    this.loadedChunks.add(chunkKey);
                }
            }
        }

        // Update biome display
        const currentBiome = getBiome(playerPosition.x, playerPosition.z);
        document.getElementById('biome').textContent = currentBiome.charAt(0).toUpperCase() + currentBiome.slice(1);
    }

    generateChunk(chunkX, chunkZ) {
        const chunkKey = `${chunkX},${chunkZ}`;
        
        if (this.chunks.has(chunkKey)) {
            return this.chunks.get(chunkKey);
        }

        console.log(`🗺️ Generating blank chunk (${chunkX}, ${chunkZ})`);

        const chunk = {
            x: chunkX,
            z: chunkZ,
            mesh: null,
            blocks: new Array(CHUNK_SIZE).fill(null).map(() => 
                new Array(WORLD_HEIGHT).fill(null).map(() => 
                    new Array(CHUNK_SIZE).fill(0)
                )
            )
        };

        // Generate terrain mesh
        this.generateTerrainMesh(chunk);
        
        this.chunks.set(chunkKey, chunk);
        return chunk;
    }

    generateTerrainMesh(chunk) {
        const geometry = new THREE.BufferGeometry();
        const vertices = [];
        const colors = [];
        const indices = [];
        let vertexIndex = 0;

        const startX = chunk.x * CHUNK_SIZE;
        const startZ = chunk.z * CHUNK_SIZE;

        // Generate terrain vertices
        for (let x = 0; x <= CHUNK_SIZE; x++) {
            for (let z = 0; z <= CHUNK_SIZE; z++) {
                const worldX = startX + x;
                const worldZ = startZ + z;
                const height = getHeightAt(worldX, worldZ);
                
                vertices.push(worldX, height, worldZ);
                
                // Color based on biome and height
                const biome = getBiome(worldX, worldZ);
                const color = this.getBiomeColor(biome, height);
                colors.push(color.r, color.g, color.b);
            }
        }

        // Generate faces
        for (let x = 0; x < CHUNK_SIZE; x++) {
            for (let z = 0; z < CHUNK_SIZE; z++) {
                const i = x * (CHUNK_SIZE + 1) + z;
                
                // Two triangles per quad
                indices.push(i, i + 1, i + CHUNK_SIZE + 1);
                indices.push(i + 1, i + CHUNK_SIZE + 2, i + CHUNK_SIZE + 1);
            }
        }

        geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
        geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));
        geometry.setIndex(indices);
        geometry.computeVertexNormals();

        const material = new THREE.MeshLambertMaterial({ 
            vertexColors: true,
            side: THREE.DoubleSide
        });
        
        chunk.mesh = new THREE.Mesh(geometry, material);
        chunk.mesh.receiveShadow = true;
        scene.add(chunk.mesh);
    }

    getBiomeColor(biome, height) {
        switch(biome) {
            case BIOMES.PLAINS:
                return { r: 0.4, g: 0.8, b: 0.2 }; // Green
            case BIOMES.FOREST:
                return { r: 0.2, g: 0.6, b: 0.2 }; // Dark green
            case BIOMES.DESERT:
                return { r: 0.9, g: 0.8, b: 0.4 }; // Sandy yellow
            case BIOMES.SNOW:
                return { r: 0.9, g: 0.9, b: 1.0 }; // White
            case BIOMES.MOUNTAINS:
                if (height > 18) {
                    return { r: 0.7, g: 0.7, b: 0.7 }; // Gray rock
                } else {
                    return { r: 0.5, g: 0.7, b: 0.3 }; // Mountain grass
                }
            default:
                return { r: 0.4, g: 0.8, b: 0.2 }; // Default green
        }
    }

    getHeightAt(x, z) {
        return getHeightAt(x, z);
    }
}
