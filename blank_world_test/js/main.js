// === BLANK WORLD MAIN LOOP ===
// Simple game initialization and main loop

// Global variables
let scene, camera, renderer;
let world, player;
let directionalLight, ambientLight;
let frameCount = 0;
let lastTime = 0;

// Initialize the game
async function init() {
    console.log('Initializing Blank World...');
    
    // Create scene
    scene = new THREE.Scene();
    scene.background = new THREE.Color(0x87CEEB); // Sky blue
    
    // Create camera
    camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    
    // Create renderer
    renderer = new THREE.WebGLRenderer({ antialias: false });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    document.getElementById('gameContainer').appendChild(renderer.domElement);
    
    // Create lighting
    ambientLight = new THREE.AmbientLight(0x404040, 0.4);
    scene.add(ambientLight);
    
    directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(50, 100, 50);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    directionalLight.shadow.camera.near = 0.5;
    directionalLight.shadow.camera.far = 500;
    directionalLight.shadow.camera.left = -100;
    directionalLight.shadow.camera.right = 100;
    directionalLight.shadow.camera.top = 100;
    directionalLight.shadow.camera.bottom = -100;
    scene.add(directionalLight);
    
    // Initialize world
    world = new World();
    
    // Initialize player
    player = new Player();
    
    // Hide loading screen
    document.getElementById('loading').style.display = 'none';
    
    console.log('✅ Blank world initialized successfully!');
    
    // Start game loop
    animate();
}

// Game loop
function animate() {
    requestAnimationFrame(animate);
    
    const currentTime = performance.now();
    const deltaTime = (currentTime - lastTime) / 1000;
    lastTime = currentTime;
    
    // Update game systems
    if (player) {
        player.update(deltaTime);
    }
    
    if (world) {
        world.update(player.position);
    }
    
    // Update FPS counter
    frameCount++;
    if (frameCount % 60 === 0) {
        const fps = Math.round(1 / deltaTime);
        document.getElementById('fps').textContent = fps;
    }
    
    // Render
    renderer.render(scene, camera);
}

// Handle window resize
window.addEventListener('resize', () => {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
});

// Start the game when page loads
window.addEventListener('load', init);
