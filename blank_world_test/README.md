# 🌍 Blank World Test

A minimal 3D world with just terrain and movement for testing purposes.

## 🎯 What's Included

- **Terrain Generation**: Procedural landscape with different biomes
- **Player Movement**: WASD + mouse controls
- **Camera System**: First-person view with mouse look
- **Biome System**: Plains, Forest, Desert, Snow, Mountains
- **Physics**: Basic gravity and ground collision

## 🚫 What's NOT Included

- No trees, buildings, or structures
- No enemies or NPCs
- No inventory system
- No block placement
- No items or objects
- Just pure terrain and movement

## 🚀 How to Run

### Option 1: Python Server (Recommended)
```bash
cd blank_world_test
python3 server.py
```
Then open your browser to `http://localhost:8080`

### Option 2: Direct File
Open `index.html` directly in your browser (may have CORS issues)

## 🎮 Controls

- **WASD**: Move around
- **R**: Run (hold for faster movement)
- **Space**: Jump
- **Mouse**: Look around (click to lock mouse)

## 🗺️ Features

- **Infinite Terrain**: World generates as you explore
- **Multiple Biomes**: Different colored terrain based on location
- **Smooth Movement**: Physics-based player movement
- **Real-time Info**: Position, biome, and FPS display

## 📁 File Structure

```
blank_world_test/
├── index.html          # Main HTML file
├── server.py           # Local test server
├── README.md           # This file
└── js/
    ├── main.js         # Game initialization
    ├── world.js        # Terrain generation
    ├── player.js       # Movement and camera
    └── noise.js        # Noise generation for terrain
```

## 🔧 Technical Details

- **Engine**: Three.js for 3D graphics
- **Terrain**: Chunk-based generation (16x16 blocks)
- **Biomes**: Noise-based biome selection
- **Rendering**: WebGL with basic lighting
- **Performance**: Optimized for smooth movement

Perfect for testing terrain generation and movement mechanics! 🎮
