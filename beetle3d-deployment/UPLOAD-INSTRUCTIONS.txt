BEETLE3D UPLOAD INSTRUCTIONS
============================

1. FRONTEND FILES (Upload to your web root directory):
   - Upload everything in the 'frontend/' folder to your public_html or web root
   - Make sure index.html is in the root directory
   - Ensure file permissions are 644 for files, 755 for directories

2. BACKEND FILES (Upload to server directory):
   - Upload everything in the 'backend/' folder to a server directory
   - Install Node.js dependencies: cd backend && npm install
   - Start the server: npm start

3. DATABASE SETUP:
   - Connect to MySQL: mysql -u beetle -p4SrTF6CYdFhKYhG
   - Run: CREATE DATABASE IF NOT EXISTS beetle;
   - Run: USE beetle;
   - Run: SOURCE database.sql;

4. CONFIGURATION:
   - Update backend/.env with your domain settings
   - Ensure ports 3001 and 3306 are open
   - Test the game at https://beetle3d.com

5. TROUBLESHOOTING:
   - Check browser console (F12) for errors
   - Check server logs for backend issues
   - Verify database connection works

Your game includes:
✅ Complete 3D Minecraft-style gameplay
✅ User authentication and database
✅ Save/load system
✅ Inventory and hotbar with drag & drop
✅ Block placement system
✅ Multiplayer-ready architecture

For detailed instructions, see deployment-instructions.md
