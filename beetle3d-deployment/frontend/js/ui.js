// === UI SYSTEM ===
// User interface updates and time management

// Game time system
let gameTime = 12 * 60; // Start at noon (12:00)
const DAY_LENGTH = 20 * 60; // 20 minutes = 1 day

function updateGameTime(deltaTime) {
    gameTime += deltaTime * 60; // 1 real second = 1 game minute
    if (gameTime >= DAY_LENGTH) {
        gameTime = 0;
    }
}

function formatTime(minutes) {
    const hours = Math.floor(minutes / 60) % 24;
    const mins = Math.floor(minutes % 60);
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
}

function updateLighting() {
    const hours = (gameTime / 60) % 24;
    let intensity;
    let color;
    
    if (hours >= 6 && hours <= 18) {
        // Day time
        intensity = 1.0;
        color = 0xffffff;
    } else if (hours >= 19 || hours <= 5) {
        // Night time
        intensity = 0.3;
        color = 0x4444ff;
    } else {
        // Transition periods
        const factor = hours > 18 ? (hours - 18) : (6 - hours);
        intensity = 1.0 - (factor * 0.7);
        color = hours > 18 ? 0x8888ff : 0xffaa88;
    }
    
    if (window.directionalLight) {
        window.directionalLight.intensity = intensity;
        window.directionalLight.color.setHex(color);
    }
    
    if (window.ambientLight) {
        window.ambientLight.intensity = intensity * 0.4;
    }
}

// UI update function
function updateUI(player, world, deltaTime) {
    // Update game time
    updateGameTime(deltaTime);
    updateLighting();
    
    // Update UI elements
    const fps = Math.round(1 / deltaTime);
    document.getElementById('fps').textContent = fps;
    
    document.getElementById('health').textContent = Math.floor(player.health);
    
    // Update stamina bar and text
    const displayStamina = Math.max(0, Math.min(100, Math.round(player.stamina)));
    const staminaPercent = displayStamina / 100;
    
    // Update stamina bar width
    const staminaBar = document.getElementById('staminaBar');
    staminaBar.style.width = `${staminaPercent * 100}%`;
    
    // Update stamina bar color based on level and running state
    if (player.isRunning && player.stamina > 5) {
        staminaBar.style.background = '#00ff00'; // Solid green when running
        staminaBar.style.boxShadow = '0 0 8px rgba(0, 255, 0, 0.6)'; // Glowing effect
    } else if (player.stamina < 20) {
        staminaBar.style.background = '#ff0000'; // Red when low
        staminaBar.style.boxShadow = '0 0 8px rgba(255, 0, 0, 0.6)'; // Red glow
    } else if (player.stamina < 50) {
        staminaBar.style.background = 'linear-gradient(to right, #ffff00, #ff8800)'; // Yellow to orange
        staminaBar.style.boxShadow = '0 0 6px rgba(255, 255, 0, 0.4)'; // Yellow glow
    } else {
        staminaBar.style.background = 'linear-gradient(to right, #00ff00, #88ff00)'; // Green gradient
        staminaBar.style.boxShadow = '0 0 4px rgba(0, 255, 0, 0.3)'; // Subtle green glow
    }
    
    // Update stamina text
    document.getElementById('staminaText').textContent = displayStamina;
    
    const biome = getBiome(Math.floor(player.position.x), Math.floor(player.position.z));
    document.getElementById('biome').textContent = biome.charAt(0).toUpperCase() + biome.slice(1);
    
    document.getElementById('orcs').textContent = world.orcs.length;
    document.getElementById('time').textContent = formatTime(gameTime);
}
