// === WORLD GENERATION ===
// Terrain generation, chunks, and biomes

// Game constants
const CHUNK_SIZE = 16;
const WORLD_HEIGHT = 24;
const SEA_LEVEL = 12;
const RENDER_DISTANCE = 3;

// Biomes
const BIOMES = {
    PLAINS: 'plains',
    FOREST: 'forest',
    DESERT: 'desert',
    SNOW: 'snow',
    MOUNTAINS: 'mountains'
};

function getBiome(x, z) {
    const temp = noise.octaveNoise(x * 0.01, z * 0.01, 2);
    const humidity = noise.octaveNoise(x * 0.008 + 1000, z * 0.008 + 1000, 2);
    const height = getHeightAt(x, z);
    
    if (height > 20) return BIOMES.MOUNTAINS;
    if (temp < 0.3) return BIOMES.SNOW;
    if (humidity < 0.3) return BIOMES.DESERT;
    if (humidity > 0.7) return BIOMES.FOREST;
    return BIOMES.PLAINS;
}

function getHeightAt(x, z) {
    let height = noise.octaveNoise(x * 0.005, z * 0.005, 4) * 15;
    height += noise.octaveNoise(x * 0.02, z * 0.02, 2) * 5;
    return Math.floor(height + SEA_LEVEL);
}

function getBlockType(x, y, z, biome) {
    const height = getHeightAt(x, z);
    
    // Water
    if (y <= SEA_LEVEL && y > height) {
        return BLOCKS.WATER;
    }
    
    // Air
    if (y > height) return BLOCKS.AIR;
    
    // Underground caves (simple 3D noise)
    if (y < height - 2 && y > 2) {
        const caveNoise1 = noise.octaveNoise(x * 0.03, y * 0.03, 2);
        const caveNoise2 = noise.octaveNoise(z * 0.03, y * 0.03, 2);
        const caveNoise3 = noise.octaveNoise(x * 0.02, z * 0.02, 2);
        
        // Combine noise for 3D cave system
        const caveValue = (caveNoise1 + caveNoise2 + caveNoise3) / 3;
        
        if (caveValue > 0.6) {
            return BLOCKS.AIR; // Cave space
        }
    }
    
    // Surface
    if (y === height) {
        switch(biome) {
            case BIOMES.DESERT: return BLOCKS.SAND;
            case BIOMES.SNOW: return BLOCKS.SNOW;
            default: return BLOCKS.GRASS;
        }
    }
    
    // Subsurface
    if (y > height - 3) {
        return biome === BIOMES.DESERT ? BLOCKS.SAND : BLOCKS.DIRT;
    }
    
    return BLOCKS.STONE;
}

// Chunk class - simplified for performance
class Chunk {
    constructor(chunkX, chunkZ) {
        this.x = chunkX;
        this.z = chunkZ;
        this.blocks = new Array(CHUNK_SIZE * CHUNK_SIZE * WORLD_HEIGHT);
        this.mesh = null;
        this.biome = getBiome(chunkX * CHUNK_SIZE + 8, chunkZ * CHUNK_SIZE + 8);
        
        this.generate();
    }
    
    generate() {
        const startX = this.x * CHUNK_SIZE;
        const startZ = this.z * CHUNK_SIZE;
        
        for (let x = 0; x < CHUNK_SIZE; x++) {
            for (let z = 0; z < CHUNK_SIZE; z++) {
                const worldX = startX + x;
                const worldZ = startZ + z;
                const biome = getBiome(worldX, worldZ);
                
                for (let y = 0; y < WORLD_HEIGHT; y++) {
                    const blockType = getBlockType(worldX, y, worldZ, biome);
                    this.setBlock(x, y, z, blockType);
                }
            }
        }
        
        // Add some trees (simplified)
        if (this.biome === BIOMES.FOREST || this.biome === BIOMES.PLAINS) {
            this.generateTrees();
        }
    }
    
    generateTrees() {
        for (let x = 2; x < CHUNK_SIZE - 2; x += 4) {
            for (let z = 2; z < CHUNK_SIZE - 2; z += 4) {
                const worldX = this.x * CHUNK_SIZE + x;
                const worldZ = this.z * CHUNK_SIZE + z;
                const treeNoise = noise.noise(worldX * 0.1, worldZ * 0.1);
                
                if (treeNoise > 0.6) {
                    const height = getHeightAt(worldX, worldZ);
                    if (height > SEA_LEVEL) {
                        this.generateTree(x, height + 1, z);
                    }
                }
            }
        }
    }
    
    generateTree(x, y, z) {
        const treeHeight = 4 + Math.floor(Math.random() * 3);
        
        // Generate trunk
        for (let i = 0; i < treeHeight; i++) {
            if (y + i < WORLD_HEIGHT) {
                this.setBlock(x, y + i, z, BLOCKS.WOOD);
            }
        }
        
        // Generate beautiful leafy canopy
        const leafY = y + treeHeight;
        for (let dx = -2; dx <= 2; dx++) {
            for (let dz = -2; dz <= 2; dz++) {
                for (let dy = -1; dy <= 1; dy++) {
                    // Create a nice rounded tree shape
                    if (Math.abs(dx) + Math.abs(dz) + Math.abs(dy) <= 3) {
                        const leafX = x + dx;
                        const leafZ = z + dz;
                        const leafYPos = leafY + dy;
                        
                        if (leafX >= 0 && leafX < CHUNK_SIZE && 
                            leafZ >= 0 && leafZ < CHUNK_SIZE && 
                            leafYPos < WORLD_HEIGHT && leafYPos > 0) {
                            
                            if (this.getBlock(leafX, leafYPos, leafZ) === BLOCKS.AIR) {
                                this.setBlock(leafX, leafYPos, leafZ, BLOCKS.LEAVES);
                            }
                        }
                    }
                }
            }
        }
        
        // Add extra leaves on top for better shape
        if (leafY + 2 < WORLD_HEIGHT) {
            this.setBlock(x, leafY + 2, z, BLOCKS.LEAVES);
        }
    }
    
    setBlock(x, y, z, blockType) {
        const index = x + z * CHUNK_SIZE + y * CHUNK_SIZE * CHUNK_SIZE;
        this.blocks[index] = blockType;
    }
    
    getBlock(x, y, z) {
        if (x < 0 || x >= CHUNK_SIZE || y < 0 || y >= WORLD_HEIGHT || z < 0 || z >= CHUNK_SIZE) {
            return BLOCKS.AIR;
        }
        const index = x + z * CHUNK_SIZE + y * CHUNK_SIZE * CHUNK_SIZE;
        return this.blocks[index] || BLOCKS.AIR;
    }
    
    createMesh() {
        if (this.mesh) {
            scene.remove(this.mesh);
            if (this.mesh.geometry) this.mesh.geometry.dispose();
        }
        
        const geometry = new THREE.BufferGeometry();
        const vertices = [];
        const colors = [];
        const indices = [];
        let vertexIndex = 0;
        
        const blockColors = {
            [BLOCKS.GRASS]: [0.3, 0.7, 0.3],
            [BLOCKS.DIRT]: [0.5, 0.3, 0.1],
            [BLOCKS.STONE]: [0.5, 0.5, 0.5],
            [BLOCKS.SAND]: [0.9, 0.7, 0.4],
            [BLOCKS.SNOW]: [1, 1, 1],
            [BLOCKS.WATER]: [0, 0.5, 0.8],
            [BLOCKS.WOOD]: [0.4, 0.2, 0.1],
            [BLOCKS.LEAVES]: [0.1, 0.5, 0.1]
        };
        
        for (let x = 0; x < CHUNK_SIZE; x++) {
            for (let y = 0; y < WORLD_HEIGHT; y++) {
                for (let z = 0; z < CHUNK_SIZE; z++) {
                    const blockType = this.getBlock(x, y, z);
                    
                    if (blockType === BLOCKS.AIR) continue;
                    
                    const worldX = this.x * CHUNK_SIZE + x;
                    const worldZ = this.z * CHUNK_SIZE + z;
                    const color = blockColors[blockType] || [0.5, 0.5, 0.5];
                    
                    // Check each face
                    const faces = [
                        { dir: [0, 1, 0], verts: [[0,1,1], [1,1,1], [1,1,0], [0,1,0]] }, // top
                        { dir: [0, -1, 0], verts: [[0,0,0], [1,0,0], [1,0,1], [0,0,1]] }, // bottom
                        { dir: [1, 0, 0], verts: [[1,0,0], [1,1,0], [1,1,1], [1,0,1]] }, // right
                        { dir: [-1, 0, 0], verts: [[0,0,1], [0,1,1], [0,1,0], [0,0,0]] }, // left
                        { dir: [0, 0, 1], verts: [[1,0,1], [1,1,1], [0,1,1], [0,0,1]] }, // front
                        { dir: [0, 0, -1], verts: [[0,0,0], [0,1,0], [1,1,0], [1,0,0]] }  // back
                    ];
                    
                    faces.forEach(face => {
                        const [dx, dy, dz] = face.dir;
                        const nx = x + dx;
                        const ny = y + dy;
                        const nz = z + dz;
                        
                        let neighborType = BLOCKS.AIR;
                        if (nx >= 0 && nx < CHUNK_SIZE && ny >= 0 && ny < WORLD_HEIGHT && nz >= 0 && nz < CHUNK_SIZE) {
                            neighborType = this.getBlock(nx, ny, nz);
                        }
                        
                        if (neighborType === BLOCKS.AIR || (blockType !== BLOCKS.WATER && neighborType === BLOCKS.WATER)) {
                            face.verts.forEach(vert => {
                                vertices.push(
                                    worldX + vert[0],
                                    y + vert[1],
                                    worldZ + vert[2]
                                );
                                colors.push(color[0], color[1], color[2]);
                            });
                            
                            indices.push(
                                vertexIndex, vertexIndex + 1, vertexIndex + 2,
                                vertexIndex, vertexIndex + 2, vertexIndex + 3
                            );
                            vertexIndex += 4;
                        }
                    });
                }
            }
        }
        
        if (vertices.length > 0) {
            geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
            geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));
            geometry.setIndex(indices);
            geometry.computeVertexNormals();
            
            const material = new THREE.MeshLambertMaterial({ 
                vertexColors: true,
                side: THREE.DoubleSide
            });
            
            this.mesh = new THREE.Mesh(geometry, material);
            scene.add(this.mesh);
        }
    }
}

// World class - simplified
class World {
    constructor() {
        this.chunks = new Map();
        this.orcs = [];
        this.maxOrcs = 5; // Reduced for performance
        this.orcSpawnTimer = 0;
    }

    getChunkKey(chunkX, chunkZ) {
        return `${chunkX},${chunkZ}`;
    }

    getChunk(chunkX, chunkZ) {
        const key = this.getChunkKey(chunkX, chunkZ);
        if (!this.chunks.has(key)) {
            this.chunks.set(key, new Chunk(chunkX, chunkZ));
        }
        return this.chunks.get(key);
    }

    updateChunks(playerX, playerZ) {
        const playerChunkX = Math.floor(playerX / CHUNK_SIZE);
        const playerChunkZ = Math.floor(playerZ / CHUNK_SIZE);

        const neededChunks = new Set();

        for (let x = playerChunkX - RENDER_DISTANCE; x <= playerChunkX + RENDER_DISTANCE; x++) {
            for (let z = playerChunkZ - RENDER_DISTANCE; z <= playerChunkZ + RENDER_DISTANCE; z++) {
                const key = this.getChunkKey(x, z);
                neededChunks.add(key);

                const chunk = this.getChunk(x, z);
                if (!chunk.mesh) {
                    chunk.createMesh();
                }
            }
        }

        // Remove distant chunks
        for (const [key, chunk] of this.chunks) {
            if (!neededChunks.has(key)) {
                if (chunk.mesh) {
                    scene.remove(chunk.mesh);
                    if (chunk.mesh.geometry) chunk.mesh.geometry.dispose();
                }
                this.chunks.delete(key);
            }
        }
    }

    updateEnemies(deltaTime, playerPosition) {
        // Update existing orcs
        this.orcs = this.orcs.filter(orc => {
            if (orc.health <= 0) {
                return false; // Remove dead orcs
            }
            orc.update(deltaTime, playerPosition);
            return true;
        });

        // Spawn new orcs occasionally
        this.orcSpawnTimer += deltaTime;
        if (this.orcSpawnTimer > 10 && this.orcs.length < this.maxOrcs) {
            this.spawnOrc(playerPosition);
            this.orcSpawnTimer = 0;
        }
    }

    spawnOrc(playerPosition) {
        // Spawn orc at a random location around the player
        const angle = Math.random() * Math.PI * 2;
        const distance = 20 + Math.random() * 20;

        const spawnX = playerPosition.x + Math.cos(angle) * distance;
        const spawnZ = playerPosition.z + Math.sin(angle) * distance;

        // Make sure spawn location is above ground
        const groundHeight = getHeightAt(spawnX, spawnZ);
        if (groundHeight > SEA_LEVEL) {
            const orc = new Orc(spawnX, spawnZ);
            this.orcs.push(orc);
            console.log(`Spawned orc at (${Math.floor(spawnX)}, ${Math.floor(spawnZ)})`);
        }
    }
}
