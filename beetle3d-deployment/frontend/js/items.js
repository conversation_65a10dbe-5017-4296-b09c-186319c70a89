// === ITEM DEFINITIONS ===
// All item types and their properties

// Block types
const BLOCKS = {
    AIR: 0,
    GRASS: 1,
    DIRT: 2,
    STONE: 3,
    SAND: 4,
    SNOW: 5,
    WATER: 6,
    WOOD: 7,
    LEAVES: 8
};

// Item types and definitions
const ITEM_TYPES = {
    WEAPON: 'weapon',
    BLOCK: 'block',
    TOOL: 'tool',
    CONSUMABLE: 'consumable'
};

const ITEMS = {
    // Weapons
    FIST: {
        id: 'fist',
        name: 'Fist',
        type: ITEM_TYPES.WEAPON,
        iconClass: 'fist',
        damage: 25,
        description: 'Your bare hands. Better than nothing!'
    },
    SWORD: {
        id: 'sword',
        name: 'Iron Sword',
        type: ITEM_TYPES.WEAPON,
        iconClass: 'sword',
        damage: 50,
        description: 'A sharp iron sword. Deals good damage to enemies.'
    },
    BOW: {
        id: 'bow',
        name: '<PERSON><PERSON> Bow',
        type: ITEM_TYPES.WEAPON,
        iconClass: 'bow',
        damage: 40,
        description: 'A ranged weapon. Good for keeping distance.'
    },
    // Blocks
    GRASS_BLOCK: {
        id: 'grass_block',
        name: 'Grass Block',
        type: ITEM_TYPES.BLOCK,
        iconClass: 'grass_block',
        blockType: BLOCKS.GRASS,
        description: 'A grassy dirt block. Can be placed in the world.'
    },
    DIRT_BLOCK: {
        id: 'dirt_block',
        name: 'Dirt Block',
        type: ITEM_TYPES.BLOCK,
        iconClass: 'dirt_block',
        blockType: BLOCKS.DIRT,
        description: 'Basic dirt block. Foundation for building.'
    },
    STONE_BLOCK: {
        id: 'stone_block',
        name: 'Stone Block',
        type: ITEM_TYPES.BLOCK,
        iconClass: 'stone_block',
        blockType: BLOCKS.STONE,
        description: 'Solid stone block. Durable building material.'
    },
    WOOD_BLOCK: {
        id: 'wood_block',
        name: 'Wood Block',
        type: ITEM_TYPES.BLOCK,
        iconClass: 'wood_block',
        blockType: BLOCKS.WOOD,
        description: 'Wooden plank block. Good for construction.'
    },
    // Tools
    PICKAXE: {
        id: 'pickaxe',
        name: 'Stone Pickaxe',
        type: ITEM_TYPES.TOOL,
        iconClass: 'pickaxe',
        description: 'Used for mining stone and ore blocks.'
    },
    // Consumables
    HEALTH_POTION: {
        id: 'health_potion',
        name: 'Health Potion',
        type: ITEM_TYPES.CONSUMABLE,
        iconClass: 'health_potion',
        healAmount: 50,
        description: 'Restores 50 health points when consumed.'
    }
};
