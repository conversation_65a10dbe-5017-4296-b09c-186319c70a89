const express = require('express');
const mysql = require('mysql2/promise');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const cors = require('cors');
const { Server } = require('socket.io');
const http = require('http');
require('dotenv').config();

const app = express();
const server = http.createServer(app);

// Socket.IO setup for real-time multiplayer
const io = new Server(server, {
    cors: {
        origin: process.env.CLIENT_URL || "http://localhost:8000",
        methods: ["GET", "POST"]
    }
});

// Middleware
app.use(cors({
    origin: process.env.CLIENT_URL || "http://localhost:8000",
    credentials: true
}));
app.use(express.json());
app.use(express.static('../')); // Serve game files

// Database connection
const dbConfig = {
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0
};

const pool = mysql.createPool(dbConfig);

// Test database connection
async function testConnection() {
    try {
        const connection = await pool.getConnection();
        console.log('✅ Database connected successfully');
        connection.release();
    } catch (error) {
        console.error('❌ Database connection failed:', error.message);
        process.exit(1);
    }
}

// JWT middleware for protected routes
const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ error: 'Access token required' });
    }

    jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
        if (err) {
            return res.status(403).json({ error: 'Invalid token' });
        }
        req.user = user;
        next();
    });
};

// === AUTHENTICATION ROUTES ===

// Register new user
app.post('/api/register', async (req, res) => {
    try {
        const { username, password, email } = req.body;

        // Validation
        if (!username || !password) {
            return res.status(400).json({ error: 'Username and password required' });
        }

        if (username.length < 3) {
            return res.status(400).json({ error: 'Username must be at least 3 characters' });
        }

        if (password.length < 4) {
            return res.status(400).json({ error: 'Password must be at least 4 characters' });
        }

        // Check if user exists
        const [existing] = await pool.execute(
            'SELECT id FROM users WHERE username = ? OR email = ?',
            [username, email || '']
        );

        if (existing.length > 0) {
            return res.status(400).json({ error: 'Username or email already exists' });
        }

        // Hash password
        const saltRounds = 10;
        const passwordHash = await bcrypt.hash(password, saltRounds);

        // Create user
        const [result] = await pool.execute(
            'INSERT INTO users (username, email, password_hash) VALUES (?, ?, ?)',
            [username, email || null, passwordHash]
        );

        // Create default player stats
        await pool.execute(
            'INSERT INTO player_stats (user_id) VALUES (?)',
            [result.insertId]
        );

        // Generate JWT token
        const token = jwt.sign(
            { userId: result.insertId, username },
            process.env.JWT_SECRET,
            { expiresIn: '7d' }
        );

        res.status(201).json({
            success: true,
            message: 'User created successfully',
            token,
            user: {
                id: result.insertId,
                username,
                email: email || null
            }
        });

    } catch (error) {
        console.error('Registration error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Login user
app.post('/api/login', async (req, res) => {
    try {
        const { username, password } = req.body;

        if (!username || !password) {
            return res.status(400).json({ error: 'Username and password required' });
        }

        // Find user
        const [users] = await pool.execute(
            'SELECT id, username, email, password_hash FROM users WHERE username = ?',
            [username]
        );

        if (users.length === 0) {
            return res.status(401).json({ error: 'Invalid credentials' });
        }

        const user = users[0];

        // Verify password
        const validPassword = await bcrypt.compare(password, user.password_hash);
        if (!validPassword) {
            return res.status(401).json({ error: 'Invalid credentials' });
        }

        // Update last login and online status
        await pool.execute(
            'UPDATE users SET last_login = NOW(), is_online = TRUE WHERE id = ?',
            [user.id]
        );

        // Generate JWT token
        const token = jwt.sign(
            { userId: user.id, username: user.username },
            process.env.JWT_SECRET,
            { expiresIn: '7d' }
        );

        res.json({
            success: true,
            message: 'Login successful',
            token,
            user: {
                id: user.id,
                username: user.username,
                email: user.email
            }
        });

    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Logout user
app.post('/api/logout', authenticateToken, async (req, res) => {
    try {
        await pool.execute(
            'UPDATE users SET is_online = FALSE WHERE id = ?',
            [req.user.userId]
        );

        res.json({ success: true, message: 'Logged out successfully' });
    } catch (error) {
        console.error('Logout error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// === GAME SAVE ROUTES ===

// Get user's game save
app.get('/api/save', authenticateToken, async (req, res) => {
    try {
        const [saves] = await pool.execute(
            'SELECT * FROM game_saves WHERE user_id = ? ORDER BY updated_at DESC LIMIT 1',
            [req.user.userId]
        );

        if (saves.length === 0) {
            return res.json({ success: true, save: null });
        }

        const save = saves[0];
        res.json({
            success: true,
            save: {
                position: {
                    x: save.position_x,
                    y: save.position_y,
                    z: save.position_z
                },
                health: save.health,
                stamina: save.stamina,
                inventory: save.inventory_data,
                hotbar: save.hotbar_data,
                kills: save.kills,
                score: save.score,
                playTime: save.play_time,
                lastSaved: save.updated_at
            }
        });

    } catch (error) {
        console.error('Load save error:', error);
        res.status(500).json({ error: 'Failed to load save' });
    }
});

// Save user's game progress
app.post('/api/save', authenticateToken, async (req, res) => {
    try {
        const {
            position,
            health,
            stamina,
            inventory,
            hotbar,
            kills,
            score,
            playTime
        } = req.body;

        // Insert or update save
        await pool.execute(`
            INSERT INTO game_saves (
                user_id, position_x, position_y, position_z,
                health, stamina, inventory_data, hotbar_data,
                kills, score, play_time
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
                position_x = VALUES(position_x),
                position_y = VALUES(position_y),
                position_z = VALUES(position_z),
                health = VALUES(health),
                stamina = VALUES(stamina),
                inventory_data = VALUES(inventory_data),
                hotbar_data = VALUES(hotbar_data),
                kills = VALUES(kills),
                score = VALUES(score),
                play_time = VALUES(play_time),
                updated_at = NOW()
        `, [
            req.user.userId,
            position.x, position.y, position.z,
            health, stamina,
            JSON.stringify(inventory),
            JSON.stringify(hotbar),
            kills, score, playTime
        ]);

        res.json({ success: true, message: 'Game saved successfully' });

    } catch (error) {
        console.error('Save game error:', error);
        res.status(500).json({ error: 'Failed to save game' });
    }
});

// Start server
const PORT = process.env.PORT || 3001;

async function startServer() {
    await testConnection();
    
    server.listen(PORT, () => {
        console.log(`🚀 Beetle3D Server running on port ${PORT}`);
        console.log(`🌐 Game URL: http://localhost:${PORT}`);
        console.log(`🎮 API URL: http://localhost:${PORT}/api`);
    });
}

startServer().catch(console.error);
