#!/bin/bash

echo "🚀 Setting up Beetle3D Server..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    echo "Visit: https://nodejs.org/"
    exit 1
fi

# Check if MySQL is installed
if ! command -v mysql &> /dev/null; then
    echo "❌ MySQL is not installed. Please install MySQL first."
    echo "Visit: https://dev.mysql.com/downloads/"
    exit 1
fi

echo "✅ Node.js version: $(node --version)"
echo "✅ npm version: $(npm --version)"

# Navigate to server directory
cd server

# Install dependencies
echo "📦 Installing server dependencies..."
npm install

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please check server/.env file."
    exit 1
fi

echo "✅ .env file found"

# Test database connection
echo "🔍 Testing database connection..."
echo "Please make sure your MySQL server is running and the database 'beetle' exists."
echo ""
echo "To create the database, run:"
echo "mysql -u beetle -p"
echo "CREATE DATABASE IF NOT EXISTS beetle;"
echo "USE beetle;"
echo "SOURCE database.sql;"
echo ""

# Start server
echo "🚀 Starting Beetle3D server..."
echo "Server will run on http://localhost:3001"
echo "Game will be available at http://localhost:3001"
echo ""

npm start
