BEETLE3D FILE STRUCTURE
=======================

frontend/                    # Upload to web root (public_html)
├── index.html              # Main game file
├── js/                     # Game scripts
│   ├── auth.js            # Authentication
│   ├── saveload.js        # Save/load system
│   ├── inventory.js       # Inventory & hotbar
│   ├── player.js          # Player controls
│   ├── world.js           # World generation
│   ├── enemies.js         # Enemy system
│   ├── items.js           # Items & blocks
│   ├── characters.js      # Character models
│   ├── ui.js              # User interface
│   ├── noise.js           # Terrain generation
│   └── main.js            # Main game loop
├── images/                # Game assets
│   └── mycharacter.png    # Custom character
└── libs/                  # Libraries
    └── three.min.js       # 3D graphics

backend/                    # Upload to server directory
├── package.json           # Dependencies
├── server.js             # API server
├── database.sql          # Database schema
├── .env                  # Configuration
└── setup-server.sh       # Setup script

DEPLOYMENT STEPS:
1. Upload frontend/ contents to web root
2. Upload backend/ to server directory
3. Setup database with database.sql
4. Install Node.js dependencies
5. Start server and test
