<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minecraft Adventure - Modular</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #87CEEB;
            overflow: hidden;
            font-family: 'Courier New', monospace;
        }
        
        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #ui {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            z-index: 100;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }
        
        #crosshair {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            pointer-events: none;
            z-index: 100;
        }
        
        #crosshair::before,
        #crosshair::after {
            content: '';
            position: absolute;
            background: white;
            box-shadow: 0 0 2px rgba(0,0,0,0.8);
        }
        
        #crosshair::before {
            width: 2px;
            height: 20px;
            left: 9px;
            top: 0;
        }
        
        #crosshair::after {
            width: 20px;
            height: 2px;
            left: 0;
            top: 9px;
        }
        
        #instructions {
            position: absolute;
            bottom: 10px;
            left: 10px;
            color: white;
            font-size: 12px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }

        #inventory {
            position: absolute;
            top: 10px;
            right: 10px;
            color: white;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            z-index: 100;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }

        #inventoryPanel {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #8B4513;
            border: 4px solid #A0522D;
            border-style: outset;
            padding: 16px;
            z-index: 300;
            display: none;
            color: white;
            font-family: 'Courier New', monospace;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
            box-shadow:
                inset 2px 2px 0px #D2B48C,
                inset -2px -2px 0px #654321,
                4px 4px 8px rgba(0,0,0,0.5);
        }

        #inventoryContent {
            display: flex;
            gap: 20px;
            align-items: flex-start;
        }

        #inventoryGrid {
            display: grid;
            grid-template-columns: repeat(5, 64px);
            grid-template-rows: repeat(4, 64px);
            gap: 2px;
            background: #654321;
            padding: 8px;
            border: 2px solid #A0522D;
            border-style: inset;
        }

        #characterPreview {
            width: 150px;
            height: 200px;
            background: #654321;
            border: 2px solid #A0522D;
            border-style: inset;
            padding: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        #characterPreviewTitle {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
            text-align: center;
        }

        #characterPreviewCanvas {
            width: 120px;
            height: 150px;
            border: 1px solid #A0522D;
            background: #2a2a2a;
        }

        /* Hotbar Styles */
        #hotbar {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 2px;
            background: #654321;
            padding: 8px;
            border: 2px solid #A0522D;
            border-style: outset;
            border-radius: 8px;
            z-index: 200;
            box-shadow:
                inset 2px 2px 0px #D2B48C,
                inset -2px -2px 0px #4A4A4A,
                4px 4px 8px rgba(0,0,0,0.5);
        }

        .hotbar-slot {
            width: 64px;
            height: 64px;
            background: #8B4513;
            border: 2px solid #A0522D;
            border-style: inset;
            position: relative;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .hotbar-slot:hover {
            background: #A0522D;
            border-color: #D2B48C;
        }

        .hotbar-slot.selected {
            background: #D2B48C;
            border-color: #FFD700;
            border-style: outset;
            box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }

        .hotbar-slot .item-icon-container {
            position: relative;
            z-index: 10;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .hotbar-slot .item-icon {
            width: 32px;
            height: 32px;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
            position: relative;
            z-index: 10;
            pointer-events: none;
        }

        .hotbar-slot .item-count {
            position: absolute;
            bottom: 2px;
            right: 4px;
            color: white;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            font-weight: bold;
            text-shadow: 1px 1px 1px rgba(0,0,0,0.8);
            pointer-events: none;
            z-index: 15;
        }

        .hotbar-number {
            position: absolute;
            top: -2px;
            left: 2px;
            color: #FFD700;
            font-family: 'Courier New', monospace;
            font-size: 10px;
            font-weight: bold;
            text-shadow: 1px 1px 1px rgba(0,0,0,0.8);
            pointer-events: none;
            z-index: 15;
        }

        /* Inventory Hotbar (in inventory panel) */
        #inventoryHotbar {
            display: grid;
            grid-template-columns: repeat(6, 64px);
            gap: 2px;
            margin-top: 16px;
            background: #654321;
            padding: 8px;
            border: 2px solid #A0522D;
            border-style: inset;
        }

        .inventory-hotbar-slot {
            width: 64px;
            height: 64px;
            background: #8B4513;
            border: 2px solid #A0522D;
            border-style: inset;
            position: relative;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .inventory-hotbar-slot:hover {
            background: #A0522D;
            border-color: #D2B48C;
        }

        .inventory-hotbar-slot.selected {
            background: #D2B48C;
            border-color: #FFD700;
            border-style: outset;
        }

        .inventory-hotbar-slot .item-icon-container {
            position: relative;
            z-index: 10;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .inventory-hotbar-slot .item-icon {
            width: 32px;
            height: 32px;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
            position: relative;
            z-index: 10;
            pointer-events: none;
        }

        .inventory-hotbar-slot .item-count {
            position: absolute;
            bottom: 2px;
            right: 4px;
            color: white;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            font-weight: bold;
            text-shadow: 1px 1px 1px rgba(0,0,0,0.8);
            pointer-events: none;
            z-index: 15;
        }

        /* Drag and Drop Styles */
        .inventory-slot.dragging,
        .hotbar-slot.dragging,
        .inventory-hotbar-slot.dragging {
            opacity: 0.7;
            transform: scale(0.95);
            border-color: #FFD700;
            box-shadow: inset 0 0 0 2px rgba(255, 215, 0, 0.6);
        }

        .hotbar-slot.drag-over,
        .inventory-hotbar-slot.drag-over {
            background: #FFD700;
            border-color: #FFA500;
            border-style: outset;
            box-shadow: inset 0 0 0 3px rgba(255, 165, 0, 0.8);
            transform: scale(1.02);
        }

        .inventory-slot.drag-over {
            background: #A0522D;
            border-color: #FFD700;
            border-style: outset;
            box-shadow: 0 0 15px rgba(255, 215, 0, 0.6);
        }

        /* Drag cursor styles */
        .inventory-slot[draggable="true"]:hover,
        .hotbar-slot[draggable="true"]:hover,
        .inventory-hotbar-slot[draggable="true"]:hover {
            cursor: grab;
        }

        .inventory-slot.dragging,
        .hotbar-slot.dragging,
        .inventory-hotbar-slot.dragging {
            cursor: grabbing;
        }

        .inventory-slot {
            width: 64px;
            height: 64px;
            border: 2px solid #A0522D;
            border-style: inset;
            background: #8B4513;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            position: relative;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
        }

        .inventory-slot:hover {
            background: #A0522D;
            border-color: #D2B48C;
        }

        .inventory-slot.selected {
            border: 2px solid #ffff00;
            border-style: outset;
            background: #B8860B;
            box-shadow: 
                inset 1px 1px 0px #ffff88,
                inset -1px -1px 0px #888800;
        }

        .inventory-slot.has-item {
            background: #A0522D;
            border-style: outset;
        }

        .item-icon {
            width: 32px;
            height: 32px;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .item-count {
            position: absolute;
            bottom: 4px;
            right: 6px;
            font-size: 12px;
            color: white;
            text-shadow: 1px 1px 0px rgba(0,0,0,1);
            font-weight: bold;
            background: rgba(0,0,0,0.7);
            padding: 1px 3px;
            border: 1px solid #666;
        }

        #inventoryInfo {
            text-align: center;
            margin-bottom: 12px;
            font-size: 16px;
            font-weight: bold;
            text-shadow: 2px 2px 0px rgba(0,0,0,1);
        }

        #selectedItemInfo {
            min-height: 48px;
            padding: 12px;
            background: #654321;
            border: 2px solid #A0522D;
            border-style: inset;
            margin-bottom: 12px;
            font-size: 14px;
            box-shadow: inset 1px 1px 2px rgba(0,0,0,0.5);
        }

        .inventory-controls {
            text-align: center;
            font-size: 12px;
            color: #F5DEB3;
            text-shadow: 1px 1px 0px rgba(0,0,0,1);
            background: #8B4513;
            padding: 8px;
            border: 1px solid #A0522D;
            border-style: inset;
        }

        /* Rendered 3D icons */
        .item-icon.rendered-icon {
            width: 56px !important;
            height: 56px !important;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
            border: none;
            background: none;
            margin: 4px;
        }

        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 24px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            z-index: 200;
        }

        /* Character Creation Modal */
        #characterCreationModal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Courier New', monospace;
        }

        .character-creation-panel {
            background: linear-gradient(135deg, #8B4513, #A0522D);
            border: 4px solid #D2B48C;
            border-radius: 10px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            color: white;
            text-align: center;
            box-shadow: 0 0 20px rgba(0,0,0,0.8);
        }

        .character-creation-panel h1 {
            color: #FFD700;
            font-size: 28px;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }

        .character-creation-panel h2 {
            color: #F5DEB3;
            font-size: 20px;
            margin: 20px 0 10px 0;
        }

        .character-creation-panel input[type="text"] {
            width: 80%;
            padding: 10px;
            font-size: 16px;
            border: 2px solid #654321;
            border-radius: 5px;
            background: #F5DEB3;
            color: #654321;
            font-family: 'Courier New', monospace;
        }

        .character-creation-panel .class-selection,
        .character-creation-panel .gender-selection {
            display: flex;
            justify-content: space-around;
            margin: 15px 0;
        }

        .character-creation-panel .class-option,
        .character-creation-panel .gender-option {
            background: #654321;
            border: 2px solid #8B4513;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }

        .character-creation-panel .class-option:hover,
        .character-creation-panel .gender-option:hover {
            background: #8B4513;
            border-color: #D2B48C;
            transform: scale(1.05);
        }

        .character-creation-panel .class-option.selected,
        .character-creation-panel .gender-option.selected {
            background: #B8860B;
            border-color: #FFD700;
            box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }

        .character-creation-panel .start-button {
            background: #228B22;
            border: 3px solid #32CD32;
            color: white;
            padding: 15px 30px;
            font-size: 18px;
            border-radius: 8px;
            cursor: pointer;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            transition: all 0.3s;
        }

        .character-creation-panel .start-button:hover {
            background: #32CD32;
            transform: scale(1.05);
        }

        .character-creation-panel .start-button:disabled {
            background: #666;
            border-color: #999;
            cursor: not-allowed;
            transform: none;
        }

        /* Story UI Panel */
        #storyPanel {
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            background: rgba(139, 69, 19, 0.95);
            border: 3px solid #D2B48C;
            border-radius: 8px;
            padding: 15px;
            color: white;
            font-family: 'Courier New', monospace;
            z-index: 150;
        }

        #storyPanel h3 {
            color: #FFD700;
            margin: 0 0 10px 0;
            text-align: center;
            font-size: 18px;
        }

        #storyPanel .character-info {
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
        }

        #storyPanel .character-info div {
            margin: 5px 0;
            font-size: 14px;
        }

        .story-toggle {
            position: fixed;
            top: 10px;
            right: 320px;
            background: #8B4513;
            border: 2px solid #D2B48C;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-family: 'Courier New', monospace;
            z-index: 160;
        }

        .story-toggle:hover {
            background: #A0522D;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <div id="loading">Loading Adventure World...</div>

        <!-- Character Creation Modal -->
        <div id="characterCreationModal" style="display: none;">
            <div class="character-creation-panel">
                <h1>🐛 BEETLE ADVENTURE 🐛</h1>
                <h2 id="characterCreationTitle">What is [Username] like to be?</h2>

                <h2>Choose Your Class</h2>
                <div class="class-selection">
                    <div class="class-option" data-class="Warrior">
                        <div>⚔️ Warrior</div>
                        <small>High Strength & Defense</small>
                    </div>
                    <div class="class-option" data-class="Mage">
                        <div>🔮 Mage</div>
                        <small>High Magic Power</small>
                    </div>
                    <div class="class-option" data-class="Rogue">
                        <div>🗡️ Rogue</div>
                        <small>Balanced Stats</small>
                    </div>
                </div>

                <h2>Choose Your Gender</h2>
                <div class="gender-selection">
                    <div class="gender-option" data-gender="male">
                        <div>👨 Male</div>
                    </div>
                    <div class="gender-option" data-gender="female">
                        <div>👩 Female</div>
                    </div>
                </div>

                <button class="start-button" id="startAdventure" disabled>Start Adventure!</button>
            </div>
        </div>

        <!-- Story Panel Toggle Button -->
        <button class="story-toggle" id="storyToggle" style="display: none;">📖 Story</button>

        <!-- Story Panel -->
        <div id="storyPanel" style="display: none;">
            <h3>Character Info</h3>
            <div class="character-info">
                <div id="charName">Name: -</div>
                <div id="charClass">Class: -</div>
                <div id="charLevel">Level: 1</div>
                <div id="charExp">Experience: 0/100</div>
                <div id="charGold">Gold: 150</div>
            </div>
            <div class="character-info">
                <div id="charStrength">⚔️ Strength: -</div>
                <div id="charDefense">🛡️ Defense: -</div>
                <div id="charMagic">🔮 Magic: -</div>
            </div>
        </div>

        <div id="ui" style="display: none;">
            <div>❤️ Health: <span id="health">100</span>/100</div>
            <div style="display: flex; align-items: center; margin: 5px 0;">
                <span style="margin-right: 10px;">⚡ Stamina:</span>
                <div id="staminaBarContainer" style="width: 100px; height: 12px; background: rgba(0,0,0,0.5); border: 1px solid #666; border-radius: 2px; position: relative;">
                    <div id="staminaBar" style="width: 100%; height: 100%; background: linear-gradient(to right, #00ff00, #ffff00, #ff0000); border-radius: 1px; transition: width 0.1s ease;"></div>
                </div>
                <span id="staminaText" style="margin-left: 8px; font-size: 12px;">100</span>
            </div>
            <div>🏔️ Biome: <span id="biome">Plains</span></div>
            <div>🧭 Town: <span id="townDirection">→ 100m East</span></div>
            <div>⏱️ FPS: <span id="fps">0</span></div>
            <div>🗡️ Orcs: <span id="orcs">0</span></div>
            <div>🦌 Animals: <span id="animals">0</span></div>
            <div>⏰ Time: <span id="time">12:00</span></div>
        </div>

        <div id="inventory" style="display: none;">
            <div>🗡️ Weapon: <span id="currentWeapon">Fist</span></div>
            <div>⚔️ Swords: <span id="swordCount">0</span></div>
            <div>💀 Kills: <span id="killCount">0</span></div>
            <div>🏆 Score: <span id="score">0</span></div>
        </div>
        
        <div id="crosshair" style="display: none;"></div>
        
        <div id="instructions" style="display: none;">
            WASD: Move | R: Run | Mouse: Look | Space: Jump | Left Click: Attack | I: Inventory | Ctrl+S: Save | Ctrl+L: Load | Mouse to lock
        </div>

        <!-- Inventory Panel -->
        <div id="inventoryPanel">
            <div id="inventoryInfo">
                <h3>📦 Inventory</h3>
            </div>
            <div id="selectedItemInfo">
                <div id="selectedItemName">Select an item to see details</div>
                <div id="selectedItemDesc"></div>
            </div>
            <div id="inventoryContent">
                <div id="inventoryGrid">
                    <!-- Grid slots will be generated by JavaScript -->
                </div>
                <div id="characterPreview">
                    <div id="characterPreviewTitle">Your Character</div>
                    <canvas id="characterPreviewCanvas" width="120" height="150"></canvas>
                </div>
            </div>
            <div id="inventoryHotbar">
                <!-- Hotbar slots in inventory will be generated by JavaScript -->
            </div>
            <div class="inventory-controls">
                Drag & Drop: Move Items | I or ESC: Close | 1-6: Select Hotbar
            </div>
        </div>

        <!-- Hotbar (always visible) -->
        <div id="hotbar">
            <!-- Hotbar slots will be generated by JavaScript -->
        </div>
    </div>

    <!-- Three.js Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    
    <!-- Game Modules -->
    <script src="js/auth.js"></script>
    <script src="js/saveload.js"></script>
    <script src="js/noise.js"></script>
    <script src="js/items.js"></script>
    <script src="js/characters.js"></script>
    <script src="js/inventory.js"></script>
    <script src="js/fixed-world.js"></script>
    <script src="js/world.js"></script>
    <script src="js/enemies.js"></script>
    <script src="js/npcs.js"></script>
    <script src="js/animals.js"></script>
    <script src="js/player.js"></script>
    <script src="js/ui.js"></script>
    <script src="js/story.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
