// === PLAYER SYSTEM ===
// Player character, controls, and combat

// Player class - simplified but with stamina and combat
class Player {
    constructor() {
        this.position = new THREE.Vector3(0, 20, 0);
        this.velocity = new THREE.Vector3(0, 0, 0);
        this.onGround = false;
        this.height = 1.8;
        this.health = 100;
        this.maxHealth = 100;
        this.stamina = 100;
        this.maxStamina = 100;
        this.isRunning = false;
        this.inventory = new Inventory();
        this.isDead = false;

        // Block placement system
        this.blockPlacementHighlight = null;
        this.placementRange = 5; // How far player can place blocks
        
        this.keys = {};
        this.rotation = { x: 0, y: 0 };
        this.mesh = null;
        this.isMoving = false;

        this.setupControls();
        this.createPlayerMesh();
        this.setupBlockPlacement();
    }

    createPlayerMesh() {
        // Remove old mesh if it exists
        if (this.mesh) {
            scene.remove(this.mesh);
        }

        // Create detailed player character
        this.mesh = createDetailedPlayerMesh();
        this.mesh.position.copy(this.position);
        scene.add(this.mesh);
    }
    
    setupControls() {
        document.addEventListener('keydown', (e) => {
            // Don't prevent default for inventory key when inventory is closed
            if (e.code !== 'KeyI' || this.inventory.isOpen) {
                this.keys[e.code] = true;
                if (!this.inventory.isOpen) {
                    e.preventDefault();
                }
            }
        });
        
        document.addEventListener('keyup', (e) => {
            this.keys[e.code] = false;
            if (!this.inventory.isOpen) {
                e.preventDefault();
            }
        });
        
        document.addEventListener('click', () => {
            if (!document.pointerLockElement && !this.inventory.isOpen) {
                document.body.requestPointerLock();
            }
        });
        
        document.addEventListener('mousemove', (e) => {
            if (document.pointerLockElement && !this.inventory.isOpen) {
                this.rotation.y -= e.movementX * 0.002;
                this.rotation.x -= e.movementY * 0.002;
                this.rotation.x = Math.max(-Math.PI/2, Math.min(Math.PI/2, this.rotation.x));
            }
        });
        
        // Mouse click for attacking
        document.addEventListener('mousedown', (e) => {
            if (document.pointerLockElement && e.button === 0 && !this.inventory.isOpen) {
                this.attack();
            }
            // Right-click for block placement
            if (document.pointerLockElement && e.button === 2 && !this.inventory.isOpen) {
                e.preventDefault();
                this.placeBlock();
            }
        });

        // Prevent context menu on right-click
        document.addEventListener('contextmenu', (e) => {
            if (document.pointerLockElement) {
                e.preventDefault();
            }
        });
    }
    
    attack() {
        const attackDamage = this.inventory.getWeaponDamage();
        const attackRange = 6;
        let hitSomething = false;
        
        // Find nearby orcs and attack them
        if (window.world) {
            window.world.orcs.forEach(orc => {
                if (orc.health > 0) {
                    const distance = this.position.distanceTo(orc.position);
                    if (distance <= attackRange) {
                        const wasAlive = orc.health > 0;
                        orc.takeDamage(attackDamage);
                        hitSomething = true;
                        
                        // If orc died, give reward
                        if (wasAlive && orc.health <= 0) {
                            this.inventory.addKill();
                            
                            // Random rewards
                            const rewards = ['sword', 'health_potion', 'stone_block', 'wood_block'];
                            const randomReward = rewards[Math.floor(Math.random() * rewards.length)];
                            this.inventory.addItem(randomReward.toUpperCase());
                            
                            // Show reward message
                            const rewardName = ITEMS[randomReward.toUpperCase()].name;
                            this.showMessage(`Orc defeated! +1 ${rewardName}!`, 'gold');
                        }
                    }
                }
            });
        }
        
        // Visual feedback
        if (hitSomething) {
            const flash = document.createElement('div');
            flash.style.position = 'fixed';
            flash.style.top = '0';
            flash.style.left = '0';
            flash.style.width = '100%';
            flash.style.height = '100%';
            flash.style.backgroundColor = 'rgba(255, 255, 0, 0.3)';
            flash.style.pointerEvents = 'none';
            flash.style.zIndex = '1000';
            document.body.appendChild(flash);
            
            setTimeout(() => {
                document.body.removeChild(flash);
            }, 100);
        }
    }
    
    showMessage(text, color = 'white') {
        const message = document.createElement('div');
        message.style.position = 'fixed';
        message.style.top = '30%';
        message.style.left = '50%';
        message.style.transform = 'translate(-50%, -50%)';
        message.style.color = color;
        message.style.fontSize = '24px';
        message.style.fontWeight = 'bold';
        message.style.textShadow = '2px 2px 4px rgba(0,0,0,0.8)';
        message.style.zIndex = '1000';
        message.style.pointerEvents = 'none';
        message.textContent = text;
        document.body.appendChild(message);
        
        setTimeout(() => {
            document.body.removeChild(message);
        }, 2000);
    }
    
    takeDamage(damage) {
        this.health -= damage;
        if (this.health < 0) this.health = 0;
        
        // Visual feedback - flash screen red
        const flash = document.createElement('div');
        flash.style.position = 'fixed';
        flash.style.top = '0';
        flash.style.left = '0';
        flash.style.width = '100%';
        flash.style.height = '100%';
        flash.style.backgroundColor = 'rgba(255, 0, 0, 0.3)';
        flash.style.pointerEvents = 'none';
        flash.style.zIndex = '1000';
        document.body.appendChild(flash);
        
        setTimeout(() => {
            document.body.removeChild(flash);
        }, 200);
        
        if (this.health <= 0) {
            this.die();
        }
    }
    
    heal(amount) {
        this.health += amount;
        if (this.health > this.maxHealth) {
            this.health = this.maxHealth;
        }
        
        // Visual feedback - flash screen green
        const flash = document.createElement('div');
        flash.style.position = 'fixed';
        flash.style.top = '0';
        flash.style.left = '0';
        flash.style.width = '100%';
        flash.style.height = '100%';
        flash.style.backgroundColor = 'rgba(0, 255, 0, 0.2)';
        flash.style.pointerEvents = 'none';
        flash.style.zIndex = '1000';
        document.body.appendChild(flash);
        
        setTimeout(() => {
            document.body.removeChild(flash);
        }, 200);
    }
    
    die() {
        this.isDead = true;
        
        // Create death screen
        const deathScreen = document.createElement('div');
        deathScreen.style.position = 'fixed';
        deathScreen.style.top = '0';
        deathScreen.style.left = '0';
        deathScreen.style.width = '100%';
        deathScreen.style.height = '100%';
        deathScreen.style.backgroundColor = 'black';
        deathScreen.style.zIndex = '2000';
        deathScreen.style.display = 'flex';
        deathScreen.style.flexDirection = 'column';
        deathScreen.style.justifyContent = 'center';
        deathScreen.style.alignItems = 'center';
        
        const gameOver = document.createElement('div');
        gameOver.style.color = 'red';
        gameOver.style.fontSize = '64px';
        gameOver.style.fontWeight = 'bold';
        gameOver.style.marginBottom = '30px';
        gameOver.textContent = 'GAME OVER';
        
        const stats = document.createElement('div');
        stats.style.color = 'white';
        stats.style.fontSize = '20px';
        stats.style.textAlign = 'center';
        stats.style.marginBottom = '40px';
        stats.innerHTML = `
            <div>Orcs Killed: ${this.inventory.kills}</div>
            <div>Swords Collected: ${this.inventory.swords}</div>
            <div>Final Score: ${this.inventory.score}</div>
        `;
        
        const restart = document.createElement('button');
        restart.style.padding = '15px 30px';
        restart.style.fontSize = '24px';
        restart.style.backgroundColor = '#ff4444';
        restart.style.color = 'white';
        restart.style.border = 'none';
        restart.style.borderRadius = '10px';
        restart.style.cursor = 'pointer';
        restart.textContent = 'RESTART GAME';
        restart.onclick = () => location.reload();
        
        deathScreen.appendChild(gameOver);
        deathScreen.appendChild(stats);
        deathScreen.appendChild(restart);
        document.body.appendChild(deathScreen);
        
        // Hide UI
        document.getElementById('ui').style.display = 'none';
        document.getElementById('inventory').style.display = 'none';
        document.getElementById('crosshair').style.display = 'none';
        document.getElementById('instructions').style.display = 'none';
    }

    setupBlockPlacement() {
        // Create placement highlight mesh
        this.createPlacementHighlight();
    }

    createPlacementHighlight() {
        // Create a wireframe cube to show where block will be placed
        const geometry = new THREE.BoxGeometry(1, 1, 1);
        const material = new THREE.MeshBasicMaterial({
            color: 0xFFD700, // Gold color
            wireframe: true,
            transparent: true,
            opacity: 0.8
        });

        this.blockPlacementHighlight = new THREE.Mesh(geometry, material);
        this.blockPlacementHighlight.visible = false;
        scene.add(this.blockPlacementHighlight);
    }

    updateBlockPlacementHighlight() {
        if (!this.blockPlacementHighlight) return;

        // Get selected hotbar item
        const selectedItem = this.inventory.hotbarSlots[this.inventory.selectedHotbarSlot];

        // Only show highlight if we have a placeable block
        if (selectedItem && this.isPlaceableBlock(selectedItem.id)) {
            // Calculate position 2 blocks in front of player
            const direction = new THREE.Vector3();
            camera.getWorldDirection(direction);

            // Position 2 blocks forward from player
            const targetPosition = this.position.clone();
            targetPosition.add(direction.multiplyScalar(2));

            // Snap to grid (whole block positions)
            const blockX = Math.floor(targetPosition.x);
            const blockY = Math.floor(targetPosition.y);
            const blockZ = Math.floor(targetPosition.z);

            // Check if position is within range
            const distance = this.position.distanceTo(new THREE.Vector3(blockX + 0.5, blockY + 0.5, blockZ + 0.5));
            if (distance <= this.placementRange) {
                // Check if target position is empty and has adjacent solid block
                if (this.canPlaceBlockAt(blockX, blockY, blockZ)) {
                    this.blockPlacementHighlight.position.set(blockX + 0.5, blockY + 0.5, blockZ + 0.5);
                    this.blockPlacementHighlight.visible = true;
                } else {
                    this.blockPlacementHighlight.visible = false;
                }
            } else {
                this.blockPlacementHighlight.visible = false;
            }
        } else {
            this.blockPlacementHighlight.visible = false;
        }
    }

    canPlaceBlockAt(x, y, z) {
        if (!window.world) return false;

        // Temporarily simplified - just check if we're near the ground
        // This will allow highlighting to work while we debug
        const groundHeight = getHeightAt(x, z);
        const targetHeight = y;

        // Allow placement if we're within 3 blocks of the ground
        if (Math.abs(targetHeight - groundHeight) <= 3) {
            return true;
        }

        return false;
    }

    isPlaceableBlock(itemId) {
        // Check if item is a placeable block
        const placeableBlocks = [
            'DIRT_BLOCK',
            'WOOD_BLOCK',
            'STONE_BLOCK',
            'GRASS_BLOCK'
        ];
        return placeableBlocks.includes(itemId);
    }

    placeBlock() {
        // Get selected hotbar item
        const selectedItem = this.inventory.hotbarSlots[this.inventory.selectedHotbarSlot];

        if (!selectedItem || !this.isPlaceableBlock(selectedItem.id)) {
            console.log('No placeable block selected');
            return;
        }

        if (!this.blockPlacementHighlight.visible) {
            console.log('Cannot place block here');
            return;
        }

        // Get placement position
        const placePos = this.blockPlacementHighlight.position.clone();

        // Convert to world coordinates
        const worldX = Math.floor(placePos.x);
        const worldY = Math.floor(placePos.y);
        const worldZ = Math.floor(placePos.z);

        // Place block in world
        if (window.world) {
            const blockType = this.getBlockTypeFromItem(selectedItem.id);

            // Find which chunk contains this position
            const chunkX = Math.floor(worldX / 32); // CHUNK_SIZE = 32
            const chunkZ = Math.floor(worldZ / 32);
            const localX = worldX % 32;
            const localZ = worldZ % 32;

            // Get the chunk
            const chunkKey = `${chunkX},${chunkZ}`;
            const chunk = window.world.chunks.get(chunkKey);

            if (chunk) {
                // Set block in chunk
                chunk.setBlock(localX, worldY, localZ, blockType);

                // Regenerate chunk mesh to show the new block
                chunk.createMesh();

                // Reduce item count
                selectedItem.count--;
                if (selectedItem.count <= 0) {
                    this.inventory.hotbarSlots[this.inventory.selectedHotbarSlot] = null;
                }

                // Update displays
                this.inventory.updateHotbarDisplay();

                console.log(`Placed ${selectedItem.id} at (${worldX}, ${worldY}, ${worldZ}) in chunk (${chunkX}, ${chunkZ})`);
                this.showMessage(`Placed ${selectedItem.id}`, 'green');
            } else {
                console.log(`No chunk found at (${chunkX}, ${chunkZ})`);
            }
        }
    }

    getBlockTypeFromItem(itemId) {
        // Convert item ID to block type using BLOCKS constants
        const blockMap = {
            'DIRT_BLOCK': BLOCKS.DIRT,
            'WOOD_BLOCK': BLOCKS.WOOD,
            'STONE_BLOCK': BLOCKS.STONE,
            'GRASS_BLOCK': BLOCKS.GRASS
        };
        return blockMap[itemId] || BLOCKS.DIRT;
    }

    update(deltaTime) {
        if (this.isDead) return;

        // Don't update movement if inventory is open
        if (this.inventory.isOpen) return;

        // Apply gravity
        this.velocity.y -= 25 * deltaTime;

        // Check if running
        const wantsToRun = this.keys['KeyR'] &&
            (this.keys['KeyW'] || this.keys['KeyS'] || this.keys['KeyA'] || this.keys['KeyD']);
        this.isRunning = wantsToRun && this.stamina > 5; // Lower stamina requirement

        // Update stamina with smoother calculations
        if (this.isRunning) {
            this.stamina -= 15 * deltaTime; // Consistent stamina drain
            if (this.stamina <= 0) {
                this.stamina = 0;
                this.isRunning = false;
            }
        } else {
            this.stamina += 30 * deltaTime; // Consistent stamina recovery
            if (this.stamina >= this.maxStamina) {
                this.stamina = this.maxStamina;
            }
        }

        // Clamp stamina to valid range to prevent glitches
        this.stamina = Math.max(0, Math.min(this.maxStamina, this.stamina));

        // Movement
        const moveVector = new THREE.Vector3();

        if (this.keys['KeyW']) moveVector.z -= 1;
        if (this.keys['KeyS']) moveVector.z += 1;
        if (this.keys['KeyA']) moveVector.x -= 1;
        if (this.keys['KeyD']) moveVector.x += 1;

        // Check if player is moving for animation
        this.isMoving = moveVector.length() > 0;

        if (moveVector.length() > 0) {
            moveVector.normalize();
            const speed = this.isRunning ? 90 : 60; // Walk at old run speed, run even faster!
            moveVector.multiplyScalar(speed * deltaTime);
            this.velocity.x = moveVector.x;
            this.velocity.z = moveVector.z;
        } else {
            this.velocity.x *= 0.95; // Even less friction for super smooth movement
            this.velocity.z *= 0.95;
        }

        // Jump
        if (this.keys['Space'] && this.onGround) {
            this.velocity.y = 12; // Higher jump for more dynamic movement
            this.onGround = false;
        }

        // Apply movement
        this.position.add(this.velocity.clone().multiplyScalar(deltaTime));

        // Ground collision
        const groundHeight = getHeightAt(Math.floor(this.position.x), Math.floor(this.position.z)) + this.height;
        if (this.position.y <= groundHeight) {
            this.position.y = groundHeight;
            this.velocity.y = 0;
            this.onGround = true;
        } else {
            this.onGround = false;
        }

        // Update player mesh position and animation
        if (this.mesh) {
            this.mesh.position.copy(this.position);
            this.mesh.position.y -= this.height; // Adjust for character height

            // Animate character
            const time = Date.now() * 0.001;
            animateCharacter(this.mesh, time, this.isMoving);

            // Make character face movement direction when moving
            if (this.isMoving) {
                const direction = new THREE.Vector3(this.velocity.x, 0, this.velocity.z);
                if (direction.length() > 0) {
                    direction.normalize();
                    const angle = Math.atan2(direction.x, direction.z);
                    this.mesh.rotation.y = angle;
                }
            }
        }

        // Update camera
        camera.position.copy(this.position);
        camera.rotation.x = this.rotation.x;
        camera.rotation.y = this.rotation.y;

        // Update block placement highlight
        this.updateBlockPlacementHighlight();
    }
}
