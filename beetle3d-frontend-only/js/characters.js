// === CHARACTER MODELS ===
// Enhanced 3D character models with details and animations

// Enhanced Player Character Creation
function createDetailedPlayerMesh() {
    const group = new THREE.Group();
    
    // Body (torso)
    const bodyGeometry = new THREE.BoxGeometry(0.8, 1.2, 0.4);
    const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x4169E1 }); // Royal blue shirt
    const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
    body.position.y = 0.1;
    group.add(body);
    
    // Head
    const headGeometry = new THREE.BoxGeometry(0.6, 0.6, 0.6);
    const headMaterial = new THREE.MeshLambertMaterial({ color: 0xFFDBB5 }); // Skin color
    const head = new THREE.Mesh(headGeometry, headMaterial);
    head.position.y = 1.0;
    group.add(head);
    
    // Hair
    const hairGeometry = new THREE.BoxGeometry(0.65, 0.3, 0.65);
    const hairMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 }); // Brown hair
    const hair = new THREE.Mesh(hairGeometry, hairMaterial);
    hair.position.y = 1.25;
    group.add(hair);
    
    // Eyes
    const eyeGeometry = new THREE.BoxGeometry(0.1, 0.1, 0.1);
    const eyeMaterial = new THREE.MeshLambertMaterial({ color: 0x000000 });
    const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
    const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
    leftEye.position.set(-0.15, 1.05, 0.25);
    rightEye.position.set(0.15, 1.05, 0.25);
    group.add(leftEye);
    group.add(rightEye);
    
    // Arms
    const armGeometry = new THREE.BoxGeometry(0.3, 1.0, 0.3);
    const armMaterial = new THREE.MeshLambertMaterial({ color: 0xFFDBB5 }); // Skin color
    const leftArm = new THREE.Mesh(armGeometry, armMaterial);
    const rightArm = new THREE.Mesh(armGeometry, armMaterial);
    leftArm.position.set(-0.6, 0.1, 0);
    rightArm.position.set(0.6, 0.1, 0);
    group.add(leftArm);
    group.add(rightArm);
    
    // Sleeves
    const sleeveGeometry = new THREE.BoxGeometry(0.32, 0.5, 0.32);
    const sleeveMaterial = new THREE.MeshLambertMaterial({ color: 0x4169E1 }); // Match shirt
    const leftSleeve = new THREE.Mesh(sleeveGeometry, sleeveMaterial);
    const rightSleeve = new THREE.Mesh(sleeveGeometry, sleeveMaterial);
    leftSleeve.position.set(-0.6, 0.35, 0);
    rightSleeve.position.set(0.6, 0.35, 0);
    group.add(leftSleeve);
    group.add(rightSleeve);
    
    // Legs
    const legGeometry = new THREE.BoxGeometry(0.3, 1.0, 0.3);
    const legMaterial = new THREE.MeshLambertMaterial({ color: 0x2F4F4F }); // Dark gray pants
    const leftLeg = new THREE.Mesh(legGeometry, legMaterial);
    const rightLeg = new THREE.Mesh(legGeometry, legMaterial);
    leftLeg.position.set(-0.2, -0.9, 0);
    rightLeg.position.set(0.2, -0.9, 0);
    group.add(leftLeg);
    group.add(rightLeg);
    
    // Shoes
    const shoeGeometry = new THREE.BoxGeometry(0.35, 0.2, 0.5);
    const shoeMaterial = new THREE.MeshLambertMaterial({ color: 0x654321 }); // Brown shoes
    const leftShoe = new THREE.Mesh(shoeGeometry, shoeMaterial);
    const rightShoe = new THREE.Mesh(shoeGeometry, shoeMaterial);
    leftShoe.position.set(-0.2, -1.5, 0.1);
    rightShoe.position.set(0.2, -1.5, 0.1);
    group.add(leftShoe);
    group.add(rightShoe);
    
    // Belt
    const beltGeometry = new THREE.BoxGeometry(0.85, 0.15, 0.45);
    const beltMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 }); // Brown belt
    const belt = new THREE.Mesh(beltGeometry, beltMaterial);
    belt.position.y = -0.3;
    group.add(belt);
    
    // Store references for animation
    group.userData = {
        leftArm: leftArm,
        rightArm: rightArm,
        leftLeg: leftLeg,
        rightLeg: rightLeg,
        body: body
    };
    
    return group;
}

// Enhanced Orc Character Creation with variants
function createDetailedOrcMesh(variant = 'warrior') {
    const group = new THREE.Group();
    
    // Base colors for different variants
    const variants = {
        warrior: { skin: 0x4a5d23, armor: 0x696969, accent: 0xff0000 },
        archer: { skin: 0x3a4d1a, armor: 0x8B4513, accent: 0x00ff00 },
        chief: { skin: 0x5a6d2a, armor: 0xFFD700, accent: 0xff4500 }
    };
    
    const colors = variants[variant] || variants.warrior;
    
    // Body (larger and more muscular)
    const bodyGeometry = new THREE.BoxGeometry(1.0, 1.8, 0.5);
    const bodyMaterial = new THREE.MeshLambertMaterial({ color: colors.skin });
    const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
    body.position.y = 0.2;
    group.add(body);
    
    // Head (larger)
    const headGeometry = new THREE.BoxGeometry(0.8, 0.8, 0.8);
    const headMaterial = new THREE.MeshLambertMaterial({ color: colors.skin });
    const head = new THREE.Mesh(headGeometry, headMaterial);
    head.position.y = 1.3;
    group.add(head);
    
    // Horns
    const hornGeometry = new THREE.ConeGeometry(0.08, 0.3, 6);
    const hornMaterial = new THREE.MeshLambertMaterial({ color: 0x2F2F2F });
    const leftHorn = new THREE.Mesh(hornGeometry, hornMaterial);
    const rightHorn = new THREE.Mesh(hornGeometry, hornMaterial);
    leftHorn.position.set(-0.25, 1.6, 0);
    rightHorn.position.set(0.25, 1.6, 0);
    group.add(leftHorn);
    group.add(rightHorn);
    
    // Glowing red eyes
    const eyeGeometry = new THREE.BoxGeometry(0.12, 0.12, 0.12);
    const eyeMaterial = new THREE.MeshLambertMaterial({ 
        color: colors.accent,
        emissive: colors.accent,
        emissiveIntensity: 0.3
    });
    const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
    const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
    leftEye.position.set(-0.2, 1.35, 0.35);
    rightEye.position.set(0.2, 1.35, 0.35);
    group.add(leftEye);
    group.add(rightEye);
    
    // Tusks
    const tuskGeometry = new THREE.ConeGeometry(0.05, 0.2, 4);
    const tuskMaterial = new THREE.MeshLambertMaterial({ color: 0xFFFFF0 });
    const leftTusk = new THREE.Mesh(tuskGeometry, tuskMaterial);
    const rightTusk = new THREE.Mesh(tuskGeometry, tuskMaterial);
    leftTusk.position.set(-0.15, 1.15, 0.35);
    rightTusk.position.set(0.15, 1.15, 0.35);
    leftTusk.rotation.x = Math.PI;
    rightTusk.rotation.x = Math.PI;
    group.add(leftTusk);
    group.add(rightTusk);
    
    // Arms (muscular)
    const armGeometry = new THREE.BoxGeometry(0.4, 1.4, 0.4);
    const armMaterial = new THREE.MeshLambertMaterial({ color: colors.skin });
    const leftArm = new THREE.Mesh(armGeometry, armMaterial);
    const rightArm = new THREE.Mesh(armGeometry, armMaterial);
    leftArm.position.set(-0.7, 0.2, 0);
    rightArm.position.set(0.7, 0.2, 0);
    group.add(leftArm);
    group.add(rightArm);
    
    // Armor plates on shoulders
    const shoulderGeometry = new THREE.BoxGeometry(0.5, 0.3, 0.5);
    const shoulderMaterial = new THREE.MeshLambertMaterial({ color: colors.armor });
    const leftShoulder = new THREE.Mesh(shoulderGeometry, shoulderMaterial);
    const rightShoulder = new THREE.Mesh(shoulderGeometry, shoulderMaterial);
    leftShoulder.position.set(-0.7, 0.7, 0);
    rightShoulder.position.set(0.7, 0.7, 0);
    group.add(leftShoulder);
    group.add(rightShoulder);
    
    // Shoulder spikes
    const spikeGeometry = new THREE.ConeGeometry(0.08, 0.25, 6);
    const spikeMaterial = new THREE.MeshLambertMaterial({ color: 0x2F2F2F });
    const leftSpike = new THREE.Mesh(spikeGeometry, spikeMaterial);
    const rightSpike = new THREE.Mesh(spikeGeometry, spikeMaterial);
    leftSpike.position.set(-0.7, 0.95, 0);
    rightSpike.position.set(0.7, 0.95, 0);
    group.add(leftSpike);
    group.add(rightSpike);
    
    // Legs (thick)
    const legGeometry = new THREE.BoxGeometry(0.4, 1.2, 0.4);
    const legMaterial = new THREE.MeshLambertMaterial({ color: colors.skin });
    const leftLeg = new THREE.Mesh(legGeometry, legMaterial);
    const rightLeg = new THREE.Mesh(legGeometry, legMaterial);
    leftLeg.position.set(-0.25, -1.0, 0);
    rightLeg.position.set(0.25, -1.0, 0);
    group.add(leftLeg);
    group.add(rightLeg);
    
    // Spiked boots
    const bootGeometry = new THREE.BoxGeometry(0.45, 0.25, 0.6);
    const bootMaterial = new THREE.MeshLambertMaterial({ color: 0x2F2F2F });
    const leftBoot = new THREE.Mesh(bootGeometry, bootMaterial);
    const rightBoot = new THREE.Mesh(bootGeometry, bootMaterial);
    leftBoot.position.set(-0.25, -1.7, 0.1);
    rightBoot.position.set(0.25, -1.7, 0.1);
    group.add(leftBoot);
    group.add(rightBoot);
    
    // Boot spikes
    const bootSpikeGeometry = new THREE.ConeGeometry(0.05, 0.15, 4);
    const bootSpikeMaterial = new THREE.MeshLambertMaterial({ color: 0x8B0000 });
    const leftBootSpike = new THREE.Mesh(bootSpikeGeometry, bootSpikeMaterial);
    const rightBootSpike = new THREE.Mesh(bootSpikeGeometry, bootSpikeMaterial);
    leftBootSpike.position.set(-0.25, -1.7, 0.4);
    rightBootSpike.position.set(0.25, -1.7, 0.4);
    group.add(leftBootSpike);
    group.add(rightBootSpike);
    
    // Weapon based on variant
    if (variant === 'warrior' || variant === 'chief') {
        // Battle axe
        const axeHandleGeometry = new THREE.BoxGeometry(0.1, 1.5, 0.1);
        const axeHandleMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const axeHandle = new THREE.Mesh(axeHandleGeometry, axeHandleMaterial);
        axeHandle.position.set(0.9, 0.2, 0);
        group.add(axeHandle);
        
        const axeHeadGeometry = new THREE.BoxGeometry(0.6, 0.4, 0.15);
        const axeHeadMaterial = new THREE.MeshLambertMaterial({ color: colors.armor });
        const axeHead = new THREE.Mesh(axeHeadGeometry, axeHeadMaterial);
        axeHead.position.set(0.9, 0.8, 0);
        group.add(axeHead);
    } else if (variant === 'archer') {
        // Bow
        const bowGeometry = new THREE.TorusGeometry(0.3, 0.03, 4, 8, Math.PI);
        const bowMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const bow = new THREE.Mesh(bowGeometry, bowMaterial);
        bow.position.set(-0.9, 0.2, 0);
        bow.rotation.z = Math.PI / 2;
        group.add(bow);
    }
    
    // Store references for animation
    group.userData = {
        leftArm: leftArm,
        rightArm: rightArm,
        leftLeg: leftLeg,
        rightLeg: rightLeg,
        body: body,
        variant: variant
    };
    
    return group;
}

// Animation functions
function animateCharacter(characterMesh, time, isMoving = false) {
    if (!characterMesh || !characterMesh.userData) return;
    
    const { leftArm, rightArm, leftLeg, rightLeg, body } = characterMesh.userData;
    
    if (isMoving) {
        // Walking animation
        const walkCycle = Math.sin(time * 8) * 0.3;
        
        // Arm swinging (opposite to legs)
        if (leftArm) leftArm.rotation.x = walkCycle;
        if (rightArm) rightArm.rotation.x = -walkCycle;
        
        // Leg movement
        if (leftLeg) leftLeg.rotation.x = -walkCycle;
        if (rightLeg) rightLeg.rotation.x = walkCycle;
        
        // Body bobbing
        if (body) body.position.y = 0.1 + Math.abs(Math.sin(time * 8)) * 0.05;
    } else {
        // Idle animation - gentle breathing
        const breathe = Math.sin(time * 2) * 0.02;
        if (body) body.position.y = 0.1 + breathe;
        
        // Reset limb rotations
        if (leftArm) leftArm.rotation.x = 0;
        if (rightArm) rightArm.rotation.x = 0;
        if (leftLeg) leftLeg.rotation.x = 0;
        if (rightLeg) rightLeg.rotation.x = 0;
    }
}
