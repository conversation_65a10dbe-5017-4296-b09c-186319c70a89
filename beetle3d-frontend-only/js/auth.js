// === AUTHENTICATION SYSTEM ===
// Login, registration, and save system

class AuthSystem {
    constructor() {
        this.currentUser = null;
        this.isLoggedIn = false;
        this.serverMode = this.detectServerMode();
        
        this.initializeAuth();
    }
    
    detectServerMode() {
        // Check if we're running on a real server or localhost
        const hostname = window.location.hostname;
        return hostname !== 'localhost' && hostname !== '127.0.0.1' && hostname !== '';
    }
    
    initializeAuth() {
        console.log('Initializing auth system...');

        // Check if user is already logged in
        const savedUser = localStorage.getItem('minecraftUser');
        if (savedUser) {
            try {
                this.currentUser = JSON.parse(savedUser);
                this.isLoggedIn = true;
                console.log('User already logged in:', this.currentUser.username);

                // User is logged in, start the game
                this.onLoginSuccess(this.currentUser.username, true);
                return;
            } catch (e) {
                console.warn('Invalid saved user data, clearing...');
                localStorage.removeItem('minecraftUser');
            }
        }

        // Show login screen if not logged in
        console.log('No saved login found, showing login screen');
        this.showLoginScreen();
    }
    
    showLoginScreen() {
        // Hide game content
        document.getElementById('gameContainer').style.display = 'none';
        
        // Create login screen
        const loginScreen = document.createElement('div');
        loginScreen.id = 'loginScreen';
        loginScreen.innerHTML = `
            <div class="login-container">
                <div class="login-box">
                    <h1>🎮 Minecraft Adventure</h1>
                    <h2>Welcome, Adventurer!</h2>
                    
                    <div class="login-tabs">
                        <button id="loginTab" class="tab-button active" onclick="authSystem.switchTab('login')">Login</button>
                        <button id="registerTab" class="tab-button" onclick="authSystem.switchTab('register')">Sign Up</button>
                    </div>
                    
                    <!-- Login Form -->
                    <div id="loginForm" class="auth-form">
                        <h3>Login to Your Adventure</h3>
                        <input type="text" id="loginUsername" placeholder="Username" maxlength="20">
                        <input type="password" id="loginPassword" placeholder="Password">
                        <button onclick="authSystem.login()" class="auth-button">🚀 Start Adventure</button>
                        <div id="loginError" class="error-message"></div>
                    </div>
                    
                    <!-- Register Form -->
                    <div id="registerForm" class="auth-form" style="display: none;">
                        <h3>Create New Adventure</h3>
                        <input type="text" id="registerUsername" placeholder="Choose Username" maxlength="20">
                        <input type="password" id="registerPassword" placeholder="Choose Password" minlength="4">
                        <input type="password" id="confirmPassword" placeholder="Confirm Password">
                        <button onclick="authSystem.register()" class="auth-button">⚔️ Create Hero</button>
                        <div id="registerError" class="error-message"></div>
                    </div>
                    
                    <div class="login-footer">
                        <p>🏰 Build, Fight, Explore, Survive! 🏰</p>
                        ${this.serverMode ? '<p class="server-info">🌐 Connected to Game Server</p>' : '<p class="server-info">💻 Local Mode</p>'}
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(loginScreen);
        this.addLoginStyles();
        
        // Focus on username field
        setTimeout(() => {
            document.getElementById('loginUsername').focus();
        }, 100);
        
        // Add enter key support
        this.setupKeyboardSupport();
    }
    
    addLoginStyles() {
        const style = document.createElement('style');
        style.textContent = `
            #loginScreen {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #2F4F4F 100%);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                font-family: 'Courier New', monospace;
            }
            
            .login-container {
                background: rgba(0, 0, 0, 0.8);
                border-radius: 15px;
                padding: 30px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
                border: 3px solid #8B4513;
                max-width: 400px;
                width: 90%;
            }
            
            .login-box h1 {
                color: #FFD700;
                text-align: center;
                margin-bottom: 10px;
                font-size: 28px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
            
            .login-box h2 {
                color: #87CEEB;
                text-align: center;
                margin-bottom: 25px;
                font-size: 18px;
            }
            
            .login-tabs {
                display: flex;
                margin-bottom: 20px;
                border-radius: 8px;
                overflow: hidden;
            }
            
            .tab-button {
                flex: 1;
                padding: 12px;
                background: #654321;
                color: white;
                border: none;
                cursor: pointer;
                font-family: 'Courier New', monospace;
                font-size: 14px;
                transition: background 0.3s;
            }
            
            .tab-button:hover {
                background: #8B4513;
            }
            
            .tab-button.active {
                background: #A0522D;
                font-weight: bold;
            }
            
            .auth-form {
                margin-bottom: 20px;
            }
            
            .auth-form h3 {
                color: #F5DEB3;
                text-align: center;
                margin-bottom: 20px;
                font-size: 16px;
            }
            
            .auth-form input {
                width: 100%;
                padding: 12px;
                margin-bottom: 15px;
                border: 2px solid #8B4513;
                border-radius: 5px;
                background: #F5DEB3;
                color: #2F4F4F;
                font-family: 'Courier New', monospace;
                font-size: 14px;
                box-sizing: border-box;
            }
            
            .auth-form input:focus {
                outline: none;
                border-color: #FFD700;
                box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
            }
            
            .auth-button {
                width: 100%;
                padding: 15px;
                background: linear-gradient(45deg, #228B22, #32CD32);
                color: white;
                border: none;
                border-radius: 8px;
                font-family: 'Courier New', monospace;
                font-size: 16px;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
            
            .auth-button:hover {
                background: linear-gradient(45deg, #32CD32, #228B22);
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            }
            
            .error-message {
                color: #FF6B6B;
                text-align: center;
                margin-top: 10px;
                font-size: 12px;
                min-height: 16px;
            }
            
            .login-footer {
                text-align: center;
                color: #87CEEB;
                font-size: 12px;
                margin-top: 20px;
            }
            
            .server-info {
                color: #90EE90;
                font-weight: bold;
                margin-top: 10px;
            }
        `;
        document.head.appendChild(style);
    }
    
    setupKeyboardSupport() {
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                const activeForm = document.querySelector('.auth-form:not([style*="display: none"])');
                if (activeForm) {
                    if (activeForm.id === 'loginForm') {
                        this.login();
                    } else {
                        this.register();
                    }
                }
            }
        });
    }
    
    switchTab(tab) {
        // Update tab buttons
        document.getElementById('loginTab').classList.toggle('active', tab === 'login');
        document.getElementById('registerTab').classList.toggle('active', tab === 'register');
        
        // Show/hide forms
        document.getElementById('loginForm').style.display = tab === 'login' ? 'block' : 'none';
        document.getElementById('registerForm').style.display = tab === 'register' ? 'block' : 'none';
        
        // Clear error messages
        document.getElementById('loginError').textContent = '';
        document.getElementById('registerError').textContent = '';
        
        // Focus on first input
        setTimeout(() => {
            const firstInput = document.querySelector(`#${tab}Form input`);
            if (firstInput) firstInput.focus();
        }, 100);
    }

    async login() {
        const username = document.getElementById('loginUsername').value.trim();
        const password = document.getElementById('loginPassword').value;
        const errorElement = document.getElementById('loginError');

        // Clear previous errors
        errorElement.textContent = '';

        // Validation
        if (!username || !password) {
            errorElement.textContent = '⚠️ Please enter both username and password';
            return;
        }

        try {
            // Show loading
            const button = document.querySelector('#loginForm .auth-button');
            const originalText = button.textContent;
            button.textContent = '🔄 Logging in...';
            button.disabled = true;

            let loginSuccess = false;

            if (this.serverMode) {
                // Server mode - make API call
                loginSuccess = await this.serverLogin(username, password);
            } else {
                // Local mode - use localStorage
                loginSuccess = this.localLogin(username, password);
            }

            if (loginSuccess) {
                this.onLoginSuccess(username);
            } else {
                errorElement.textContent = '❌ Invalid username or password';
            }

            // Restore button
            button.textContent = originalText;
            button.disabled = false;

        } catch (error) {
            console.error('Login error:', error);
            errorElement.textContent = '🔥 Connection error. Please try again.';
        }
    }

    async register() {
        const username = document.getElementById('registerUsername').value.trim();
        const password = document.getElementById('registerPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        const errorElement = document.getElementById('registerError');

        // Clear previous errors
        errorElement.textContent = '';

        // Validation
        if (!username || !password || !confirmPassword) {
            errorElement.textContent = '⚠️ Please fill in all fields';
            return;
        }

        if (username.length < 3) {
            errorElement.textContent = '⚠️ Username must be at least 3 characters';
            return;
        }

        if (password.length < 4) {
            errorElement.textContent = '⚠️ Password must be at least 4 characters';
            return;
        }

        if (password !== confirmPassword) {
            errorElement.textContent = '⚠️ Passwords do not match';
            return;
        }

        // Check for valid characters
        if (!/^[a-zA-Z0-9_]+$/.test(username)) {
            errorElement.textContent = '⚠️ Username can only contain letters, numbers, and underscores';
            return;
        }

        try {
            // Show loading
            const button = document.querySelector('#registerForm .auth-button');
            const originalText = button.textContent;
            button.textContent = '🔄 Creating hero...';
            button.disabled = true;

            let registerSuccess = false;

            if (this.serverMode) {
                // Server mode - make API call
                registerSuccess = await this.serverRegister(username, password);
            } else {
                // Local mode - use localStorage
                registerSuccess = this.localRegister(username, password);
            }

            if (registerSuccess) {
                this.onLoginSuccess(username);
            }

            // Restore button
            button.textContent = originalText;
            button.disabled = false;

        } catch (error) {
            console.error('Registration error:', error);
            errorElement.textContent = '🔥 Registration failed. Please try again.';
        }
    }

    localLogin(username, password) {
        // Local storage based authentication
        const users = JSON.parse(localStorage.getItem('minecraftUsers') || '{}');
        const user = users[username.toLowerCase()];

        if (user && user.password === password) {
            return true;
        }
        return false;
    }

    localRegister(username, password) {
        const users = JSON.parse(localStorage.getItem('minecraftUsers') || '{}');
        const lowerUsername = username.toLowerCase();

        // Check if user already exists
        if (users[lowerUsername]) {
            document.getElementById('registerError').textContent = '⚠️ Username already taken';
            return false;
        }

        // Create new user
        users[lowerUsername] = {
            username: username,
            password: password,
            createdAt: new Date().toISOString(),
            gameData: this.getDefaultGameData()
        };

        localStorage.setItem('minecraftUsers', JSON.stringify(users));
        return true;
    }

    async serverLogin(username, password) {
        try {
            const response = await fetch('http://localhost:3001/api/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ username, password })
            });

            const data = await response.json();

            if (data.success) {
                // Store JWT token
                localStorage.setItem('authToken', data.token);
                localStorage.setItem('userData', JSON.stringify(data.user));
                return true;
            } else {
                console.error('Server login failed:', data.error);
                return false;
            }
        } catch (error) {
            console.error('Server login error:', error);
            // Fallback to local mode if server is down
            return this.localLogin(username, password);
        }
    }

    async serverRegister(username, password) {
        try {
            const response = await fetch('http://localhost:3001/api/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ username, password })
            });

            const data = await response.json();

            if (data.success) {
                // Store JWT token
                localStorage.setItem('authToken', data.token);
                localStorage.setItem('userData', JSON.stringify(data.user));
                return true;
            } else {
                document.getElementById('registerError').textContent = `⚠️ ${data.error}`;
                return false;
            }
        } catch (error) {
            console.error('Server register error:', error);
            // Fallback to local mode if server is down
            return this.localRegister(username, password);
        }
    }

    getDefaultGameData() {
        return {
            position: { x: 0, y: 20, z: 0 },
            health: 100,
            stamina: 100,
            inventory: {
                slots: new Array(20).fill(null),
                kills: 0,
                score: 0
            },
            playTime: 0,
            lastSaved: new Date().toISOString()
        };
    }

    onLoginSuccess(username, isAutoLogin = false) {
        console.log(`Login success for ${username} (auto: ${isAutoLogin})`);

        this.currentUser = {
            username: username,
            loginTime: new Date().toISOString()
        };
        this.isLoggedIn = true;

        // Save login state
        localStorage.setItem('minecraftUser', JSON.stringify(this.currentUser));

        // Remove login screen if it exists
        const loginScreen = document.getElementById('loginScreen');
        if (loginScreen) {
            loginScreen.remove();
        }

        // Load user's game data
        this.loadGameData();

        console.log(`Welcome back, ${username}! 🎮`);

        // Make sure game container is visible
        document.getElementById('gameContainer').style.display = 'block';

        // Initialize game after login
        if (typeof window.init === 'function') {
            console.log('Starting game initialization...');
            try {
                window.init();
            } catch (error) {
                console.error('Game initialization error:', error);
                alert('Error starting game. Please refresh the page.');
            }
        } else {
            console.error('Game initialization function not found!');
            document.getElementById('loading').textContent = 'Error: Game initialization function not found.';
        }
    }

    loadGameData() {
        if (this.serverMode) {
            // Load from server (implement later)
            console.log('Loading game data from server...');
        } else {
            // Load from localStorage
            const users = JSON.parse(localStorage.getItem('minecraftUsers') || '{}');
            const userData = users[this.currentUser.username.toLowerCase()];

            if (userData && userData.gameData) {
                console.log('Loading saved game data...');
                // Game data will be loaded by the game initialization
                window.savedGameData = userData.gameData;
            }
        }
    }

    async saveGameData(gameData) {
        if (!this.isLoggedIn) return;

        if (this.serverMode) {
            try {
                const token = localStorage.getItem('authToken');
                const response = await fetch('http://localhost:3001/api/save', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(gameData)
                });

                const data = await response.json();
                if (data.success) {
                    console.log('Game data saved to server');
                } else {
                    console.error('Server save failed:', data.error);
                }
            } catch (error) {
                console.error('Server save error:', error);
            }
        } else {
            // Save to localStorage
            const users = JSON.parse(localStorage.getItem('minecraftUsers') || '{}');
            const lowerUsername = this.currentUser.username.toLowerCase();

            if (users[lowerUsername]) {
                users[lowerUsername].gameData = {
                    ...gameData,
                    lastSaved: new Date().toISOString()
                };
                localStorage.setItem('minecraftUsers', JSON.stringify(users));
                console.log('Game data saved locally');
            }
        }
    }

    logout() {
        // Save current game state before logout
        if (window.player && this.isLoggedIn) {
            const gameData = {
                position: {
                    x: window.player.position.x,
                    y: window.player.position.y,
                    z: window.player.position.z
                },
                health: window.player.health,
                stamina: window.player.stamina,
                inventory: {
                    slots: window.player.inventory.slots,
                    kills: window.player.inventory.kills,
                    score: window.player.inventory.score
                },
                playTime: Date.now() - new Date(this.currentUser.loginTime).getTime()
            };

            this.saveGameData(gameData);
        }

        // Clear login state
        localStorage.removeItem('minecraftUser');
        this.currentUser = null;
        this.isLoggedIn = false;

        // Reload page to show login screen
        window.location.reload();
    }
}

// Global auth system instance
let authSystem;
