// === INVENTORY SYSTEM ===
// 3D item icon renderer and inventory management

// Item icon renderer for 3D items
class ItemIconRenderer {
    constructor() {
        this.iconCache = new Map();
        this.iconScene = new THREE.Scene();
        this.iconCamera = new THREE.OrthographicCamera(-1, 1, 1, -1, 0.1, 100);
        this.iconRenderer = new THREE.WebGLRenderer({ 
            antialias: false, 
            alpha: true,
            preserveDrawingBuffer: true
        });
        this.iconRenderer.setSize(64, 64);
        this.iconRenderer.setClearColor(0x000000, 0);
        
        // Setup lighting for icons
        const ambientLight = new THREE.AmbientLight(0x404040, 0.8);
        this.iconScene.add(ambientLight);
        
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
        directionalLight.position.set(1.5, 1.5, 1.5);
        this.iconScene.add(directionalLight);
        
        this.iconCamera.position.set(1.5, 1.5, 1.5);
        this.iconCamera.lookAt(0, 0, 0);
    }
    
    createSwordIcon() {
        const group = new THREE.Group();
        
        // Blade - much larger
        const bladeGeometry = new THREE.BoxGeometry(0.15, 1.2, 0.08);
        const bladeMaterial = new THREE.MeshLambertMaterial({ color: 0xC0C0C0 });
        const blade = new THREE.Mesh(bladeGeometry, bladeMaterial);
        blade.position.y = 0.2;
        group.add(blade);
        
        // Handle - larger
        const handleGeometry = new THREE.BoxGeometry(0.2, 0.5, 0.15);
        const handleMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const handle = new THREE.Mesh(handleGeometry, handleMaterial);
        handle.position.y = -0.4;
        group.add(handle);
        
        // Guard - larger
        const guardGeometry = new THREE.BoxGeometry(0.5, 0.12, 0.12);
        const guardMaterial = new THREE.MeshLambertMaterial({ color: 0x696969 });
        const guard = new THREE.Mesh(guardGeometry, guardMaterial);
        guard.position.y = -0.05;
        group.add(guard);
        
        // Scale the entire sword up
        group.scale.set(1.2, 1.2, 1.2);
        
        return group;
    }
    
    createPotionIcon() {
        const group = new THREE.Group();
        
        // Bottle body - much larger
        const bottleGeometry = new THREE.CylinderGeometry(0.4, 0.35, 1.0, 8);
        const bottleMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x8B4513,
            transparent: true,
            opacity: 0.7
        });
        const bottle = new THREE.Mesh(bottleGeometry, bottleMaterial);
        group.add(bottle);
        
        // Potion liquid - larger
        const liquidGeometry = new THREE.CylinderGeometry(0.35, 0.3, 0.75, 8);
        const liquidMaterial = new THREE.MeshLambertMaterial({ 
            color: 0xFF1493,
            transparent: true,
            opacity: 0.9
        });
        const liquid = new THREE.Mesh(liquidGeometry, liquidMaterial);
        liquid.position.y = -0.05;
        group.add(liquid);
        
        // Cork - larger
        const corkGeometry = new THREE.CylinderGeometry(0.2, 0.2, 0.25, 8);
        const corkMaterial = new THREE.MeshLambertMaterial({ color: 0xD2B48C });
        const cork = new THREE.Mesh(corkGeometry, corkMaterial);
        cork.position.y = 0.6;
        group.add(cork);
        
        // Scale the entire potion up
        group.scale.set(1.3, 1.3, 1.3);
        
        return group;
    }
    
    createPickaxeIcon() {
        const group = new THREE.Group();
        
        // Handle - larger
        const handleGeometry = new THREE.BoxGeometry(0.12, 1.0, 0.12);
        const handleMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const handle = new THREE.Mesh(handleGeometry, handleMaterial);
        group.add(handle);
        
        // Pickaxe head - larger
        const headGeometry = new THREE.BoxGeometry(1.0, 0.2, 0.2);
        const headMaterial = new THREE.MeshLambertMaterial({ color: 0x696969 });
        const head = new THREE.Mesh(headGeometry, headMaterial);
        head.position.y = 0.3;
        group.add(head);
        
        // Pick points - larger
        const pointGeometry = new THREE.ConeGeometry(0.08, 0.25, 4);
        const pointMaterial = new THREE.MeshLambertMaterial({ color: 0x2F4F4F });
        
        const leftPoint = new THREE.Mesh(pointGeometry, pointMaterial);
        leftPoint.position.set(-0.55, 0.3, 0);
        leftPoint.rotation.z = Math.PI / 2;
        group.add(leftPoint);
        
        const rightPoint = new THREE.Mesh(pointGeometry, pointMaterial);
        rightPoint.position.set(0.55, 0.3, 0);
        rightPoint.rotation.z = -Math.PI / 2;
        group.add(rightPoint);
        
        // Scale the entire pickaxe up
        group.scale.set(1.4, 1.4, 1.4);
        
        return group;
    }
    
    createGrassBlockIcon() {
        const group = new THREE.Group();
        
        // Create a cube with different materials for each face
        const geometry = new THREE.BoxGeometry(0.8, 0.8, 0.8);
        
        // Materials for each face
        const materials = [
            new THREE.MeshLambertMaterial({ color: 0x8B4513 }), // right - dirt
            new THREE.MeshLambertMaterial({ color: 0x8B4513 }), // left - dirt
            new THREE.MeshLambertMaterial({ color: 0x228B22 }), // top - grass
            new THREE.MeshLambertMaterial({ color: 0x8B4513 }), // bottom - dirt
            new THREE.MeshLambertMaterial({ color: 0x8B4513 }), // front - dirt
            new THREE.MeshLambertMaterial({ color: 0x8B4513 })  // back - dirt
        ];
        
        const cube = new THREE.Mesh(geometry, materials);
        group.add(cube);
        
        // Add some grass texture on top
        const grassGeometry = new THREE.PlaneGeometry(0.8, 0.8);
        const grassMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x32CD32,
            transparent: true,
            opacity: 0.8
        });
        const grassOverlay = new THREE.Mesh(grassGeometry, grassMaterial);
        grassOverlay.rotation.x = -Math.PI / 2;
        grassOverlay.position.y = 0.401;
        group.add(grassOverlay);
        
        group.scale.set(1.2, 1.2, 1.2);
        return group;
    }
    
    createDirtBlockIcon() {
        const group = new THREE.Group();
        
        const geometry = new THREE.BoxGeometry(0.8, 0.8, 0.8);
        const material = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const cube = new THREE.Mesh(geometry, material);
        group.add(cube);
        
        // Add dirt texture spots
        const spotGeometry = new THREE.BoxGeometry(0.1, 0.1, 0.1);
        const spotMaterial = new THREE.MeshLambertMaterial({ color: 0xA0522D });
        
        // Add random dirt spots
        for (let i = 0; i < 8; i++) {
            const spot = new THREE.Mesh(spotGeometry, spotMaterial);
            spot.position.set(
                (Math.random() - 0.5) * 0.6,
                (Math.random() - 0.5) * 0.6,
                (Math.random() - 0.5) * 0.6
            );
            group.add(spot);
        }
        
        group.scale.set(1.2, 1.2, 1.2);
        return group;
    }
    
    createStoneBlockIcon() {
        const group = new THREE.Group();
        
        const geometry = new THREE.BoxGeometry(0.8, 0.8, 0.8);
        const material = new THREE.MeshLambertMaterial({ color: 0x808080 });
        const cube = new THREE.Mesh(geometry, material);
        group.add(cube);
        
        // Add stone texture variations
        const lightSpotGeometry = new THREE.BoxGeometry(0.08, 0.08, 0.08);
        const lightSpotMaterial = new THREE.MeshLambertMaterial({ color: 0xA9A9A9 });
        const darkSpotMaterial = new THREE.MeshLambertMaterial({ color: 0x696969 });
        
        // Add random stone spots
        for (let i = 0; i < 12; i++) {
            const spot = new THREE.Mesh(lightSpotGeometry, i % 2 ? lightSpotMaterial : darkSpotMaterial);
            spot.position.set(
                (Math.random() - 0.5) * 0.7,
                (Math.random() - 0.5) * 0.7,
                (Math.random() - 0.5) * 0.7
            );
            group.add(spot);
        }
        
        group.scale.set(1.2, 1.2, 1.2);
        return group;
    }
    
    createWoodBlockIcon() {
        const group = new THREE.Group();
        
        const geometry = new THREE.BoxGeometry(0.8, 0.8, 0.8);
        const material = new THREE.MeshLambertMaterial({ color: 0xDEB887 });
        const cube = new THREE.Mesh(geometry, material);
        group.add(cube);
        
        // Add wood grain lines
        const grainGeometry = new THREE.BoxGeometry(0.82, 0.02, 0.82);
        const grainMaterial = new THREE.MeshLambertMaterial({ color: 0x8B7355 });
        
        // Add horizontal grain lines
        for (let i = 0; i < 6; i++) {
            const grain = new THREE.Mesh(grainGeometry, grainMaterial);
            grain.position.y = -0.3 + (i * 0.12);
            group.add(grain);
        }
        
        group.scale.set(1.2, 1.2, 1.2);
        return group;
    }
    
    createFistIcon() {
        const group = new THREE.Group();
        
        // Create a simple fist shape
        const palmGeometry = new THREE.BoxGeometry(0.4, 0.6, 0.3);
        const skinMaterial = new THREE.MeshLambertMaterial({ color: 0xDDBEA9 });
        const palm = new THREE.Mesh(palmGeometry, skinMaterial);
        group.add(palm);
        
        // Add fingers
        const fingerGeometry = new THREE.BoxGeometry(0.08, 0.3, 0.15);
        for (let i = 0; i < 4; i++) {
            const finger = new THREE.Mesh(fingerGeometry, skinMaterial);
            finger.position.set(-0.15 + (i * 0.1), 0.35, 0);
            group.add(finger);
        }
        
        // Add thumb
        const thumbGeometry = new THREE.BoxGeometry(0.1, 0.25, 0.12);
        const thumb = new THREE.Mesh(thumbGeometry, skinMaterial);
        thumb.position.set(0.25, 0.1, 0);
        group.add(thumb);
        
        group.scale.set(1.3, 1.3, 1.3);
        return group;
    }
    
    createBowIcon() {
        const group = new THREE.Group();
        
        // Bow body (curved)
        const bowGeometry = new THREE.TorusGeometry(0.4, 0.05, 4, 8, Math.PI);
        const bowMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const bow = new THREE.Mesh(bowGeometry, bowMaterial);
        bow.rotation.z = Math.PI / 2;
        group.add(bow);
        
        // Bow string
        const stringGeometry = new THREE.CylinderGeometry(0.01, 0.01, 0.7, 4);
        const stringMaterial = new THREE.MeshLambertMaterial({ color: 0xF5DEB3 });
        const string = new THREE.Mesh(stringGeometry, stringMaterial);
        string.position.x = 0.35;
        group.add(string);
        
        group.scale.set(1.4, 1.4, 1.4);
        return group;
    }
    
    renderIcon(itemId) {
        if (this.iconCache.has(itemId)) {
            return this.iconCache.get(itemId);
        }
        
        // Clear scene
        while(this.iconScene.children.length > 2) { // Keep lights
            this.iconScene.remove(this.iconScene.children[2]);
        }
        
        let itemMesh;
        switch(itemId) {
            case 'sword':
                itemMesh = this.createSwordIcon();
                break;
            case 'health_potion':
                itemMesh = this.createPotionIcon();
                break;
            case 'pickaxe':
                itemMesh = this.createPickaxeIcon();
                break;
            case 'grass_block':
                itemMesh = this.createGrassBlockIcon();
                break;
            case 'dirt_block':
                itemMesh = this.createDirtBlockIcon();
                break;
            case 'stone_block':
                itemMesh = this.createStoneBlockIcon();
                break;
            case 'wood_block':
                itemMesh = this.createWoodBlockIcon();
                break;
            case 'fist':
                itemMesh = this.createFistIcon();
                break;
            case 'bow':
                itemMesh = this.createBowIcon();
                break;
            default:
                return null;
        }
        
        this.iconScene.add(itemMesh);
        this.iconRenderer.render(this.iconScene, this.iconCamera);
        
        // Get the rendered image as data URL
        const canvas = this.iconRenderer.domElement;
        const dataURL = canvas.toDataURL();
        
        this.iconCache.set(itemId, dataURL);
        return dataURL;
    }
}

let itemIconRenderer;

// Character Preview Renderer (Custom Image)
class CharacterPreviewRenderer {
    constructor() {
        this.canvas = document.getElementById('characterPreviewCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.animationTime = 0;
        this.characterImage = null;
        this.imageLoaded = false;

        // Load custom character image
        this.loadCharacterImage();
    }

    loadCharacterImage() {
        const sourceImage = new Image();
        sourceImage.onload = () => {
            console.log('Source image loaded, converting to Minecraft style...');
            this.convertToMinecraftStyle(sourceImage);
        };
        sourceImage.onerror = () => {
            console.error('Failed to load character image. Creating fallback...');
            this.createFallbackCharacter();
        };

        // Try to load your custom image
        sourceImage.src = 'images/mycharacter.png';
    }

    convertToMinecraftStyle(sourceImage) {
        // Create canvas to analyze the source image
        const analyzeCanvas = document.createElement('canvas');
        const analyzeCtx = analyzeCanvas.getContext('2d');
        analyzeCanvas.width = sourceImage.width;
        analyzeCanvas.height = sourceImage.height;

        // Draw source image for analysis
        analyzeCtx.drawImage(sourceImage, 0, 0);

        // Sample colors from different parts of the image
        const colors = this.analyzeImageColors(analyzeCtx, sourceImage.width, sourceImage.height);

        // Create Minecraft-style character based on analyzed colors
        this.createMinecraftCharacter(colors);
    }

    analyzeImageColors(ctx, width, height) {
        // Sample colors from different regions of the image
        const colors = {
            hair: this.getAverageColor(ctx, width * 0.2, 0, width * 0.6, height * 0.25),
            skin: this.getAverageColor(ctx, width * 0.3, height * 0.25, width * 0.4, height * 0.2),
            shirt: this.getAverageColor(ctx, width * 0.2, height * 0.45, width * 0.6, height * 0.3),
            pants: this.getAverageColor(ctx, width * 0.25, height * 0.75, width * 0.5, height * 0.25)
        };

        console.log('Analyzed colors:', colors);
        return colors;
    }

    getAverageColor(ctx, x, y, w, h) {
        try {
            const imageData = ctx.getImageData(x, y, w, h);
            const data = imageData.data;
            let r = 0, g = 0, b = 0, count = 0;

            // Sample every 4th pixel for performance
            for (let i = 0; i < data.length; i += 16) {
                r += data[i];
                g += data[i + 1];
                b += data[i + 2];
                count++;
            }

            if (count === 0) return '#8B4513'; // Fallback brown

            r = Math.round(r / count);
            g = Math.round(g / count);
            b = Math.round(b / count);

            return `rgb(${r}, ${g}, ${b})`;
        } catch (e) {
            console.warn('Color sampling failed, using fallback');
            return '#8B4513';
        }
    }

    createMinecraftCharacter(colors) {
        // Create Minecraft-style character canvas
        const mcCanvas = document.createElement('canvas');
        mcCanvas.width = 64;
        mcCanvas.height = 96;
        const mcCtx = mcCanvas.getContext('2d');

        // Disable smoothing for pixel art
        mcCtx.imageSmoothingEnabled = false;

        // Draw Minecraft-style character using analyzed colors
        // Head
        mcCtx.fillStyle = colors.skin;
        mcCtx.fillRect(20, 8, 24, 24);

        // Hair (top and sides)
        mcCtx.fillStyle = colors.hair;
        mcCtx.fillRect(18, 6, 28, 12); // Top hair
        mcCtx.fillRect(18, 18, 6, 8);  // Left side hair
        mcCtx.fillRect(40, 18, 6, 8);  // Right side hair

        // Eyes (simple black dots)
        mcCtx.fillStyle = '#000000';
        mcCtx.fillRect(24, 16, 3, 3);
        mcCtx.fillRect(37, 16, 3, 3);

        // Mouth (small line)
        mcCtx.fillRect(30, 22, 4, 2);

        // Body/Shirt
        mcCtx.fillStyle = colors.shirt;
        mcCtx.fillRect(18, 32, 28, 32);

        // Arms
        mcCtx.fillStyle = colors.skin;
        mcCtx.fillRect(10, 36, 8, 24); // Left arm
        mcCtx.fillRect(46, 36, 8, 24); // Right arm

        // Sleeves (shirt color on upper arms)
        mcCtx.fillStyle = colors.shirt;
        mcCtx.fillRect(10, 36, 8, 12); // Left sleeve
        mcCtx.fillRect(46, 36, 8, 12); // Right sleeve

        // Belt (darker version of shirt color)
        const beltColor = this.darkenColor(colors.shirt, 0.3);
        mcCtx.fillStyle = beltColor;
        mcCtx.fillRect(18, 56, 28, 4);

        // Pants
        mcCtx.fillStyle = colors.pants;
        mcCtx.fillRect(18, 60, 28, 24);

        // Legs
        mcCtx.fillStyle = colors.pants;
        mcCtx.fillRect(20, 84, 10, 12); // Left leg
        mcCtx.fillRect(34, 84, 10, 12); // Right leg

        // Shoes (darker version of pants)
        const shoeColor = this.darkenColor(colors.pants, 0.4);
        mcCtx.fillStyle = shoeColor;
        mcCtx.fillRect(18, 92, 14, 4); // Left shoe
        mcCtx.fillRect(32, 92, 14, 4); // Right shoe

        // Convert to final image
        this.characterImage = new Image();
        this.characterImage.onload = () => {
            this.imageLoaded = true;
            console.log('Minecraft-style character created!');
        };
        this.characterImage.src = mcCanvas.toDataURL();
    }

    darkenColor(color, factor) {
        // Convert color to RGB and darken it
        const rgb = color.match(/\d+/g);
        if (!rgb) return '#654321'; // Fallback

        const r = Math.max(0, Math.round(rgb[0] * (1 - factor)));
        const g = Math.max(0, Math.round(rgb[1] * (1 - factor)));
        const b = Math.max(0, Math.round(rgb[2] * (1 - factor)));

        return `rgb(${r}, ${g}, ${b})`;
    }

    createFallbackCharacter() {
        // Create a simple fallback character if image fails to load
        const tempCanvas = document.createElement('canvas');
        tempCanvas.width = 64;
        tempCanvas.height = 96;
        const tempCtx = tempCanvas.getContext('2d');

        // Simple pixel character
        tempCtx.fillStyle = '#FDBCB4'; // Skin
        tempCtx.fillRect(20, 8, 24, 24); // Head

        tempCtx.fillStyle = '#8B4513'; // Hair
        tempCtx.fillRect(18, 6, 28, 12);

        tempCtx.fillStyle = '#4169E1'; // Shirt
        tempCtx.fillRect(18, 32, 28, 32);

        tempCtx.fillStyle = '#2F4F4F'; // Pants
        tempCtx.fillRect(18, 60, 28, 24);

        this.characterImage = new Image();
        this.characterImage.src = tempCanvas.toDataURL();
        this.imageLoaded = true;
    }

    setCharacter(characterMesh) {
        // For image-based preview, we don't need the mesh
        // This method exists for compatibility
    }

    update() {
        if (!this.imageLoaded || !this.characterImage) return;

        // Clear canvas with background
        this.ctx.fillStyle = '#2a2a2a';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Disable smoothing for crisp pixels
        this.ctx.imageSmoothingEnabled = false;

        // Simple breathing animation
        this.animationTime += 0.05;
        const breathe = Math.sin(this.animationTime) * 2;

        // Calculate how to fit the image nicely in the preview
        const canvasWidth = this.canvas.width;
        const canvasHeight = this.canvas.height;
        const imgWidth = this.characterImage.width;
        const imgHeight = this.characterImage.height;

        // Scale to fit while maintaining aspect ratio
        const scale = Math.min(
            (canvasWidth - 20) / imgWidth,
            (canvasHeight - 20) / imgHeight
        );

        const scaledWidth = imgWidth * scale;
        const scaledHeight = imgHeight * scale;

        // Center the image with breathing effect
        const x = (canvasWidth - scaledWidth) / 2;
        const y = (canvasHeight - scaledHeight) / 2 + breathe;

        // Draw your custom character image
        this.ctx.drawImage(
            this.characterImage,
            x, y,
            scaledWidth, scaledHeight
        );

        // Add a nice border
        this.ctx.strokeStyle = '#A0522D';
        this.ctx.lineWidth = 2;
        this.ctx.strokeRect(1, 1, this.canvas.width - 2, this.canvas.height - 2);

        // Add a subtle glow effect around the character
        this.ctx.shadowColor = 'rgba(160, 82, 45, 0.3)';
        this.ctx.shadowBlur = 10;
        this.ctx.strokeRect(1, 1, this.canvas.width - 2, this.canvas.height - 2);
        this.ctx.shadowBlur = 0;
    }
}

let characterPreviewRenderer;

// Enhanced Inventory system
class Inventory {
    constructor() {
        this.slots = new Array(20).fill(null); // 20 inventory slots
        this.hotbarSlots = new Array(6).fill(null); // 6 separate hotbar slots
        this.selectedHotbarSlot = 0; // Currently selected hotbar slot (0-5)
        this.selectedSlot = -1;
        this.currentWeapon = ITEMS.FIST;
        this.kills = 0;
        this.score = 0;
        this.isOpen = false;

        this.initializeInventoryUI();
        this.setupInventoryControls();
        this.initializeCharacterPreview();
        this.initializeHotbar();
        this.setupHotbarControls();
        this.setupDragAndDrop();
    }

    initializeInventoryUI() {
        const grid = document.getElementById('inventoryGrid');
        grid.innerHTML = '';

        for (let i = 0; i < 20; i++) {
            const slot = document.createElement('div');
            slot.className = 'inventory-slot';
            slot.dataset.slotIndex = i;
            slot.draggable = true; // Make slots draggable

            // Add item icon container
            const iconContainer = document.createElement('div');
            iconContainer.className = 'item-icon-container';
            slot.appendChild(iconContainer);

            // Add item count
            const count = document.createElement('div');
            count.className = 'item-count';
            slot.appendChild(count);

            // Test drag events
            slot.addEventListener('dragstart', (e) => {
                console.log(`Drag started on slot ${i}`);
            });

            grid.appendChild(slot);
        }

        this.updateInventoryDisplay();
    }

    initializeHotbar() {
        // Create main hotbar (always visible)
        const hotbar = document.getElementById('hotbar');
        hotbar.innerHTML = '';

        for (let i = 0; i < this.hotbarSlots.length; i++) {
            const slot = document.createElement('div');
            slot.className = 'hotbar-slot';
            slot.dataset.slotIndex = i;
            slot.draggable = true; // Make hotbar slots draggable

            // Add number indicator
            const number = document.createElement('div');
            number.className = 'hotbar-number';
            number.textContent = i + 1;
            slot.appendChild(number);

            // Add item icon container
            const iconContainer = document.createElement('div');
            iconContainer.className = 'item-icon-container';
            slot.appendChild(iconContainer);

            // Add item count
            const count = document.createElement('div');
            count.className = 'item-count';
            slot.appendChild(count);

            hotbar.appendChild(slot);
        }

        // Create inventory hotbar (in inventory panel)
        const inventoryHotbar = document.getElementById('inventoryHotbar');
        inventoryHotbar.innerHTML = '';

        for (let i = 0; i < this.hotbarSlots.length; i++) {
            const slot = document.createElement('div');
            slot.className = 'inventory-hotbar-slot';
            slot.dataset.slotIndex = i;
            slot.draggable = true; // Make inventory hotbar slots draggable

            // Add item icon container
            const iconContainer = document.createElement('div');
            iconContainer.className = 'item-icon-container';
            slot.appendChild(iconContainer);

            // Add item count
            const count = document.createElement('div');
            count.className = 'item-count';
            slot.appendChild(count);

            inventoryHotbar.appendChild(slot);
        }

        // Set initial selection
        this.updateHotbarSelection();
    }

    setupHotbarControls() {
        // Number key controls (1-6)
        document.addEventListener('keydown', (e) => {
            if (this.isOpen) return; // Don't handle hotbar keys when inventory is open

            const num = parseInt(e.key);
            if (num >= 1 && num <= this.hotbarSlots) {
                this.selectHotbarSlot(num - 1);
                e.preventDefault();
            }
        });

        // Click controls for hotbar
        document.getElementById('hotbar').addEventListener('click', (e) => {
            const slot = e.target.closest('.hotbar-slot');
            if (slot) {
                const slotIndex = parseInt(slot.dataset.slotIndex);
                this.selectHotbarSlot(slotIndex);
            }
        });

        // Click controls for inventory hotbar
        document.getElementById('inventoryHotbar').addEventListener('click', (e) => {
            const slot = e.target.closest('.inventory-hotbar-slot');
            if (slot) {
                const slotIndex = parseInt(slot.dataset.slotIndex);
                this.selectHotbarSlot(slotIndex);
            }
        });
    }

    selectHotbarSlot(index) {
        if (index < 0 || index >= this.hotbarSlots.length) return;

        this.selectedHotbarSlot = index;
        this.updateHotbarSelection();

        // Update current weapon based on selected hotbar slot
        const item = this.hotbarSlots[index];
        if (item && item.item) {
            this.currentWeapon = item.item || ITEMS.FIST;
        } else {
            this.currentWeapon = ITEMS.FIST;
        }

        console.log(`Selected hotbar slot ${index + 1}:`, item ? item.id : 'empty');
    }

    updateHotbarSelection() {
        // Update main hotbar selection
        const hotbarSlots = document.querySelectorAll('.hotbar-slot');
        hotbarSlots.forEach((slot, index) => {
            slot.classList.toggle('selected', index === this.selectedHotbarSlot);
        });

        // Update inventory hotbar selection
        const inventoryHotbarSlots = document.querySelectorAll('.inventory-hotbar-slot');
        inventoryHotbarSlots.forEach((slot, index) => {
            slot.classList.toggle('selected', index === this.selectedHotbarSlot);
        });
    }

    updateHotbarDisplay() {
        // Update main hotbar
        const hotbarSlots = document.querySelectorAll('.hotbar-slot');
        hotbarSlots.forEach((slotElement, index) => {
            const item = this.hotbarSlots[index]; // Use separate hotbar slots
            const iconContainer = slotElement.querySelector('.item-icon-container');
            const countElement = slotElement.querySelector('.item-count');

            // Clear slot first
            iconContainer.innerHTML = '';
            countElement.textContent = '';
            countElement.style.display = 'none';

            if (item && item.id && itemIconRenderer) {
                console.log(`Rendering hotbar icon for ${item.id}`);
                try {
                    // Show item icon using the item ID (convert to lowercase)
                    const img = document.createElement('img');
                    img.className = 'item-icon';
                    img.src = itemIconRenderer.renderIcon(item.id.toLowerCase());
                    img.onerror = () => {
                        console.error(`Failed to load icon for ${item.id}`);
                        iconContainer.innerHTML = '<div style="color: red; font-size: 10px;">?</div>';
                    };
                    iconContainer.appendChild(img);

                    // Show count if > 1
                    if (item.count > 1) {
                        countElement.textContent = item.count;
                        countElement.style.display = 'block';
                    }
                } catch (error) {
                    console.error(`Error rendering icon for ${item.id}:`, error);
                    iconContainer.innerHTML = '<div style="color: red; font-size: 10px;">!</div>';
                }
            }
        });

        // Update inventory hotbar
        const inventoryHotbarSlots = document.querySelectorAll('.inventory-hotbar-slot');
        inventoryHotbarSlots.forEach((slotElement, index) => {
            const item = this.hotbarSlots[index]; // Use separate hotbar slots
            const iconContainer = slotElement.querySelector('.item-icon-container');
            const countElement = slotElement.querySelector('.item-count');

            // Clear slot first
            iconContainer.innerHTML = '';
            countElement.textContent = '';
            countElement.style.display = 'none';

            if (item && item.id && itemIconRenderer) {
                try {
                    // Show item icon using the item ID (convert to lowercase)
                    const img = document.createElement('img');
                    img.className = 'item-icon';
                    img.src = itemIconRenderer.renderIcon(item.id.toLowerCase());
                    img.onerror = () => {
                        console.error(`Failed to load inventory hotbar icon for ${item.id}`);
                        iconContainer.innerHTML = '<div style="color: red; font-size: 10px;">?</div>';
                    };
                    iconContainer.appendChild(img);

                    // Show count if > 1
                    if (item.count > 1) {
                        countElement.textContent = item.count;
                        countElement.style.display = 'block';
                    }
                } catch (error) {
                    console.error(`Error rendering inventory hotbar icon for ${item.id}:`, error);
                    iconContainer.innerHTML = '<div style="color: red; font-size: 10px;">!</div>';
                }
            }
        });
    }

    initializeCharacterPreview() {
        // Initialize character preview renderer
        characterPreviewRenderer = new CharacterPreviewRenderer();

        // Set the player character for preview
        if (window.player && window.player.mesh) {
            characterPreviewRenderer.setCharacter(window.player.mesh);
        }
    }

    setupInventoryControls() {
        document.addEventListener('keydown', (e) => {
            if (e.code === 'KeyI' || (e.code === 'Escape' && this.isOpen)) {
                e.preventDefault();
                this.toggleInventory();
            }
        });
    }

    toggleInventory() {
        this.isOpen = !this.isOpen;
        const panel = document.getElementById('inventoryPanel');
        panel.style.display = this.isOpen ? 'block' : 'none';

        if (this.isOpen) {
            this.updateInventoryDisplay();
            // Update character preview
            if (characterPreviewRenderer && window.player && window.player.mesh) {
                characterPreviewRenderer.setCharacter(window.player.mesh);
            }
            // Release pointer lock when inventory is open
            if (document.pointerLockElement) {
                document.exitPointerLock();
            }
        }
    }

    addItem(itemId, count = 1) {
        const item = ITEMS[itemId];
        if (!item) return false;

        // Try to stack with existing items first
        for (let i = 0; i < this.slots.length; i++) {
            if (this.slots[i] && this.slots[i].id === itemId) {
                this.slots[i].count += count;
                this.updateInventoryDisplay();
                return true;
            }
        }

        // Find empty slot
        for (let i = 0; i < this.slots.length; i++) {
            if (!this.slots[i]) {
                this.slots[i] = {
                    id: itemId,
                    item: item,
                    count: count
                };
                this.updateInventoryDisplay();
                return true;
            }
        }

        return false; // Inventory full
    }

    removeItem(slotIndex, count = 1) {
        if (!this.slots[slotIndex]) return false;

        this.slots[slotIndex].count -= count;
        if (this.slots[slotIndex].count <= 0) {
            this.slots[slotIndex] = null;
            if (this.selectedSlot === slotIndex) {
                this.selectedSlot = -1;
                this.updateSelectedItemInfo();
            }
        }

        this.updateInventoryDisplay();
        return true;
    }

    // Old selectSlot method removed - replaced with drag and drop

    useConsumable(slotIndex) {
        const slotItem = this.slots[slotIndex];
        if (!slotItem || slotItem.item.type !== ITEM_TYPES.CONSUMABLE) return;

        if (slotItem.item.id === 'health_potion') {
            if (window.player) {
                window.player.heal(slotItem.item.healAmount);
                this.removeItem(slotIndex, 1);
                window.player.showMessage(`+${slotItem.item.healAmount} Health!`, 'green');
            }
        }
    }

    setupDragAndDrop() {
        this.draggedItem = null;
        this.draggedFromSlot = -1;
        this.draggedFromType = null; // 'inventory' or 'hotbar'

        // Setup drag and drop for inventory slots
        this.setupInventoryDragDrop();

        // Setup drag and drop for hotbar slots
        this.setupHotbarDragDrop();
    }

    setupInventoryDragDrop() {
        const inventoryGrid = document.getElementById('inventoryGrid');

        inventoryGrid.addEventListener('dragstart', (e) => {
            console.log('Drag start event triggered', e.target);
            const slot = e.target.closest('.inventory-slot');
            if (!slot) {
                console.log('No slot found');
                return;
            }

            const slotIndex = parseInt(slot.dataset.slotIndex);
            const item = this.slots[slotIndex];

            console.log(`Slot ${slotIndex}, item:`, item);

            if (!item) {
                console.log('No item in slot, preventing drag');
                e.preventDefault();
                return;
            }

            this.draggedItem = { ...item };
            this.draggedFromSlot = slotIndex;
            this.draggedFromType = 'inventory';

            // Create drag image
            const dragImage = this.createDragImage(item);
            e.dataTransfer.setDragImage(dragImage, 32, 32);
            e.dataTransfer.effectAllowed = 'move';

            // Add visual feedback
            slot.classList.add('dragging');

            console.log(`Started dragging ${item.id} from inventory slot ${slotIndex}`);
        });

        inventoryGrid.addEventListener('dragend', (e) => {
            const slot = e.target.closest('.inventory-slot');
            if (slot) {
                slot.classList.remove('dragging');
            }

            // Reset drag state
            this.draggedItem = null;
            this.draggedFromSlot = -1;
            this.draggedFromType = null;
        });

        inventoryGrid.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';
        });

        inventoryGrid.addEventListener('drop', (e) => {
            e.preventDefault();

            const slot = e.target.closest('.inventory-slot');
            if (!slot || !this.draggedItem) return;

            const targetSlotIndex = parseInt(slot.dataset.slotIndex);
            this.handleDrop(targetSlotIndex, 'inventory');
        });
    }

    setupHotbarDragDrop() {
        // Main hotbar drag and drop
        const hotbar = document.getElementById('hotbar');

        hotbar.addEventListener('dragstart', (e) => {
            const slot = e.target.closest('.hotbar-slot');
            if (!slot) return;

            const slotIndex = parseInt(slot.dataset.slotIndex);
            const item = this.hotbarSlots[slotIndex]; // Use hotbar slots

            if (!item) {
                e.preventDefault();
                return;
            }

            this.draggedItem = { ...item };
            this.draggedFromSlot = slotIndex;
            this.draggedFromType = 'hotbar';

            // Create drag image
            const dragImage = this.createDragImage(item);
            e.dataTransfer.setDragImage(dragImage, 32, 32);
            e.dataTransfer.effectAllowed = 'move';

            slot.classList.add('dragging');

            console.log(`Started dragging ${item.id} from hotbar slot ${slotIndex}`);
        });

        hotbar.addEventListener('dragend', (e) => {
            const slot = e.target.closest('.hotbar-slot');
            if (slot) {
                slot.classList.remove('dragging');
            }

            this.draggedItem = null;
            this.draggedFromSlot = -1;
            this.draggedFromType = null;
        });

        hotbar.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';

            const slot = e.target.closest('.hotbar-slot');
            if (slot) {
                slot.classList.add('drag-over');
            }
        });

        hotbar.addEventListener('dragleave', (e) => {
            const slot = e.target.closest('.hotbar-slot');
            if (slot) {
                slot.classList.remove('drag-over');
            }
        });

        hotbar.addEventListener('drop', (e) => {
            e.preventDefault();

            const slot = e.target.closest('.hotbar-slot');
            if (!slot || !this.draggedItem) return;

            slot.classList.remove('drag-over');

            const targetSlotIndex = parseInt(slot.dataset.slotIndex);
            this.handleDrop(targetSlotIndex, 'hotbar');
        });

        // Inventory hotbar drag and drop (same logic)
        const inventoryHotbar = document.getElementById('inventoryHotbar');

        inventoryHotbar.addEventListener('dragstart', (e) => {
            const slot = e.target.closest('.inventory-hotbar-slot');
            if (!slot) return;

            const slotIndex = parseInt(slot.dataset.slotIndex);
            const item = this.hotbarSlots[slotIndex]; // Use hotbar slots

            if (!item) {
                e.preventDefault();
                return;
            }

            this.draggedItem = { ...item };
            this.draggedFromSlot = slotIndex;
            this.draggedFromType = 'hotbar'; // Still hotbar type since it's hotbar data

            // Create drag image
            const dragImage = this.createDragImage(item);
            e.dataTransfer.setDragImage(dragImage, 32, 32);
            e.dataTransfer.effectAllowed = 'move';

            slot.classList.add('dragging');

            console.log(`Started dragging ${item.id} from inventory hotbar slot ${slotIndex}`);
        });

        inventoryHotbar.addEventListener('dragend', (e) => {
            const slot = e.target.closest('.inventory-hotbar-slot');
            if (slot) {
                slot.classList.remove('dragging');
            }

            this.draggedItem = null;
            this.draggedFromSlot = -1;
            this.draggedFromType = null;
        });

        inventoryHotbar.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';

            const slot = e.target.closest('.inventory-hotbar-slot');
            if (slot) {
                slot.classList.add('drag-over');
            }
        });

        inventoryHotbar.addEventListener('dragleave', (e) => {
            const slot = e.target.closest('.inventory-hotbar-slot');
            if (slot) {
                slot.classList.remove('drag-over');
            }
        });

        inventoryHotbar.addEventListener('drop', (e) => {
            e.preventDefault();

            const slot = e.target.closest('.inventory-hotbar-slot');
            if (!slot || !this.draggedItem) return;

            slot.classList.remove('drag-over');

            const targetSlotIndex = parseInt(slot.dataset.slotIndex);
            this.handleDrop(targetSlotIndex, 'hotbar');
        });
    }

    createDragImage(item) {
        // Create a canvas for the drag image
        const canvas = document.createElement('canvas');
        canvas.width = 64;
        canvas.height = 64;
        const ctx = canvas.getContext('2d');

        // Set transparent background
        ctx.clearRect(0, 0, 64, 64);

        // Get item icon from renderer using item ID (convert to lowercase)
        if (itemIconRenderer) {
            // Create a temporary image from the rendered icon
            const iconDataUrl = itemIconRenderer.renderIcon(item.id.toLowerCase());
            const iconImg = new Image();
            iconImg.onload = () => {
                ctx.drawImage(iconImg, 8, 8, 48, 48);
            };
            iconImg.src = iconDataUrl;
        }

        // Add count if > 1
        if (item.count > 1) {
            ctx.fillStyle = 'white';
            ctx.strokeStyle = 'black';
            ctx.font = 'bold 12px Courier New';
            ctx.lineWidth = 2;
            ctx.strokeText(item.count.toString(), 45, 55);
            ctx.fillText(item.count.toString(), 45, 55);
        }

        return canvas;
    }

    handleDrop(targetSlotIndex, targetType) {
        if (!this.draggedItem || this.draggedFromSlot === -1) return;

        console.log(`Dropping ${this.draggedItem.id} to ${targetType} slot ${targetSlotIndex}`);

        // Get source and target items based on type
        const sourceItem = this.draggedItem;
        let targetItem;

        if (targetType === 'hotbar') {
            targetItem = this.hotbarSlots[targetSlotIndex];
        } else {
            targetItem = this.slots[targetSlotIndex];
        }

        // Handle different drop scenarios
        if (!targetItem) {
            // Dropping into empty slot
            this.moveItemToSlot(sourceItem, targetSlotIndex, targetType);
        } else if (targetItem.id === sourceItem.id) {
            // Stacking same items
            this.stackItems(sourceItem, targetSlotIndex, targetType);
        } else {
            // Swapping different items
            this.swapItems(this.draggedFromSlot, targetSlotIndex, this.draggedFromType, targetType);
        }

        // Update displays
        this.updateInventoryDisplay();
        this.updateHotbarDisplay();

        // Update selected hotbar slot if dropping to hotbar
        if (targetType === 'hotbar') {
            this.selectHotbarSlot(targetSlotIndex);
        }
    }

    moveItemToSlot(item, targetSlotIndex, targetType) {
        // Move item to target slot
        if (targetType === 'hotbar') {
            this.hotbarSlots[targetSlotIndex] = { ...item };
        } else {
            this.slots[targetSlotIndex] = { ...item };
        }

        // Clear source slot
        if (this.draggedFromType === 'hotbar') {
            this.hotbarSlots[this.draggedFromSlot] = null;
        } else {
            this.slots[this.draggedFromSlot] = null;
        }

        console.log(`Moved ${item.id} to ${targetType} slot ${targetSlotIndex}`);
    }

    stackItems(sourceItem, targetSlotIndex, targetType) {
        let targetItem;

        if (targetType === 'hotbar') {
            targetItem = this.hotbarSlots[targetSlotIndex];
        } else {
            targetItem = this.slots[targetSlotIndex];
        }

        // Add source count to target
        targetItem.count += sourceItem.count;

        // Clear source slot
        if (this.draggedFromType === 'hotbar') {
            this.hotbarSlots[this.draggedFromSlot] = null;
        } else {
            this.slots[this.draggedFromSlot] = null;
        }

        console.log(`Stacked ${sourceItem.count} ${sourceItem.id} with existing stack`);
    }

    swapItems(sourceSlotIndex, targetSlotIndex, sourceType, targetType) {
        // Get source and target items
        let sourceItem, targetItem;

        if (sourceType === 'hotbar') {
            sourceItem = this.hotbarSlots[sourceSlotIndex];
        } else {
            sourceItem = this.slots[sourceSlotIndex];
        }

        if (targetType === 'hotbar') {
            targetItem = this.hotbarSlots[targetSlotIndex];
        } else {
            targetItem = this.slots[targetSlotIndex];
        }

        // Swap the items
        if (sourceType === 'hotbar') {
            this.hotbarSlots[sourceSlotIndex] = targetItem;
        } else {
            this.slots[sourceSlotIndex] = targetItem;
        }

        if (targetType === 'hotbar') {
            this.hotbarSlots[targetSlotIndex] = sourceItem;
        } else {
            this.slots[targetSlotIndex] = sourceItem;
        }

        console.log(`Swapped items between ${sourceType} slot ${sourceSlotIndex} and ${targetType} slot ${targetSlotIndex}`);
    }

    updateInventoryDisplay() {
        console.log('Updating inventory display, slots:', this.slots);
        const slots = document.querySelectorAll('.inventory-slot');
        console.log('Found DOM slots:', slots.length);

        slots.forEach((slot, index) => {
            const slotData = this.slots[index];
            const iconContainer = slot.querySelector('.item-icon-container');
            const countElement = slot.querySelector('.item-count');

            console.log(`Slot ${index}:`, slotData, 'containers:', iconContainer, countElement);

            // Clear containers
            if (iconContainer) iconContainer.innerHTML = '';
            if (countElement) {
                countElement.textContent = '';
                countElement.style.display = 'none';
            }

            slot.className = 'inventory-slot';

            if (slotData) {
                slot.classList.add('has-item');

                // Use 3D rendered icons for all items now
                if (itemIconRenderer && iconContainer) {
                    const iconImg = document.createElement('img');
                    iconImg.className = 'item-icon';
                    iconImg.src = itemIconRenderer.renderIcon(slotData.item.id.toLowerCase());
                    iconImg.style.width = '48px';
                    iconImg.style.height = '48px';
                    iconImg.style.imageRendering = 'pixelated';
                    iconContainer.appendChild(iconImg);
                }

                if (slotData.count > 1 && countElement) {
                    countElement.textContent = slotData.count;
                    countElement.style.display = 'block';
                }
            }

            if (index === this.selectedSlot) {
                slot.classList.add('selected');
            }
        });

        // Also update hotbar display
        this.updateHotbarDisplay();
    }

    updateSelectedItemInfo() {
        const nameElement = document.getElementById('selectedItemName');
        const descElement = document.getElementById('selectedItemDesc');

        if (this.selectedSlot >= 0 && this.slots[this.selectedSlot]) {
            const slotData = this.slots[this.selectedSlot];
            nameElement.textContent = `${slotData.item.name} x${slotData.count}`;
            descElement.textContent = slotData.item.description;
        } else {
            nameElement.textContent = 'Select an item to see details';
            descElement.textContent = '';
        }
    }

    addKill() {
        this.kills++;
        this.score += 100;
        this.updateUI();
    }

    getWeaponDamage() {
        return this.currentWeapon.damage || 25;
    }

    getWeaponName() {
        return this.currentWeapon.name || 'Fist';
    }

    updateUI() {
        document.getElementById('currentWeapon').textContent = this.getWeaponName();
        document.getElementById('swordCount').textContent = this.countItem('sword');
        document.getElementById('killCount').textContent = this.kills;
        document.getElementById('score').textContent = this.score;

        // Update hotbar display
        this.updateHotbarDisplay();
        this.updateHotbarSelection();
    }

    countItem(itemId) {
        let total = 0;
        for (const slot of this.slots) {
            if (slot && slot.id === itemId) {
                total += slot.count;
            }
        }
        return total;
    }
}
