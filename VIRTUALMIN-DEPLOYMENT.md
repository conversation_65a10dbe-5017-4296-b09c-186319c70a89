# 🚀 Beetle3D VirtualMin Deployment Guide

## 📦 VirtualMin-Optimized Package Ready!

Your complete Minecraft-style game is ready for deployment on your VirtualMin server at **beetle3d.com**.

## 🎯 Quick Deployment Steps

### 1. **Upload Files to Server**
```bash
# Download: beetle3d-virtualmin-deployment.tar.gz
# Upload to your VirtualMin server
# Extract in your domain directory
tar -xzf beetle3d-virtualmin-deployment.tar.gz
```

### 2. **Run VirtualMin Setup Script**
```bash
chmod +x virtualmin-setup.sh
./virtualmin-setup.sh
```

The script will:
- ✅ **Detect Node.js** (handles VirtualMin-specific paths)
- ✅ **Test database connection** (MariaDB/MySQL)
- ✅ **Install dependencies** (npm install)
- ✅ **Setup database schema** (automatic SQL import)
- ✅ **Start the server** (on port 3001)

### 3. **Access Your Game**
- **Game URL**: http://beetle3d.com:3001
- **API URL**: http://beetle3d.com:3001/api

## 🔧 VirtualMin-Specific Features

### **Smart Detection**
- Finds Node.js in VirtualMin-specific locations
- Handles both `node` and `nodejs` commands
- Detects MariaDB/MySQL automatically
- Works with VirtualMin's package management

### **Database Setup**
- Automatically creates database if needed
- Imports schema from `database.sql`
- Tests connection with your credentials:
  - Database: `beetle`
  - User: `beetle`
  - Password: `4SrTF6CYdFhKYhG`

### **Error Handling**
- Clear error messages for missing components
- Installation suggestions for VirtualMin
- Fallback options for different configurations

## 🎮 What You Get

### **Complete Game Features**
- ✅ **3D Minecraft-style world** with terrain generation
- ✅ **Player movement** (WASD + mouse look)
- ✅ **Block placement** (right-click with highlighting)
- ✅ **Inventory system** (20 slots + 6-slot hotbar)
- ✅ **Drag & drop** between inventory and hotbar
- ✅ **Enemy combat** (orc spawning and fighting)
- ✅ **Custom character** (your uploaded image)

### **Database Backend**
- ✅ **User authentication** (register/login system)
- ✅ **Game saves** (progress stored in database)
- ✅ **Cross-device sync** (play from anywhere)
- ✅ **Statistics tracking** (kills, playtime, etc.)
- ✅ **Multiplayer ready** (architecture supports rooms)

## 🔍 Troubleshooting

### **If Node.js Not Found:**
```bash
# Via VirtualMin Panel:
# System Settings → Software Packages → Search "nodejs"

# Via command line:
sudo apt update && sudo apt install nodejs npm

# Via NodeSource (latest):
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
sudo apt-get install -y nodejs
```

### **If Database Issues:**
```bash
# Create database manually:
sudo mysql
CREATE DATABASE beetle;
CREATE USER 'beetle'@'localhost' IDENTIFIED BY '4SrTF6CYdFhKYhG';
GRANT ALL PRIVILEGES ON beetle.* TO 'beetle'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### **If Port 3001 Blocked:**
- Open port 3001 in VirtualMin firewall
- Or modify `server/.env` to use different port

## 📁 File Structure

```
beetle3d-virtualmin-deployment/
├── index.html              # Main game file
├── js/                     # Game JavaScript
├── images/                 # Game assets
├── server/                 # Backend API
│   ├── package.json       # Dependencies
│   ├── server.js          # Main server
│   ├── database.sql       # Database schema
│   └── .env               # Configuration
├── virtualmin-setup.sh    # VirtualMin setup script
└── deployment-instructions.md
```

## 🚀 Ready to Deploy!

1. **Upload** `beetle3d-virtualmin-deployment.tar.gz` to your server
2. **Extract** the files
3. **Run** `./virtualmin-setup.sh`
4. **Visit** http://beetle3d.com:3001

Your complete Minecraft-style game with database backend will be live! 🎮✨

## 🔮 Next Steps

After deployment:
- Test user registration and login
- Test game saving/loading
- Plan multiplayer features (Ctrl+J)
- Add more game content
- Monitor server performance

Your Beetle3D game is ready for production on VirtualMin! 🚀
