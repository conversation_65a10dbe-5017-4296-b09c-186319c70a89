#!/bin/bash

# Automatic Game Backup System
# Creates compressed archives of all game files

# Get current timestamp
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="backup-game-${TIMESTAMP}"

# Create backup directory if it doesn't exist
mkdir -p file-saves

# List of files and directories to backup
FILES_TO_BACKUP=(
    "index.html"
    "js/"
    "images/"
    "server/"
)

echo "🗜️ Creating game backup: ${BACKUP_NAME}"

# Create tar.gz archive (better compression than zip)
tar -czf "file-saves/${BACKUP_NAME}.tar.gz" "${FILES_TO_BACKUP[@]}" 2>/dev/null

if [ $? -eq 0 ]; then
    echo "✅ Backup created successfully: file-saves/${BACKUP_NAME}.tar.gz"
    
    # Show archive contents
    echo "📁 Archive contents:"
    tar -tzf "file-saves/${BACKUP_NAME}.tar.gz" | head -10
    if [ $(tar -tzf "file-saves/${BACKUP_NAME}.tar.gz" | wc -l) -gt 10 ]; then
        echo "... and $(( $(tar -tzf "file-saves/${BACKUP_NAME}.tar.gz" | wc -l) - 10 )) more files"
    fi
    
    # Show file size
    SIZE=$(du -h "file-saves/${BACKUP_NAME}.tar.gz" | cut -f1)
    echo "📦 Archive size: ${SIZE}"
    
    # Keep only last 5 backups to save space
    echo "🧹 Cleaning old backups (keeping last 5)..."
    cd file-saves
    ls -t backup-game-*.tar.gz | tail -n +6 | xargs -r rm
    echo "📊 Current backups:"
    ls -lah backup-game-*.tar.gz 2>/dev/null || echo "No previous backups found"
    
else
    echo "❌ Backup failed!"
    exit 1
fi

echo "🎯 Backup complete!"
