// === ENEMY SYSTEM ===
// Orc enemies with AI and combat

// Simple Orc enemy class
class Orc {
    constructor(x, z) {
        this.position = new THREE.Vector3(x, getHeightAt(x, z) + 1, z);
        this.velocity = new THREE.Vector3(0, 0, 0);
        this.health = 100;
        this.maxHealth = 100;
        this.speed = 2.5;
        this.attackRange = 3;
        this.detectionRange = 15;
        this.attackCooldown = 0;
        this.attackDamage = 15;
        this.onGround = false;
        this.state = 'idle';
        this.mesh = null;
        this.healthBar = null;
        this.isMoving = false;
        this.variant = this.getRandomVariant();

        this.createMesh();
    }

    getRandomVariant() {
        const variants = ['warrior', 'archer', 'chief'];
        const weights = [0.6, 0.3, 0.1]; // 60% warrior, 30% archer, 10% chief

        const random = Math.random();
        let cumulative = 0;

        for (let i = 0; i < variants.length; i++) {
            cumulative += weights[i];
            if (random <= cumulative) {
                return variants[i];
            }
        }

        return 'warrior'; // fallback
    }

    createMesh() {
        // Create detailed orc character with variant
        console.log(`Creating ${this.variant} orc`);
        this.mesh = createDetailedOrcMesh(this.variant);
        this.mesh.position.copy(this.position);
        scene.add(this.mesh);

        // Create simple health bar
        this.createHealthBar();
    }
    
    createHealthBar() {
        const barWidth = 1.5;
        const barHeight = 0.1;
        
        // Background bar (red)
        const bgGeometry = new THREE.PlaneGeometry(barWidth, barHeight);
        const bgMaterial = new THREE.MeshBasicMaterial({ color: 0xff0000 });
        const bgBar = new THREE.Mesh(bgGeometry, bgMaterial);
        
        // Health bar (green)
        const healthGeometry = new THREE.PlaneGeometry(barWidth, barHeight);
        const healthMaterial = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
        const healthBar = new THREE.Mesh(healthGeometry, healthMaterial);
        
        this.healthBar = new THREE.Group();
        this.healthBar.add(bgBar);
        this.healthBar.add(healthBar);
        this.healthBar.position.set(0, 2.5, 0);
        
        this.mesh.add(this.healthBar);
        this.healthBarMesh = healthBar;
    }
    
    update(deltaTime, playerPosition) {
        if (this.health <= 0) return;
        
        // Apply gravity
        this.velocity.y -= 25 * deltaTime;
        
        // Calculate distance to player
        const distanceToPlayer = this.position.distanceTo(playerPosition);
        
        // Update state based on distance
        if (distanceToPlayer <= this.attackRange) {
            this.state = 'attacking';
        } else if (distanceToPlayer <= this.detectionRange) {
            this.state = 'chasing';
        } else {
            this.state = 'idle';
        }
        
        // Behavior based on state
        switch (this.state) {
            case 'chasing':
                this.chasePlayer(playerPosition, deltaTime);
                this.isMoving = true;
                break;
            case 'attacking':
                this.attackPlayer(playerPosition, deltaTime);
                this.isMoving = false;
                break;
            default:
                this.isMoving = false;
        }
        
        // Update position
        this.position.add(this.velocity.clone().multiplyScalar(deltaTime));
        
        // Ground collision
        const groundHeight = getHeightAt(Math.floor(this.position.x), Math.floor(this.position.z)) + 1;
        if (this.position.y <= groundHeight) {
            this.position.y = groundHeight;
            this.velocity.y = 0;
            this.onGround = true;
        } else {
            this.onGround = false;
        }
        
        // Update mesh position and animation
        if (this.mesh) {
            this.mesh.position.copy(this.position);

            // Make orc face the player
            if (this.state !== 'idle') {
                this.mesh.lookAt(playerPosition.x, this.position.y, playerPosition.z);
            }

            // Animate character
            const time = Date.now() * 0.001;
            animateCharacter(this.mesh, time, this.isMoving);
        }
        
        // Update health bar
        this.updateHealthBar();
        
        // Update attack cooldown
        if (this.attackCooldown > 0) {
            this.attackCooldown -= deltaTime;
        }
    }
    
    chasePlayer(playerPosition, deltaTime) {
        const direction = new THREE.Vector3()
            .subVectors(playerPosition, this.position)
            .normalize();
        
        this.velocity.x = direction.x * this.speed;
        this.velocity.z = direction.z * this.speed;
    }
    
    attackPlayer(playerPosition, deltaTime) {
        if (this.attackCooldown <= 0) {
            // Deal damage to player
            if (window.player) {
                window.player.takeDamage(this.attackDamage);
            }
            this.attackCooldown = 2.0;
            
            // Visual attack effect
            if (this.mesh) {
                this.mesh.scale.set(1.2, 1.2, 1.2);
                setTimeout(() => {
                    if (this.mesh) this.mesh.scale.set(1, 1, 1);
                }, 200);
            }
        }
        
        // Stop moving when attacking
        this.velocity.x = 0;
        this.velocity.z = 0;
    }
    
    takeDamage(damage) {
        this.health -= damage;
        if (this.health <= 0) {
            this.die();
        }
    }
    
    updateHealthBar() {
        if (this.healthBar && this.healthBarMesh) {
            const healthPercent = this.health / this.maxHealth;
            this.healthBarMesh.scale.x = healthPercent;
            this.healthBarMesh.position.x = -(1.5 * (1 - healthPercent)) / 2;
            
            // Make health bar face camera
            this.healthBar.lookAt(camera.position);
        }
    }
    
    die() {
        if (this.mesh) {
            scene.remove(this.mesh);
            this.mesh = null;
        }
        this.health = 0;
    }
}
