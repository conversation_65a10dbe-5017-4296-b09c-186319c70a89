// === FIXED WORLD SYSTEM ===
// 500x500 block designed world with specific regions and locations

class FixedWorld {
    constructor() {
        this.worldSize = 500; // 500x500 blocks
        this.centerX = 0;
        this.centerZ = 0;
        this.worldBounds = {
            minX: this.centerX - this.worldSize / 2,
            maxX: this.centerX + this.worldSize / 2,
            minZ: this.centerZ - this.worldSize / 2,
            maxZ: this.centerZ + this.worldSize / 2
        };
        
        // Define world regions
        this.regions = this.defineRegions();
        
        // Define specific locations
        this.locations = this.defineLocations();
        
        // Define roads/paths
        this.roads = this.defineRoads();
        
        console.log('🗺️ Fixed world initialized (500x500 blocks)');
        console.log('🗺️ World bounds:', this.worldBounds);
    }
    
    defineRegions() {
        return {
            // Central spawn area - peaceful plains
            SPAWN: {
                center: { x: 0, z: 0 },
                radius: 50,
                biome: 'plains',
                description: 'Starting village and tutorial area',
                difficulty: 1
            },
            
            // North - Forest region
            NORTH_FOREST: {
                center: { x: 0, z: -150 },
                radius: 80,
                biome: 'forest',
                description: 'Dense woods with animals and lumber',
                difficulty: 2
            },
            
            // South - Desert region  
            SOUTH_DESERT: {
                center: { x: 0, z: 150 },
                radius: 70,
                biome: 'desert',
                description: 'Harsh desert with rare resources',
                difficulty: 4
            },
            
            // East - Mountain region
            EAST_MOUNTAINS: {
                center: { x: 150, z: 0 },
                radius: 75,
                biome: 'mountains',
                description: 'Rocky peaks with mining opportunities',
                difficulty: 3
            },
            
            // West - Tundra region
            WEST_TUNDRA: {
                center: { x: -150, z: 0 },
                radius: 65,
                biome: 'snow',
                description: 'Frozen wasteland, end-game area',
                difficulty: 5
            },
            
            // Northeast - Mixed forest/plains
            NE_PLAINS: {
                center: { x: 100, z: -100 },
                radius: 40,
                biome: 'plains',
                description: 'Peaceful farming area',
                difficulty: 1
            },
            
            // Southeast - Rocky desert
            SE_BADLANDS: {
                center: { x: 100, z: 100 },
                radius: 45,
                biome: 'desert',
                description: 'Rocky badlands with caves',
                difficulty: 3
            }
        };
    }
    
    defineLocations() {
        return {
            // Towns and villages
            SPAWN_VILLAGE: {
                x: 30, z: 30,
                type: 'village',
                name: 'Haven Village',
                description: 'Starting village with basic NPCs and shops',
                region: 'SPAWN'
            },
            
            FOREST_OUTPOST: {
                x: -20, z: -120,
                type: 'outpost',
                name: 'Woodcutter Outpost',
                description: 'Logging camp in the northern forest',
                region: 'NORTH_FOREST'
            },
            
            MOUNTAIN_TOWN: {
                x: 120, z: -30,
                type: 'town',
                name: 'Ironpeak',
                description: 'Mining town in the eastern mountains',
                region: 'EAST_MOUNTAINS'
            },
            
            DESERT_OASIS: {
                x: 30, z: 130,
                type: 'oasis',
                name: 'Mirage Oasis',
                description: 'Life-saving oasis in the southern desert',
                region: 'SOUTH_DESERT'
            },
            
            TUNDRA_FORTRESS: {
                x: -130, z: 20,
                type: 'fortress',
                name: 'Frosthold',
                description: 'Ancient fortress in the frozen west',
                region: 'WEST_TUNDRA'
            },
            
            // Dungeons and special areas
            FOREST_CAVE: {
                x: -50, z: -180,
                type: 'dungeon',
                name: 'Bear Cave',
                description: 'Dangerous cave system',
                region: 'NORTH_FOREST'
            },
            
            MOUNTAIN_MINE: {
                x: 180, z: 40,
                type: 'dungeon',
                name: 'Deep Mine',
                description: 'Abandoned mine with treasures',
                region: 'EAST_MOUNTAINS'
            },
            
            DESERT_RUINS: {
                x: -40, z: 180,
                type: 'ruins',
                name: 'Ancient Ruins',
                description: 'Mysterious desert ruins',
                region: 'SOUTH_DESERT'
            }
        };
    }
    
    defineRoads() {
        return [
            // Main cross roads from spawn
            {
                name: 'North Road',
                start: { x: 0, z: 0 },
                end: { x: 0, z: -120 },
                width: 3
            },
            {
                name: 'South Road', 
                start: { x: 0, z: 0 },
                end: { x: 0, z: 120 },
                width: 3
            },
            {
                name: 'East Road',
                start: { x: 0, z: 0 },
                end: { x: 120, z: 0 },
                width: 3
            },
            {
                name: 'West Road',
                start: { x: 0, z: 0 },
                end: { x: -120, z: 0 },
                width: 3
            },
            
            // Secondary roads
            {
                name: 'Mountain Path',
                start: { x: 120, z: 0 },
                end: { x: 120, z: -30 },
                width: 2
            },
            {
                name: 'Forest Trail',
                start: { x: 0, z: -120 },
                end: { x: -20, z: -120 },
                width: 2
            }
        ];
    }
    
    // Get the biome for a specific coordinate
    getBiomeAt(x, z) {
        // Check if coordinates are within world bounds
        if (x < this.worldBounds.minX || x > this.worldBounds.maxX ||
            z < this.worldBounds.minZ || z > this.worldBounds.maxZ) {
            return 'void'; // Outside world
        }
        
        // Check which region this coordinate belongs to
        for (const [regionName, region] of Object.entries(this.regions)) {
            const distance = Math.sqrt(
                Math.pow(x - region.center.x, 2) + 
                Math.pow(z - region.center.z, 2)
            );
            
            if (distance <= region.radius) {
                return region.biome;
            }
        }
        
        // Default to plains if not in any specific region
        return 'plains';
    }
    
    // Get the region for a specific coordinate
    getRegionAt(x, z) {
        for (const [regionName, region] of Object.entries(this.regions)) {
            const distance = Math.sqrt(
                Math.pow(x - region.center.x, 2) + 
                Math.pow(z - region.center.z, 2)
            );
            
            if (distance <= region.radius) {
                return regionName;
            }
        }
        
        return 'UNKNOWN';
    }
    
    // Check if coordinates are on a road
    isOnRoad(x, z) {
        for (const road of this.roads) {
            if (this.isPointOnRoad(x, z, road)) {
                return true;
            }
        }
        return false;
    }
    
    isPointOnRoad(x, z, road) {
        const { start, end, width } = road;
        
        // Calculate distance from point to line segment
        const A = x - start.x;
        const B = z - start.z;
        const C = end.x - start.x;
        const D = end.z - start.z;
        
        const dot = A * C + B * D;
        const lenSq = C * C + D * D;
        
        if (lenSq === 0) return false; // Start and end are the same point
        
        const param = dot / lenSq;
        
        let xx, zz;
        
        if (param < 0) {
            xx = start.x;
            zz = start.z;
        } else if (param > 1) {
            xx = end.x;
            zz = end.z;
        } else {
            xx = start.x + param * C;
            zz = start.z + param * D;
        }
        
        const dx = x - xx;
        const dz = z - zz;
        const distance = Math.sqrt(dx * dx + dz * dz);
        
        return distance <= width / 2;
    }
    
    // Get height for terrain (can be customized per region)
    getHeightAt(x, z) {
        const region = this.getRegionAt(x, z);
        const regionData = this.regions[region];
        
        if (!regionData) {
            return 10; // Default height
        }
        
        // Base height varies by region
        let baseHeight = 10;
        
        switch (regionData.biome) {
            case 'mountains':
                baseHeight = 15 + Math.random() * 10; // Higher terrain
                break;
            case 'desert':
                baseHeight = 8 + Math.random() * 4; // Lower, flatter
                break;
            case 'snow':
                baseHeight = 12 + Math.random() * 6; // Varied frozen terrain
                break;
            case 'forest':
                baseHeight = 10 + Math.random() * 5; // Moderate hills
                break;
            case 'plains':
            default:
                baseHeight = 10 + Math.random() * 3; // Gentle rolling hills
                break;
        }
        
        // Roads are always flat
        if (this.isOnRoad(x, z)) {
            baseHeight = 10;
        }
        
        return Math.floor(baseHeight);
    }
    
    // Get nearby locations
    getNearbyLocations(x, z, radius = 50) {
        const nearby = [];
        
        for (const [locationName, location] of Object.entries(this.locations)) {
            const distance = Math.sqrt(
                Math.pow(x - location.x, 2) + 
                Math.pow(z - location.z, 2)
            );
            
            if (distance <= radius) {
                nearby.push({
                    name: locationName,
                    ...location,
                    distance: Math.floor(distance)
                });
            }
        }
        
        return nearby.sort((a, b) => a.distance - b.distance);
    }
}

// Export for global access
window.FixedWorld = FixedWorld;
