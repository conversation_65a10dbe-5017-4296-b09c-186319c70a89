// === ANIMAL SYSTEM ===
// Wildlife animals: deer, boars, squirrels

class Animal {
    constructor(x, z, type) {
        this.type = type;
        this.position = new THREE.Vector3(x, 0, z);
        this.velocity = new THREE.Vector3(0, 0, 0);
        this.startPosition = new THREE.Vector3(x, 0, z);
        
        // Animal properties
        this.speed = this.getSpeedForType(type);
        this.detectionRange = this.getDetectionRangeForType(type);
        this.maxDistanceFromStart = 30;
        this.fadeDistance = 50; // Distance at which animal fades away
        
        // State management
        this.state = 'idle'; // idle, fleeing, climbing, fading, charging, flying
        this.fleeDirection = new THREE.Vector3(0, 0, 0);
        this.fleeTimer = 0;
        this.fadeTimer = 0;
        this.fadeTime = 3; // 3 seconds to fade
        this.climbTarget = null;
        this.climbHeight = 0;

        // Special behavior properties
        this.chargeTimer = 0;
        this.chargeTime = 2; // Boars charge for 2 seconds
        this.flyHeight = 0;
        this.targetFlyHeight = 8; // Birds fly up 8 blocks
        
        // Animation
        this.animationOffset = Math.random() * Math.PI * 2;
        
        // Create mesh
        this.createMesh();
        this.updateGroundPosition();

        console.log(`🦌 Created ${type} at (${Math.floor(x)}, ${Math.floor(z)}, ${this.position.y})`);
        console.log(`🦌 Mesh created:`, this.mesh ? 'YES' : 'NO');
        console.log(`🦌 Mesh position:`, this.mesh ? this.mesh.position : 'N/A');
    }
    
    getSpeedForType(type) {
        switch(type) {
            case 'deer': return 8; // Fast
            case 'boar': return 7; // Medium-fast (for charging)
            case 'squirrel': return 10; // Very fast but short distance
            case 'fox': return 9; // Swift
            case 'bird': return 12; // Very fast when flying
            default: return 5;
        }
    }

    getDetectionRangeForType(type) {
        switch(type) {
            case 'deer': return 15; // Very skittish
            case 'boar': return 10; // Aggressive - detects from further
            case 'squirrel': return 6; // Small detection range
            case 'fox': return 12; // Alert and cautious
            case 'bird': return 8; // Good eyesight
            default: return 10;
        }
    }
    
    createMesh() {
        const group = new THREE.Group();
        
        switch(this.type) {
            case 'deer':
                this.createDeerMesh(group);
                break;
            case 'boar':
                this.createBoarMesh(group);
                break;
            case 'squirrel':
                this.createSquirrelMesh(group);
                break;
            case 'fox':
                this.createFoxMesh(group);
                break;
            case 'bird':
                this.createBirdMesh(group);
                break;
        }
        
        this.mesh = group;
        this.mesh.position.copy(this.position);
        scene.add(this.mesh);
        console.log(`🦌 Added ${this.type} mesh to scene at position:`, this.mesh.position);
    }
    
    createDeerMesh(group) {
        // Body (brown)
        const bodyGeometry = new THREE.BoxGeometry(1.2, 0.8, 2.0);
        const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.y = 1.2;
        group.add(body);
        
        // Head (lighter brown)
        const headGeometry = new THREE.BoxGeometry(0.6, 0.6, 0.8);
        const headMaterial = new THREE.MeshLambertMaterial({ color: 0xA0522D });
        const head = new THREE.Mesh(headGeometry, headMaterial);
        head.position.set(0, 1.5, 1.2);
        group.add(head);
        
        // Antlers (dark brown)
        const antlerGeometry = new THREE.BoxGeometry(0.1, 0.8, 0.1);
        const antlerMaterial = new THREE.MeshLambertMaterial({ color: 0x654321 });
        
        const leftAntler = new THREE.Mesh(antlerGeometry, antlerMaterial);
        leftAntler.position.set(-0.2, 2.2, 1.2);
        group.add(leftAntler);
        
        const rightAntler = new THREE.Mesh(antlerGeometry, antlerMaterial);
        rightAntler.position.set(0.2, 2.2, 1.2);
        group.add(rightAntler);
        
        // Legs (4 legs)
        const legGeometry = new THREE.BoxGeometry(0.2, 1.0, 0.2);
        const legMaterial = new THREE.MeshLambertMaterial({ color: 0x654321 });
        
        const frontLeftLeg = new THREE.Mesh(legGeometry, legMaterial);
        frontLeftLeg.position.set(-0.4, 0.5, 0.6);
        group.add(frontLeftLeg);
        
        const frontRightLeg = new THREE.Mesh(legGeometry, legMaterial);
        frontRightLeg.position.set(0.4, 0.5, 0.6);
        group.add(frontRightLeg);
        
        const backLeftLeg = new THREE.Mesh(legGeometry, legMaterial);
        backLeftLeg.position.set(-0.4, 0.5, -0.6);
        group.add(backLeftLeg);
        
        const backRightLeg = new THREE.Mesh(legGeometry, legMaterial);
        backRightLeg.position.set(0.4, 0.5, -0.6);
        group.add(backRightLeg);
        
        // Tail
        const tailGeometry = new THREE.BoxGeometry(0.1, 0.1, 0.4);
        const tail = new THREE.Mesh(tailGeometry, bodyMaterial);
        tail.position.set(0, 1.2, -1.2);
        group.add(tail);
        
        this.body = body;
        this.legs = [frontLeftLeg, frontRightLeg, backLeftLeg, backRightLeg];
    }
    
    createBoarMesh(group) {
        // Body (dark brown/black)
        const bodyGeometry = new THREE.BoxGeometry(1.0, 0.8, 1.6);
        const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x2F2F2F });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.y = 1.0;
        group.add(body);
        
        // Head (same color)
        const headGeometry = new THREE.BoxGeometry(0.8, 0.6, 0.8);
        const head = new THREE.Mesh(headGeometry, bodyMaterial);
        head.position.set(0, 1.2, 1.0);
        group.add(head);
        
        // Tusks (white)
        const tuskGeometry = new THREE.BoxGeometry(0.1, 0.3, 0.1);
        const tuskMaterial = new THREE.MeshLambertMaterial({ color: 0xFFFFFF });
        
        const leftTusk = new THREE.Mesh(tuskGeometry, tuskMaterial);
        leftTusk.position.set(-0.2, 1.0, 1.3);
        group.add(leftTusk);
        
        const rightTusk = new THREE.Mesh(tuskGeometry, tuskMaterial);
        rightTusk.position.set(0.2, 1.0, 1.3);
        group.add(rightTusk);
        
        // Legs (shorter and thicker)
        const legGeometry = new THREE.BoxGeometry(0.3, 0.8, 0.3);
        const legMaterial = new THREE.MeshLambertMaterial({ color: 0x1a1a1a });
        
        const frontLeftLeg = new THREE.Mesh(legGeometry, legMaterial);
        frontLeftLeg.position.set(-0.3, 0.4, 0.5);
        group.add(frontLeftLeg);
        
        const frontRightLeg = new THREE.Mesh(legGeometry, legMaterial);
        frontRightLeg.position.set(0.3, 0.4, 0.5);
        group.add(frontRightLeg);
        
        const backLeftLeg = new THREE.Mesh(legGeometry, legMaterial);
        backLeftLeg.position.set(-0.3, 0.4, -0.5);
        group.add(backLeftLeg);
        
        const backRightLeg = new THREE.Mesh(legGeometry, legMaterial);
        backRightLeg.position.set(0.3, 0.4, -0.5);
        group.add(backRightLeg);
        
        this.body = body;
        this.legs = [frontLeftLeg, frontRightLeg, backLeftLeg, backRightLeg];
    }
    
    createSquirrelMesh(group) {
        // Body (gray)
        const bodyGeometry = new THREE.BoxGeometry(0.4, 0.3, 0.6);
        const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x808080 });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.y = 0.4;
        group.add(body);
        
        // Head
        const headGeometry = new THREE.BoxGeometry(0.3, 0.3, 0.3);
        const head = new THREE.Mesh(headGeometry, bodyMaterial);
        head.position.set(0, 0.5, 0.4);
        group.add(head);
        
        // Ears
        const earGeometry = new THREE.BoxGeometry(0.1, 0.2, 0.1);
        const leftEar = new THREE.Mesh(earGeometry, bodyMaterial);
        leftEar.position.set(-0.1, 0.7, 0.4);
        group.add(leftEar);
        
        const rightEar = new THREE.Mesh(earGeometry, bodyMaterial);
        rightEar.position.set(0.1, 0.7, 0.4);
        group.add(rightEar);
        
        // Big fluffy tail
        const tailGeometry = new THREE.BoxGeometry(0.3, 0.4, 0.8);
        const tail = new THREE.Mesh(tailGeometry, bodyMaterial);
        tail.position.set(0, 0.6, -0.6);
        group.add(tail);
        
        // Small legs
        const legGeometry = new THREE.BoxGeometry(0.1, 0.2, 0.1);
        const legMaterial = new THREE.MeshLambertMaterial({ color: 0x654321 });
        
        const frontLeftLeg = new THREE.Mesh(legGeometry, legMaterial);
        frontLeftLeg.position.set(-0.15, 0.1, 0.2);
        group.add(frontLeftLeg);
        
        const frontRightLeg = new THREE.Mesh(legGeometry, legMaterial);
        frontRightLeg.position.set(0.15, 0.1, 0.2);
        group.add(frontRightLeg);
        
        const backLeftLeg = new THREE.Mesh(legGeometry, legMaterial);
        backLeftLeg.position.set(-0.15, 0.1, -0.2);
        group.add(backLeftLeg);
        
        const backRightLeg = new THREE.Mesh(legGeometry, legMaterial);
        backRightLeg.position.set(0.15, 0.1, -0.2);
        group.add(backRightLeg);
        
        this.body = body;
        this.legs = [frontLeftLeg, frontRightLeg, backLeftLeg, backRightLeg];
        this.tail = tail;
    }

    createFoxMesh(group) {
        // Body (orange/red)
        const bodyGeometry = new THREE.BoxGeometry(0.8, 0.6, 1.4);
        const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0xFF4500 }); // Orange red
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.y = 0.8;
        group.add(body);

        // Head (same color)
        const headGeometry = new THREE.BoxGeometry(0.5, 0.5, 0.6);
        const head = new THREE.Mesh(headGeometry, bodyMaterial);
        head.position.set(0, 1.0, 0.8);
        group.add(head);

        // Ears (pointed)
        const earGeometry = new THREE.ConeGeometry(0.1, 0.3, 4);
        const earMaterial = new THREE.MeshLambertMaterial({ color: 0xCC3300 });

        const leftEar = new THREE.Mesh(earGeometry, earMaterial);
        leftEar.position.set(-0.15, 1.4, 0.8);
        group.add(leftEar);

        const rightEar = new THREE.Mesh(earGeometry, earMaterial);
        rightEar.position.set(0.15, 1.4, 0.8);
        group.add(rightEar);

        // Snout (darker)
        const snoutGeometry = new THREE.BoxGeometry(0.2, 0.2, 0.3);
        const snoutMaterial = new THREE.MeshLambertMaterial({ color: 0x8B0000 });
        const snout = new THREE.Mesh(snoutGeometry, snoutMaterial);
        snout.position.set(0, 0.9, 1.0);
        group.add(snout);

        // Bushy tail (larger)
        const tailGeometry = new THREE.BoxGeometry(0.4, 0.4, 1.0);
        const tailMaterial = new THREE.MeshLambertMaterial({ color: 0xFF6347 }); // Lighter orange
        const tail = new THREE.Mesh(tailGeometry, tailMaterial);
        tail.position.set(0, 1.0, -0.9);
        group.add(tail);

        // Legs (slender)
        const legGeometry = new THREE.BoxGeometry(0.15, 0.6, 0.15);
        const legMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });

        const frontLeftLeg = new THREE.Mesh(legGeometry, legMaterial);
        frontLeftLeg.position.set(-0.25, 0.3, 0.4);
        group.add(frontLeftLeg);

        const frontRightLeg = new THREE.Mesh(legGeometry, legMaterial);
        frontRightLeg.position.set(0.25, 0.3, 0.4);
        group.add(frontRightLeg);

        const backLeftLeg = new THREE.Mesh(legGeometry, legMaterial);
        backLeftLeg.position.set(-0.25, 0.3, -0.4);
        group.add(backLeftLeg);

        const backRightLeg = new THREE.Mesh(legGeometry, legMaterial);
        backRightLeg.position.set(0.25, 0.3, -0.4);
        group.add(backRightLeg);

        this.body = body;
        this.legs = [frontLeftLeg, frontRightLeg, backLeftLeg, backRightLeg];
        this.tail = tail;
    }

    createBirdMesh(group) {
        // Body (brown/gray)
        const bodyGeometry = new THREE.BoxGeometry(0.3, 0.4, 0.6);
        const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.y = 0.4;
        group.add(body);

        // Head (smaller)
        const headGeometry = new THREE.BoxGeometry(0.25, 0.25, 0.25);
        const head = new THREE.Mesh(headGeometry, bodyMaterial);
        head.position.set(0, 0.6, 0.35);
        group.add(head);

        // Beak (yellow/orange)
        const beakGeometry = new THREE.ConeGeometry(0.05, 0.15, 4);
        const beakMaterial = new THREE.MeshLambertMaterial({ color: 0xFFA500 });
        const beak = new THREE.Mesh(beakGeometry, beakMaterial);
        beak.position.set(0, 0.6, 0.5);
        beak.rotation.x = Math.PI / 2;
        group.add(beak);

        // Wings (folded)
        const wingGeometry = new THREE.BoxGeometry(0.6, 0.1, 0.4);
        const wingMaterial = new THREE.MeshLambertMaterial({ color: 0x654321 });
        const wings = new THREE.Mesh(wingGeometry, wingMaterial);
        wings.position.set(0, 0.5, 0);
        group.add(wings);

        // Tail feathers
        const tailGeometry = new THREE.BoxGeometry(0.2, 0.1, 0.4);
        const tail = new THREE.Mesh(tailGeometry, wingMaterial);
        tail.position.set(0, 0.4, -0.4);
        group.add(tail);

        // Legs (thin)
        const legGeometry = new THREE.BoxGeometry(0.05, 0.3, 0.05);
        const legMaterial = new THREE.MeshLambertMaterial({ color: 0xFFA500 });

        const leftLeg = new THREE.Mesh(legGeometry, legMaterial);
        leftLeg.position.set(-0.1, 0.15, 0);
        group.add(leftLeg);

        const rightLeg = new THREE.Mesh(legGeometry, legMaterial);
        rightLeg.position.set(0.1, 0.15, 0);
        group.add(rightLeg);

        this.body = body;
        this.legs = [leftLeg, rightLeg];
        this.wings = wings;
        this.tail = tail;

        // Birds can be on ground or in trees initially
        this.isFlying = false;
        this.groundLevel = this.position.y;
    }
    
    updateGroundPosition() {
        const groundHeight = getHeightAt(Math.floor(this.position.x), Math.floor(this.position.z));
        const heightOffset = this.type === 'squirrel' ? 0.5 : (this.type === 'deer' ? 1.5 : 1.2);
        this.position.y = groundHeight + heightOffset;
    }
    
    update(deltaTime, playerPosition) {
        const distanceToPlayer = this.position.distanceTo(playerPosition);
        const distanceFromStart = this.position.distanceTo(this.startPosition);
        
        // Check if player is within detection range
        if (this.state === 'idle' && distanceToPlayer <= this.detectionRange) {
            this.startFleeing(playerPosition);
        }
        
        // Update based on state
        switch(this.state) {
            case 'idle':
                this.updateIdle(deltaTime);
                break;
            case 'fleeing':
                this.updateFleeing(deltaTime, playerPosition);
                break;
            case 'charging':
                this.updateCharging(deltaTime, playerPosition);
                break;
            case 'climbing':
                this.updateClimbing(deltaTime);
                break;
            case 'flying':
                this.updateFlying(deltaTime);
                break;
            case 'fading':
                this.updateFading(deltaTime);
                break;
        }
        
        // Check if too far from start or player - start fading
        if (this.state !== 'fading' && (distanceFromStart > this.fadeDistance || distanceToPlayer > this.fadeDistance)) {
            this.startFading();
        }
        
        // Update mesh position and animation
        if (this.mesh && this.state !== 'fading') {
            this.updateGroundPosition();
            this.mesh.position.copy(this.position);
            this.animateMovement(deltaTime);
        }
    }
    
    startFleeing(playerPosition) {
        // Different behaviors for different animals
        if (this.type === 'boar') {
            // Boars charge at the player first
            this.state = 'charging';
            this.chargeTimer = 0;
            this.fleeDirection.copy(playerPosition).sub(this.position).normalize(); // Toward player
            console.log(`${this.type} started charging at player!`);
        } else if (this.type === 'bird') {
            // Birds fly away
            this.state = 'flying';
            this.isFlying = true;
            this.flyHeight = 0;
            console.log(`${this.type} started flying away!`);
        } else {
            // Normal fleeing for deer, fox, squirrel
            this.state = 'fleeing';

            // Calculate flee direction (away from player)
            this.fleeDirection.copy(this.position).sub(playerPosition).normalize();

            // Add some randomness to flee direction
            this.fleeDirection.x += (Math.random() - 0.5) * 0.5;
            this.fleeDirection.z += (Math.random() - 0.5) * 0.5;
            this.fleeDirection.normalize();

            // Special behavior for squirrels - find nearest tree
            if (this.type === 'squirrel') {
                this.findNearestTree();
            }

            console.log(`${this.type} started fleeing!`);
        }
    }
    
    findNearestTree() {
        // Look for trees in a small radius
        let nearestTree = null;
        let nearestDistance = Infinity;

        for (let dx = -10; dx <= 10; dx += 2) {
            for (let dz = -10; dz <= 10; dz += 2) {
                const checkX = Math.floor(this.position.x + dx);
                const checkZ = Math.floor(this.position.z + dz);

                // Check for actual trees rather than biome
                let hasTree = false;

                if (window.world && window.world.heightMap) {
                    const key = `${checkX},${checkZ}`;
                    const heightData = window.world.heightMap.get(key);
                    hasTree = heightData && heightData.hasTree;
                } else {
                    // Fallback: check height difference
                    const groundHeight = getHeightAt(checkX, checkZ);
                    const baseHeight = getHeightAt(checkX, checkZ, false);
                    hasTree = groundHeight > baseHeight + 2;
                }

                if (hasTree) {
                    const distance = Math.sqrt(dx * dx + dz * dz);
                    if (distance < nearestDistance) {
                        nearestDistance = distance;
                        nearestTree = new THREE.Vector3(checkX, 0, checkZ);
                    }
                }
            }
        }

        if (nearestTree) {
            this.climbTarget = nearestTree;
            this.fleeDirection.copy(nearestTree).sub(this.position).normalize();
            console.log(`🐿️ Squirrel found tree at (${nearestTree.x}, ${nearestTree.z})`);
        } else {
            console.log(`🐿️ Squirrel couldn't find nearby tree, fleeing normally`);
        }
    }
    
    updateIdle(deltaTime) {
        // Gentle wandering
        const time = Date.now() * 0.001 + this.animationOffset;
        const wanderX = Math.sin(time * 0.3) * 0.5;
        const wanderZ = Math.cos(time * 0.2) * 0.5;
        
        this.position.x += wanderX * deltaTime;
        this.position.z += wanderZ * deltaTime;
    }
    
    updateFleeing(deltaTime, playerPosition) {
        // Move away from player
        this.velocity.copy(this.fleeDirection).multiplyScalar(this.speed);
        this.position.add(this.velocity.clone().multiplyScalar(deltaTime));
        
        // Face movement direction
        if (this.mesh && this.velocity.length() > 0.1) {
            const angle = Math.atan2(this.velocity.x, this.velocity.z);
            this.mesh.rotation.y = angle;
        }
        
        // Special handling for squirrels
        if (this.type === 'squirrel' && this.climbTarget) {
            const distanceToTree = this.position.distanceTo(this.climbTarget);
            if (distanceToTree < 2) {
                this.state = 'climbing';
                this.climbHeight = 0;
                console.log('Squirrel started climbing tree!');
            }
        }
        
        // Continue fleeing for a while
        this.fleeTimer += deltaTime;
        if (this.fleeTimer > 5) { // Flee for 5 seconds
            this.startFading();
        }
    }
    
    updateCharging(deltaTime, playerPosition) {
        // Boar charges at player for a short time, then flees
        this.chargeTimer += deltaTime;

        if (this.chargeTimer < this.chargeTime) {
            // Still charging - move toward player
            this.velocity.copy(this.fleeDirection).multiplyScalar(this.speed * 1.5); // Faster when charging
            this.position.add(this.velocity.clone().multiplyScalar(deltaTime));

            // Face movement direction
            if (this.mesh && this.velocity.length() > 0.1) {
                const angle = Math.atan2(this.velocity.x, this.velocity.z);
                this.mesh.rotation.y = angle;
            }
        } else {
            // Charge time over - now flee away from player
            this.state = 'fleeing';
            this.fleeDirection.copy(this.position).sub(playerPosition).normalize();

            // Add randomness to flee direction
            this.fleeDirection.x += (Math.random() - 0.5) * 0.5;
            this.fleeDirection.z += (Math.random() - 0.5) * 0.5;
            this.fleeDirection.normalize();

            this.fleeTimer = 0; // Reset flee timer
            console.log(`${this.type} finished charging, now fleeing!`);
        }
    }

    updateClimbing(deltaTime) {
        // Squirrel climbs up the tree
        this.climbHeight += 3 * deltaTime; // 3 blocks per second
        this.position.y += 3 * deltaTime;

        if (this.climbHeight > 8) { // Climbed high enough
            this.startFading();
        }
    }

    updateFlying(deltaTime) {
        // Bird flies up and away
        this.flyHeight += 6 * deltaTime; // 6 blocks per second upward
        this.position.y += 6 * deltaTime;

        // Also move horizontally away from start position
        const awayDirection = new THREE.Vector3(
            this.position.x - this.startPosition.x,
            0,
            this.position.z - this.startPosition.z
        ).normalize();

        if (awayDirection.length() === 0) {
            // If at start position, pick random direction
            awayDirection.set(Math.random() - 0.5, 0, Math.random() - 0.5).normalize();
        }

        this.position.x += awayDirection.x * this.speed * deltaTime;
        this.position.z += awayDirection.z * this.speed * deltaTime;

        // Face movement direction
        if (this.mesh) {
            const angle = Math.atan2(awayDirection.x, awayDirection.z);
            this.mesh.rotation.y = angle;
        }

        if (this.flyHeight > this.targetFlyHeight) { // Flown high enough
            this.startFading();
        }
    }
    
    updateFading(deltaTime) {
        this.fadeTimer += deltaTime;
        const fadeProgress = this.fadeTimer / this.fadeTime;
        
        if (this.mesh) {
            // Fade out the mesh
            this.mesh.traverse((child) => {
                if (child.material) {
                    child.material.transparent = true;
                    child.material.opacity = 1 - fadeProgress;
                }
            });
            
            if (fadeProgress >= 1) {
                this.destroy();
                return true; // Signal for removal
            }
        }
        
        return false;
    }
    
    startFading() {
        this.state = 'fading';
        this.fadeTimer = 0;
        console.log(`${this.type} started fading away`);
    }
    
    animateMovement(deltaTime) {
        if (!this.body || !this.legs) return;
        
        const time = Date.now() * 0.001 + this.animationOffset;
        const isMoving = this.state === 'fleeing' || this.state === 'climbing';
        
        if (isMoving) {
            // Walking animation
            const walkCycle = Math.sin(time * 8) * 0.3;
            
            // Animate legs
            this.legs.forEach((leg, index) => {
                if (leg) {
                    leg.rotation.x = (index % 2 === 0) ? walkCycle : -walkCycle;
                }
            });
            
            // Body bobbing
            this.body.position.y += Math.sin(time * 16) * 0.02;
            
            // Tail animation for squirrel
            if (this.tail && this.type === 'squirrel') {
                this.tail.rotation.x = Math.sin(time * 6) * 0.2;
            }
        } else {
            // Idle animation - gentle breathing
            const breathe = Math.sin(time * 2) * 0.01;
            this.body.position.y += breathe;
            
            // Reset leg rotations
            this.legs.forEach(leg => {
                if (leg) leg.rotation.x = 0;
            });
        }
    }
    
    destroy() {
        if (this.mesh) {
            scene.remove(this.mesh);
            this.mesh = null;
        }
    }
}

// Animal Manager
class AnimalManager {
    constructor() {
        this.animals = [];
        this.maxAnimals = 12; // Maximum animals in the world
        this.spawnTimer = 0;
        this.spawnInterval = 8; // Spawn new animal every 8 seconds
        this.spawnRadius = 40; // Spawn within 40 blocks of player
    }

    spawnAnimal(x, z, type) {
        if (this.animals.length >= this.maxAnimals) {
            return null;
        }

        // Check if location has trees nearby (better than biome detection)
        const hasTreesNearby = this.checkForTreesNearby(x, z);
        const biome = getBiome(Math.floor(x), Math.floor(z));
        console.log(`Trying to spawn ${type} at (${Math.floor(x)}, ${Math.floor(z)}) - biome: ${biome}, trees nearby: ${hasTreesNearby}`);

        // Allow spawning if there are trees nearby OR if it's plains/forest biome
        if (!hasTreesNearby && biome !== 'forest' && biome !== 'plains') {
            console.log(`Rejected spawn - no trees nearby and biome is ${biome}`);
            return null;
        }

        // Make sure not too close to towns
        const townDistance = Math.sqrt((x - 100) * (x - 100) + (z - 50) * (z - 50));
        const villageDistance = Math.sqrt((x + 150) * (x + 150) + (z - 200) * (z - 200));

        if (townDistance < 25 || villageDistance < 25) {
            console.log(`Rejected spawn - too close to settlements`);
            return null;
        }

        const animal = new Animal(x, z, type);
        this.animals.push(animal);
        console.log(`✅ Successfully spawned ${type} at (${Math.floor(x)}, ${Math.floor(z)}) in ${biome} (trees: ${hasTreesNearby})`);
        return animal;
    }

    checkForTreesNearby(x, z) {
        // Check for trees in a small radius around the spawn point
        const checkRadius = 8; // Check 8 blocks around
        let treeCount = 0;

        for (let dx = -checkRadius; dx <= checkRadius; dx += 2) {
            for (let dz = -checkRadius; dz <= checkRadius; dz += 2) {
                const checkX = Math.floor(x + dx);
                const checkZ = Math.floor(z + dz);

                // Use the world's tree detection if available
                if (window.world && window.world.heightMap) {
                    const key = `${checkX},${checkZ}`;
                    const heightData = window.world.heightMap.get(key);
                    if (heightData && heightData.hasTree) {
                        treeCount++;
                    }
                } else {
                    // Fallback: check if height suggests trees (trees make terrain higher)
                    const groundHeight = getHeightAt(checkX, checkZ);
                    const baseHeight = getHeightAt(checkX, checkZ, false); // Without trees
                    if (groundHeight > baseHeight + 2) { // Tree height difference
                        treeCount++;
                    }
                }
            }
        }

        // Consider it "forested" if we found at least 3 trees in the area
        return treeCount >= 3;
    }

    spawnRandomAnimal(playerPosition) {
        // Random position around player
        const angle = Math.random() * Math.PI * 2;
        const distance = 20 + Math.random() * 20; // 20-40 blocks away

        const spawnX = playerPosition.x + Math.cos(angle) * distance;
        const spawnZ = playerPosition.z + Math.sin(angle) * distance;

        // Random animal type with different probabilities
        const animalTypes = [
            'deer', 'deer',           // Common
            'fox', 'fox',             // Common
            'squirrel', 'squirrel',   // Common
            'bird', 'bird',           // Common
            'boar'                    // Less common (aggressive)
        ];
        const type = animalTypes[Math.floor(Math.random() * animalTypes.length)];

        return this.spawnAnimal(spawnX, spawnZ, type);
    }

    update(deltaTime, playerPosition) {
        // Update existing animals
        for (let i = this.animals.length - 1; i >= 0; i--) {
            const animal = this.animals[i];
            const shouldRemove = animal.update(deltaTime, playerPosition);

            if (shouldRemove || animal.state === 'fading') {
                // Remove faded animals
                if (animal.fadeTimer >= animal.fadeTime) {
                    animal.destroy();
                    this.animals.splice(i, 1);
                }
            }
        }

        // Spawn new animals
        this.spawnTimer += deltaTime;
        if (this.spawnTimer >= this.spawnInterval && this.animals.length < this.maxAnimals) {
            this.spawnRandomAnimal(playerPosition);
            this.spawnTimer = 0;
        }
    }

    removeAnimal(animal) {
        const index = this.animals.indexOf(animal);
        if (index > -1) {
            animal.destroy();
            this.animals.splice(index, 1);
        }
    }

    removeAllAnimals() {
        this.animals.forEach(animal => animal.destroy());
        this.animals = [];
    }

    getAnimalCount() {
        return this.animals.length;
    }

    getAnimalsByType(type) {
        return this.animals.filter(animal => animal.type === type);
    }
}

// Test function to spawn an animal near the player
function testSpawnAnimal(type = 'deer') {
    if (window.world && window.world.animalManager && window.player) {
        const playerPos = window.player.position;
        const testX = playerPos.x + 10;
        const testZ = playerPos.z + 10;

        console.log(`🧪 Test spawning ${type} at:`, testX, testZ);
        const animal = new Animal(testX, testZ, type);
        window.world.animalManager.animals.push(animal);
        console.log('🧪 Test animal created:', animal);
        return animal;
    } else {
        console.log('❌ Cannot test spawn - missing world, animalManager, or player');
        return null;
    }
}

// Test functions for specific animals
function testSpawnBoar() { return testSpawnAnimal('boar'); }
function testSpawnFox() { return testSpawnAnimal('fox'); }
function testSpawnBird() { return testSpawnAnimal('bird'); }

// Export for global access
window.AnimalManager = AnimalManager;
window.Animal = Animal;
window.testSpawnAnimal = testSpawnAnimal;
