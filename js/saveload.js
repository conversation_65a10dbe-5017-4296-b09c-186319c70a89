// === SAVE/LOAD SYSTEM ===
// Manual save/load with keyboard shortcuts and confirmation dialogs

class SaveLoadSystem {
    constructor() {
        this.setupKeyboardShortcuts();
        this.createDialogStyles();
    }
    
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Only work if user is logged in and not in inventory
            if (!authSystem || !authSystem.isLoggedIn) return;
            if (window.player && window.player.inventory && window.player.inventory.isOpen) return;
            
            // Ctrl+S for save
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                this.showSaveDialog();
            }
            
            // Ctrl+L for load
            if (e.ctrlKey && e.key === 'l') {
                e.preventDefault();
                this.showLoadDialog();
            }
        });
    }
    
    createDialogStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .save-dialog-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.7);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 5000;
                font-family: 'Courier New', monospace;
            }
            
            .save-dialog-box {
                background: #8B4513;
                border: 4px solid #A0522D;
                border-style: outset;
                border-radius: 10px;
                padding: 30px;
                max-width: 400px;
                width: 90%;
                text-align: center;
                box-shadow: 
                    inset 2px 2px 0px #D2B48C,
                    inset -2px -2px 0px #654321,
                    8px 8px 16px rgba(0,0,0,0.5);
            }
            
            .save-dialog-title {
                color: #FFD700;
                font-size: 24px;
                font-weight: bold;
                margin-bottom: 15px;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            }
            
            .save-dialog-message {
                color: #F5DEB3;
                font-size: 16px;
                margin-bottom: 25px;
                line-height: 1.4;
            }
            
            .save-dialog-info {
                background: #654321;
                border: 2px solid #A0522D;
                border-style: inset;
                padding: 15px;
                margin-bottom: 25px;
                border-radius: 5px;
                color: #F5DEB3;
                font-size: 14px;
            }
            
            .save-dialog-buttons {
                display: flex;
                gap: 15px;
                justify-content: center;
            }
            
            .save-dialog-button {
                padding: 12px 25px;
                border: none;
                border-radius: 8px;
                font-family: 'Courier New', monospace;
                font-size: 16px;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s;
                text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
                min-width: 100px;
            }
            
            .save-dialog-button.yes {
                background: linear-gradient(45deg, #228B22, #32CD32);
                color: white;
            }
            
            .save-dialog-button.yes:hover {
                background: linear-gradient(45deg, #32CD32, #228B22);
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            }
            
            .save-dialog-button.no {
                background: linear-gradient(45deg, #DC143C, #FF6347);
                color: white;
            }
            
            .save-dialog-button.no:hover {
                background: linear-gradient(45deg, #FF6347, #DC143C);
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            }
            
            .save-dialog-button.load {
                background: linear-gradient(45deg, #4169E1, #6495ED);
                color: white;
            }
            
            .save-dialog-button.load:hover {
                background: linear-gradient(45deg, #6495ED, #4169E1);
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            }
            
            .save-status-message {
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                border: 2px solid #8B4513;
                font-family: 'Courier New', monospace;
                font-size: 14px;
                z-index: 6000;
                animation: slideInRight 0.3s ease-out;
            }
            
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
            
            .save-status-message.success {
                border-color: #32CD32;
                color: #90EE90;
            }
            
            .save-status-message.error {
                border-color: #FF6347;
                color: #FFB6C1;
            }
            
            .save-status-message.info {
                border-color: #6495ED;
                color: #87CEEB;
            }
        `;
        document.head.appendChild(style);
    }
    
    showSaveDialog() {
        // Get current game state info
        const gameInfo = this.getCurrentGameInfo();
        
        const dialog = document.createElement('div');
        dialog.className = 'save-dialog-overlay';
        dialog.innerHTML = `
            <div class="save-dialog-box">
                <div class="save-dialog-title">💾 Save Game</div>
                <div class="save-dialog-message">
                    Do you want to save your current progress?
                </div>
                <div class="save-dialog-info">
                    <div><strong>Player:</strong> ${authSystem.currentUser.username}</div>
                    <div><strong>Health:</strong> ${gameInfo.health}/100</div>
                    <div><strong>Position:</strong> ${gameInfo.position}</div>
                    <div><strong>Kills:</strong> ${gameInfo.kills}</div>
                    <div><strong>Score:</strong> ${gameInfo.score}</div>
                    <div><strong>Play Time:</strong> ${gameInfo.playTime}</div>
                </div>
                <div class="save-dialog-buttons">
                    <button class="save-dialog-button yes" onclick="saveLoadSystem.confirmSave()">
                        ✅ Yes, Save
                    </button>
                    <button class="save-dialog-button no" onclick="saveLoadSystem.cancelSave()">
                        ❌ Cancel
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(dialog);
        
        // Add escape key to cancel
        const escapeHandler = (e) => {
            if (e.key === 'Escape') {
                this.cancelSave();
                document.removeEventListener('keydown', escapeHandler);
            }
        };
        document.addEventListener('keydown', escapeHandler);
        
        // Store escape handler for cleanup
        dialog.escapeHandler = escapeHandler;
    }
    
    showLoadDialog() {
        // Get saved game info
        const savedInfo = this.getSavedGameInfo();
        
        if (!savedInfo) {
            this.showStatusMessage('❌ No saved game found!', 'error');
            return;
        }
        
        const dialog = document.createElement('div');
        dialog.className = 'save-dialog-overlay';
        dialog.innerHTML = `
            <div class="save-dialog-box">
                <div class="save-dialog-title">📁 Load Game</div>
                <div class="save-dialog-message">
                    Load your most recent save? This will overwrite current progress.
                </div>
                <div class="save-dialog-info">
                    <div><strong>Saved:</strong> ${savedInfo.lastSaved}</div>
                    <div><strong>Health:</strong> ${savedInfo.health}/100</div>
                    <div><strong>Position:</strong> ${savedInfo.position}</div>
                    <div><strong>Kills:</strong> ${savedInfo.kills}</div>
                    <div><strong>Score:</strong> ${savedInfo.score}</div>
                    <div><strong>Play Time:</strong> ${savedInfo.playTime}</div>
                </div>
                <div class="save-dialog-buttons">
                    <button class="save-dialog-button load" onclick="saveLoadSystem.confirmLoad()">
                        📁 Load Save
                    </button>
                    <button class="save-dialog-button no" onclick="saveLoadSystem.cancelLoad()">
                        ❌ Cancel
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(dialog);
        
        // Add escape key to cancel
        const escapeHandler = (e) => {
            if (e.key === 'Escape') {
                this.cancelLoad();
                document.removeEventListener('keydown', escapeHandler);
            }
        };
        document.addEventListener('keydown', escapeHandler);
        
        // Store escape handler for cleanup
        dialog.escapeHandler = escapeHandler;
    }
    
    getCurrentGameInfo() {
        if (!window.player) {
            return {
                health: 0,
                position: 'Unknown',
                kills: 0,
                score: 0,
                playTime: '0 minutes'
            };
        }
        
        const playTimeMs = Date.now() - new Date(authSystem.currentUser.loginTime).getTime();
        const playTimeMinutes = Math.floor(playTimeMs / 60000);
        
        return {
            health: Math.floor(window.player.health),
            position: `(${Math.floor(window.player.position.x)}, ${Math.floor(window.player.position.z)})`,
            kills: window.player.inventory.kills,
            score: window.player.inventory.score,
            playTime: `${playTimeMinutes} minutes`
        };
    }
    
    getSavedGameInfo() {
        if (!authSystem || !authSystem.isLoggedIn) return null;
        
        const users = JSON.parse(localStorage.getItem('minecraftUsers') || '{}');
        const userData = users[authSystem.currentUser.username.toLowerCase()];
        
        if (!userData || !userData.gameData) return null;
        
        const saved = userData.gameData;
        const lastSaved = new Date(saved.lastSaved).toLocaleString();
        const playTimeMinutes = Math.floor((saved.playTime || 0) / 60000);
        
        return {
            health: saved.health || 100,
            position: saved.position ? `(${Math.floor(saved.position.x)}, ${Math.floor(saved.position.z)})` : 'Unknown',
            kills: saved.inventory?.kills || 0,
            score: saved.inventory?.score || 0,
            playTime: `${playTimeMinutes} minutes`,
            lastSaved: lastSaved
        };
    }

    confirmSave() {
        this.removeDialog();

        if (!window.player || !authSystem || !authSystem.isLoggedIn) {
            this.showStatusMessage('❌ Cannot save - not logged in!', 'error');
            return;
        }

        try {
            // Create save data
            const gameData = {
                position: {
                    x: window.player.position.x,
                    y: window.player.position.y,
                    z: window.player.position.z
                },
                health: window.player.health,
                stamina: window.player.stamina,
                inventory: {
                    slots: window.player.inventory.slots,
                    kills: window.player.inventory.kills,
                    score: window.player.inventory.score
                },
                playTime: Date.now() - new Date(authSystem.currentUser.loginTime).getTime(),
                manualSave: true // Mark as manual save
            };

            // Save the data
            authSystem.saveGameData(gameData);

            // Show success message
            this.showStatusMessage('✅ Game saved successfully!', 'success');

            console.log('Manual save completed');

        } catch (error) {
            console.error('Save error:', error);
            this.showStatusMessage('❌ Save failed! Please try again.', 'error');
        }
    }

    confirmLoad() {
        this.removeDialog();

        if (!authSystem || !authSystem.isLoggedIn) {
            this.showStatusMessage('❌ Cannot load - not logged in!', 'error');
            return;
        }

        try {
            // Get saved data
            const users = JSON.parse(localStorage.getItem('minecraftUsers') || '{}');
            const userData = users[authSystem.currentUser.username.toLowerCase()];

            if (!userData || !userData.gameData) {
                this.showStatusMessage('❌ No saved game found!', 'error');
                return;
            }

            const savedData = userData.gameData;

            // Load player position
            if (savedData.position && window.player) {
                window.player.position.set(
                    savedData.position.x,
                    savedData.position.y,
                    savedData.position.z
                );
            }

            // Load player stats
            if (window.player) {
                if (savedData.health !== undefined) window.player.health = savedData.health;
                if (savedData.stamina !== undefined) window.player.stamina = savedData.stamina;
            }

            // Load inventory
            if (savedData.inventory && window.player && window.player.inventory) {
                if (savedData.inventory.slots) {
                    window.player.inventory.slots = [...savedData.inventory.slots];
                }
                if (savedData.inventory.kills !== undefined) {
                    window.player.inventory.kills = savedData.inventory.kills;
                }
                if (savedData.inventory.score !== undefined) {
                    window.player.inventory.score = savedData.inventory.score;
                }

                // Update inventory display
                window.player.inventory.updateInventoryDisplay();
                window.player.inventory.updateUI();
            }

            // Update world chunks around new position
            if (window.world && window.player) {
                window.world.updateChunks(window.player.position.x, window.player.position.z);
            }

            // Show success message
            this.showStatusMessage('📁 Game loaded successfully!', 'success');

            console.log('Manual load completed');

        } catch (error) {
            console.error('Load error:', error);
            this.showStatusMessage('❌ Load failed! Please try again.', 'error');
        }
    }

    cancelSave() {
        this.removeDialog();
        this.showStatusMessage('💾 Save cancelled', 'info');
    }

    cancelLoad() {
        this.removeDialog();
        this.showStatusMessage('📁 Load cancelled', 'info');
    }

    removeDialog() {
        const dialog = document.querySelector('.save-dialog-overlay');
        if (dialog) {
            // Remove escape key handler
            if (dialog.escapeHandler) {
                document.removeEventListener('keydown', dialog.escapeHandler);
            }
            dialog.remove();
        }
    }

    showStatusMessage(message, type = 'info') {
        // Remove any existing status message
        const existing = document.querySelector('.save-status-message');
        if (existing) {
            existing.remove();
        }

        // Create new status message
        const statusDiv = document.createElement('div');
        statusDiv.className = `save-status-message ${type}`;
        statusDiv.textContent = message;

        document.body.appendChild(statusDiv);

        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (statusDiv.parentNode) {
                statusDiv.remove();
            }
        }, 3000);
    }
}

// Global save/load system instance
let saveLoadSystem;
