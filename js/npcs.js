// === NPC SYSTEM ===
// Basic NPCs that walk around as blocks

class NPC {
    constructor(x, z, clothingColor = 0x8B4513, name = "Villager") {
        this.name = name;
        this.startX = x;
        this.startZ = z;
        this.position = new THREE.Vector3(x, 0, z);
        this.velocity = new THREE.Vector3(0, 0, 0);
        this.clothingColor = clothingColor;
        this.skinColor = 0xFFDBB5; // Peach skin color
        
        // Movement properties
        this.speed = 2; // blocks per second
        this.wanderRadius = 15; // how far from spawn point they wander
        this.directionChangeTimer = 0;
        this.directionChangeInterval = 3 + Math.random() * 4; // 3-7 seconds
        this.currentDirection = new THREE.Vector3(0, 0, 0);

        // Combat properties
        this.state = 'wandering'; // wandering, fighting, panicking
        this.detectionRange = 8; // How close monsters need to be
        this.currentTarget = null;
        this.panicTimer = 0;
        this.panicDuration = 5; // Panic for 5 seconds
        this.combatChoice = null; // 'fight' or 'panic'
        this.panicDirection = new THREE.Vector3(0, 0, 0);

        // Animation properties
        this.bobOffset = Math.random() * Math.PI * 2; // Random start for bob animation
        this.walkAnimationSpeed = 4;
        
        // Create the NPC mesh
        this.createMesh();
        
        // Set initial position on ground
        this.updateGroundPosition();
        
        // Choose initial random direction
        this.chooseNewDirection();
        
        console.log(`Created NPC ${this.name} at (${Math.floor(x)}, ${Math.floor(z)})`);
    }
    
    createMesh() {
        // Create a simple block character
        const group = new THREE.Group();

        // Body (main block) - brown clothing
        const bodyGeometry = new THREE.BoxGeometry(1.2, 1.8, 0.8);
        const bodyMaterial = new THREE.MeshLambertMaterial({ color: this.clothingColor });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.y = 0.9; // Center the body
        group.add(body);

        // Head (smaller block on top) - peach skin
        const headGeometry = new THREE.BoxGeometry(0.8, 0.8, 0.8);
        const headMaterial = new THREE.MeshLambertMaterial({ color: this.skinColor });
        const head = new THREE.Mesh(headGeometry, headMaterial);
        head.position.y = 2.2; // On top of body
        group.add(head);
        
        // Nose (small protruding block)
        const noseGeometry = new THREE.BoxGeometry(0.12, 0.15, 0.2);
        const noseMaterial = new THREE.MeshLambertMaterial({ color: 0xF5C99B }); // Slightly darker peach
        const nose = new THREE.Mesh(noseGeometry, noseMaterial);
        nose.position.set(0, 2.2, 0.45);
        group.add(nose);

        // Mouth (small horizontal rectangle)
        const mouthGeometry = new THREE.BoxGeometry(0.25, 0.08, 0.05);
        const mouthMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 }); // Brown
        const mouth = new THREE.Mesh(mouthGeometry, mouthMaterial);
        mouth.position.set(0, 2.05, 0.42);
        group.add(mouth);

        // Eyes (white background)
        const eyeGeometry = new THREE.BoxGeometry(0.12, 0.12, 0.05);
        const eyeMaterial = new THREE.MeshLambertMaterial({ color: 0xffffff });

        const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
        leftEye.position.set(-0.18, 2.3, 0.42);
        group.add(leftEye);

        const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
        rightEye.position.set(0.18, 2.3, 0.42);
        group.add(rightEye);

        // Eye pupils (small black dots)
        const pupilGeometry = new THREE.BoxGeometry(0.06, 0.06, 0.02);
        const pupilMaterial = new THREE.MeshLambertMaterial({ color: 0x000000 });

        const leftPupil = new THREE.Mesh(pupilGeometry, pupilMaterial);
        leftPupil.position.set(-0.18, 2.3, 0.44);
        group.add(leftPupil);

        const rightPupil = new THREE.Mesh(pupilGeometry, pupilMaterial);
        rightPupil.position.set(0.18, 2.3, 0.44);
        group.add(rightPupil);
        
        // Arms (brown clothing - like long sleeves)
        const armGeometry = new THREE.BoxGeometry(0.4, 1.2, 0.4);
        const armMaterial = new THREE.MeshLambertMaterial({ color: this.clothingColor });

        const leftArm = new THREE.Mesh(armGeometry, armMaterial);
        leftArm.position.set(-0.8, 1.2, 0);
        group.add(leftArm);

        const rightArm = new THREE.Mesh(armGeometry, armMaterial);
        rightArm.position.set(0.8, 1.2, 0);
        group.add(rightArm);

        // Legs (small blocks at bottom) - brown clothing
        const legGeometry = new THREE.BoxGeometry(0.4, 0.8, 0.4);
        const legMaterial = new THREE.MeshLambertMaterial({ color: this.clothingColor });

        const leftLeg = new THREE.Mesh(legGeometry, legMaterial);
        leftLeg.position.set(-0.3, 0.4, 0);
        group.add(leftLeg);

        const rightLeg = new THREE.Mesh(legGeometry, legMaterial);
        rightLeg.position.set(0.3, 0.4, 0);
        group.add(rightLeg);
        
        this.mesh = group;
        this.body = body;
        this.head = head;
        this.leftArm = leftArm;
        this.rightArm = rightArm;
        this.leftLeg = leftLeg;
        this.rightLeg = rightLeg;
        
        scene.add(this.mesh);
    }
    
    updateGroundPosition() {
        // Set Y position to ground level
        const groundHeight = getHeightAt(Math.floor(this.position.x), Math.floor(this.position.z));
        this.position.y = groundHeight + 1; // 1 block above ground
    }
    
    chooseNewDirection() {
        // Choose a random direction to walk
        const angle = Math.random() * Math.PI * 2;
        this.currentDirection.set(
            Math.cos(angle),
            0,
            Math.sin(angle)
        );
        this.currentDirection.normalize();
        
        // Reset timer
        this.directionChangeTimer = 0;
        this.directionChangeInterval = 3 + Math.random() * 4; // 3-7 seconds
    }
    
    update(deltaTime, playerPosition) {
        // Check for nearby monsters first
        this.checkForMonsters();

        // Handle different states
        if (this.state === 'panicking') {
            this.updatePanicking(deltaTime);
            this.updateMeshAndAnimation(deltaTime);
            return;
        } else if (this.state === 'fighting') {
            this.updateFighting(deltaTime);
            this.updateMeshAndAnimation(deltaTime);
            return;
        }

        // Normal wandering behavior
        // Update direction change timer
        this.directionChangeTimer += deltaTime;
        
        // Change direction periodically or if too far from spawn
        const distanceFromSpawn = Math.sqrt(
            Math.pow(this.position.x - this.startX, 2) + 
            Math.pow(this.position.z - this.startZ, 2)
        );
        
        if (this.directionChangeTimer >= this.directionChangeInterval || distanceFromSpawn > this.wanderRadius) {
            if (distanceFromSpawn > this.wanderRadius) {
                // Head back towards spawn point
                this.currentDirection.set(
                    this.startX - this.position.x,
                    0,
                    this.startZ - this.position.z
                );
                this.currentDirection.normalize();
            } else {
                // Choose random direction
                this.chooseNewDirection();
            }
        }
        
        // Move in current direction
        this.velocity.copy(this.currentDirection);
        this.velocity.multiplyScalar(this.speed);
        
        // Apply movement
        const oldPosition = this.position.clone();
        this.position.add(this.velocity.clone().multiplyScalar(deltaTime));
        
        // Check building collision and rollback if needed
        if (window.world && window.world.checkBuildingCollision && window.world.checkBuildingCollision(this.position)) {
            this.position.copy(oldPosition);
            // Choose new direction if we hit something
            this.chooseNewDirection();
        }
        
        // Update mesh and animations
        this.updateMeshAndAnimation(deltaTime);
    }
    
    destroy() {
        if (this.mesh) {
            scene.remove(this.mesh);
        }
    }

    updateMeshAndAnimation(deltaTime) {
        // Update ground position
        this.updateGroundPosition();

        // Update mesh position
        if (this.mesh) {
            this.mesh.position.copy(this.position);

            // Face movement direction
            if (this.velocity.length() > 0.1) {
                const angle = Math.atan2(this.velocity.x, this.velocity.z);
                this.mesh.rotation.y = angle;
            }

            // Walking animation (bob up and down)
            const time = Date.now() * 0.001;
            const isMoving = this.velocity.length() > 0.1;
            const isPanicking = this.state === 'panicking';

            if (isMoving || isPanicking) {
                // Bob the whole character (faster when panicking)
                const speed = isPanicking ? this.walkAnimationSpeed * 2 : this.walkAnimationSpeed;
                const bobAmount = Math.sin((time + this.bobOffset) * speed) * 0.1;
                this.mesh.position.y += bobAmount;

                // Swing arms (more dramatic when panicking)
                const armSwing = Math.sin((time + this.bobOffset) * speed * 2) * (isPanicking ? 0.8 : 0.3);
                this.leftArm.rotation.x = armSwing;
                this.rightArm.rotation.x = -armSwing;

                // Move legs
                const legSwing = Math.sin((time + this.bobOffset) * speed * 2) * 0.2;
                this.leftLeg.rotation.x = legSwing;
                this.rightLeg.rotation.x = -legSwing;

                // Raise arms when panicking
                if (isPanicking) {
                    this.leftArm.rotation.z = -Math.PI / 3; // Arms up!
                    this.rightArm.rotation.z = Math.PI / 3;
                }
            } else {
                // Reset animations when not moving
                this.leftArm.rotation.x = 0;
                this.rightArm.rotation.x = 0;
                this.leftLeg.rotation.x = 0;
                this.rightLeg.rotation.x = 0;
                this.leftArm.rotation.z = 0;
                this.rightArm.rotation.z = 0;
            }
        }
    }

    checkForMonsters() {
        if (this.state !== 'wandering') return; // Only check when wandering

        // Check for nearby orcs
        if (window.world && window.world.orcs) {
            for (const orc of window.world.orcs) {
                const distance = this.position.distanceTo(orc.position);

                if (distance <= this.detectionRange) {
                    // Monster detected! Choose response
                    this.currentTarget = orc;
                    this.combatChoice = Math.random() < 0.4 ? 'fight' : 'panic'; // 40% fight, 60% panic

                    if (this.combatChoice === 'fight') {
                        this.state = 'fighting';
                        console.log(`${this.name} decided to fight the orc!`);
                    } else {
                        this.state = 'panicking';
                        this.panicTimer = 0;
                        console.log(`${this.name} is panicking and running around!`);
                    }

                    break; // Only react to one monster at a time
                }
            }
        }
    }

    updateFighting(deltaTime) {
        // Face the target and stay in place (fighting stance)
        if (this.currentTarget) {
            const direction = new THREE.Vector3(
                this.currentTarget.position.x - this.position.x,
                0,
                this.currentTarget.position.z - this.position.z
            );

            if (this.mesh && direction.length() > 0.1) {
                const angle = Math.atan2(direction.x, direction.z);
                this.mesh.rotation.y = angle;
            }

            // Stay in fighting stance (no movement)
            this.velocity.set(0, 0, 0);

            // Check if target is still close
            const distance = this.position.distanceTo(this.currentTarget.position);
            if (distance > this.detectionRange + 3) {
                // Target moved away, go back to wandering
                this.state = 'wandering';
                this.currentTarget = null;
                console.log(`${this.name} stopped fighting - target moved away`);
            }
        } else {
            // No target, go back to wandering
            this.state = 'wandering';
        }
    }

    updatePanicking(deltaTime) {
        // Run around in circles with hands in the air!
        this.panicTimer += deltaTime;

        if (this.panicTimer >= this.panicDuration) {
            // Stop panicking
            this.state = 'wandering';
            this.panicTimer = 0;
            this.currentTarget = null;
            console.log(`${this.name} stopped panicking`);
            return;
        }

        // Run in a circle around starting position
        const circleSpeed = 3; // Speed of circular motion
        const circleRadius = 4; // Radius of panic circle
        const angle = this.panicTimer * circleSpeed;

        // Calculate target position on circle
        const targetX = this.startX + Math.cos(angle) * circleRadius;
        const targetZ = this.startZ + Math.sin(angle) * circleRadius;

        // Move towards target position
        const direction = new THREE.Vector3(
            targetX - this.position.x,
            0,
            targetZ - this.position.z
        );
        direction.normalize();

        this.velocity.copy(direction).multiplyScalar(this.speed * 1.5); // Faster when panicking
        this.position.add(this.velocity.clone().multiplyScalar(deltaTime));

        // Check if target is still nearby (keep panicking if monster is close)
        if (this.currentTarget) {
            const distance = this.position.distanceTo(this.currentTarget.position);
            if (distance > this.detectionRange + 5) {
                // Monster is far away, can stop panicking early
                this.state = 'wandering';
                this.panicTimer = 0;
                this.currentTarget = null;
                console.log(`${this.name} stopped panicking - monster went away`);
            }
        }
    }
}

// NPC Manager
class NPCManager {
    constructor() {
        this.npcs = [];
        this.maxNPCs = 8; // Maximum number of NPCs in the world
        this.spawnTimer = 0;
        this.spawnInterval = 5; // Spawn new NPC every 5 seconds if under max
    }
    
    spawnNPC(x, z, clothingColor, name) {
        if (this.npcs.length >= this.maxNPCs) {
            return null;
        }

        const npc = new NPC(x, z, clothingColor, name);
        this.npcs.push(npc);
        return npc;
    }
    
    spawnRandomNPC() {
        // Spawn NPCs near actual fixed world locations
        let locations = [
            { x: 30, z: 30, name: "Haven Village" }  // Moved away from spawn point
        ];

        // Add other locations from fixed world if available
        if (window.world && window.world.fixedWorld && window.world.fixedWorld.locations) {
            const fixedLocations = Object.values(window.world.fixedWorld.locations);
            fixedLocations.forEach(loc => {
                if (loc.type === 'village' || loc.type === 'town' || loc.type === 'outpost') {
                    locations.push({ x: loc.x, z: loc.z, name: loc.name });
                }
            });
        }

        const location = locations[Math.floor(Math.random() * locations.length)];
        
        // Random position near the location (closer to buildings)
        const offsetX = (Math.random() - 0.5) * 30; // ±15 blocks
        const offsetZ = (Math.random() - 0.5) * 30;
        
        const spawnX = location.x + offsetX;
        const spawnZ = location.z + offsetZ;
        
        // Random brown clothing shades and names
        const clothingColors = [
            0x8B4513, // Saddle brown
            0xA0522D, // Sienna
            0x654321, // Dark brown
            0x964B00, // Traditional brown
            0x6F4E37, // Coffee brown
            0x8B7355  // Burlywood
        ];
        const names = ["Villager", "Trader", "Farmer", "Guard", "Citizen", "Wanderer"];

        const clothingColor = clothingColors[Math.floor(Math.random() * clothingColors.length)];
        const name = names[Math.floor(Math.random() * names.length)];

        return this.spawnNPC(spawnX, spawnZ, clothingColor, name);
    }
    
    update(deltaTime, playerPosition) {
        // Update existing NPCs
        this.npcs.forEach(npc => {
            npc.update(deltaTime, playerPosition);
        });
        
        // Don't spawn random NPCs - they should stay in towns
        // NPCs are spawned by the world system at town locations only
    }
    
    removeNPC(npc) {
        const index = this.npcs.indexOf(npc);
        if (index > -1) {
            npc.destroy();
            this.npcs.splice(index, 1);
        }
    }
    
    removeAllNPCs() {
        this.npcs.forEach(npc => npc.destroy());
        this.npcs = [];
    }
}

// Export for global access
window.NPCManager = NPCManager;
window.NPC = NPC;
