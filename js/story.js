// === STORY AND CHARACTER SYSTEM ===
// Character creation, story progression, and RPG elements

// RPG Game State Management
class GameState {
    constructor() {
        this.character = null;
        this.isCharacterCreated = false;
        this.currentRoom = "town";
        this.gameStarted = false;
    }

    createCharacter(name, characterClass, gender) {
        // Set base stats based on class
        let stats = { strength: 10, defense: 8, magic: 8 };

        if (characterClass === "Warrior") {
            stats = { strength: 15, defense: 10, magic: 5 };
        } else if (characterClass === "Mage") {
            stats = { strength: 8, defense: 6, magic: 15 };
        } else if (characterClass === "Rogue") {
            stats = { strength: 12, defense: 8, magic: 8 };
        }

        this.character = {
            name: name,
            characterClass: characterClass,
            gender: gender,
            level: 1,
            maxHealth: 100,
            health: 100,
            experience: 0,
            experienceToNext: 100,
            gold: 150,
            inventory: [],
            ...stats
        };

        this.isCharacterCreated = true;
        this.updateCharacterUI();
    }

    updateCharacterUI() {
        if (!this.character) return;

        document.getElementById('charName').textContent = `Name: ${this.character.name}`;
        document.getElementById('charClass').textContent = `Class: ${this.character.characterClass}`;
        document.getElementById('charLevel').textContent = `Level: ${this.character.level}`;
        document.getElementById('charExp').textContent = `Experience: ${this.character.experience}/${this.character.experienceToNext}`;
        document.getElementById('charGold').textContent = `Gold: ${this.character.gold}`;

        // Get equipment bonuses if player exists
        let equipmentBonuses = { strength: 0, defense: 0, magic: 0 };
        if (window.player && player.inventory) {
            equipmentBonuses = player.inventory.getEquipmentBonuses();
        }

        // Show base stats + equipment bonuses
        const totalStrength = this.character.strength + equipmentBonuses.strength;
        const totalDefense = this.character.defense + equipmentBonuses.defense;
        const totalMagic = this.character.magic + equipmentBonuses.magic;

        document.getElementById('charStrength').textContent = `⚔️ Strength: ${totalStrength}${equipmentBonuses.strength > 0 ? ` (${this.character.strength}+${equipmentBonuses.strength})` : ''}`;
        document.getElementById('charDefense').textContent = `🛡️ Defense: ${totalDefense}${equipmentBonuses.defense > 0 ? ` (${this.character.defense}+${equipmentBonuses.defense})` : ''}`;
        document.getElementById('charMagic').textContent = `🔮 Magic: ${totalMagic}${equipmentBonuses.magic > 0 ? ` (${this.character.magic}+${equipmentBonuses.magic})` : ''}`;

        // Update main health display to use character health
        document.getElementById('health').textContent = this.character.health;
    }

    gainExperience(amount) {
        if (!this.character) return;

        this.character.experience += amount;
        if (this.character.experience >= this.character.experienceToNext) {
            this.levelUp();
        }
        this.updateCharacterUI();
    }

    levelUp() {
        const oldLevel = this.character.level;
        this.character.level += 1;
        this.character.maxHealth += 20;
        this.character.health = this.character.maxHealth; // Full heal on level up
        this.character.strength += 2;
        this.character.defense += 2;
        this.character.magic += 2;
        this.character.experienceToNext = this.character.level * 100;

        // Show level up notification
        this.showLevelUpNotification(oldLevel, this.character.level);
        console.log(`🎉 ${this.character.name} leveled up to level ${this.character.level}!`);
    }

    showLevelUpNotification(oldLevel, newLevel) {
        // Create level up notification
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(135deg, #FFD700, #FFA500);
            border: 4px solid #FF8C00;
            border-radius: 15px;
            padding: 30px;
            color: #8B4513;
            font-family: 'Courier New', monospace;
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            z-index: 2000;
            box-shadow: 0 0 30px rgba(255, 215, 0, 0.8);
            animation: levelUpPulse 2s ease-in-out;
        `;

        notification.innerHTML = `
            <div style="font-size: 32px; margin-bottom: 15px;">🎉 LEVEL UP! 🎉</div>
            <div>${this.character.name} reached Level ${newLevel}!</div>
            <div style="font-size: 18px; margin-top: 15px; color: #654321;">
                Health: ${this.character.maxHealth} | Strength: ${this.character.strength}<br>
                Defense: ${this.character.defense} | Magic: ${this.character.magic}
            </div>
        `;

        // Add CSS animation
        if (!document.getElementById('levelUpStyle')) {
            const style = document.createElement('style');
            style.id = 'levelUpStyle';
            style.textContent = `
                @keyframes levelUpPulse {
                    0% { transform: translate(-50%, -50%) scale(0.5); opacity: 0; }
                    50% { transform: translate(-50%, -50%) scale(1.1); opacity: 1; }
                    100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(notification);

        // Remove notification after 4 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 4000);
    }
}

// Initialize game state
let gameState = new GameState();

// Character Creation System
function initializeCharacterCreation() {
    // Show character creation modal if no character exists
    if (!gameState.isCharacterCreated) {
        // Update the title with the username
        const username = authSystem && authSystem.currentUser ? authSystem.currentUser.username : "Player";
        const titleElement = document.getElementById('characterCreationTitle');
        if (titleElement) {
            titleElement.textContent = `What is ${username} like to be?`;
        }

        document.getElementById('characterCreationModal').style.display = 'flex';

        // Add event listeners for character creation
        setupCharacterCreationEvents();
    } else {
        // Character already exists, show story panel
        document.getElementById('storyToggle').style.display = 'block';
        gameState.updateCharacterUI();
    }
}

function setupCharacterCreationEvents() {
    // Class selection
    document.querySelectorAll('.class-option').forEach(option => {
        option.addEventListener('click', function() {
            document.querySelectorAll('.class-option').forEach(opt => opt.classList.remove('selected'));
            this.classList.add('selected');
            checkStartButtonState();
        });
    });

    // Gender selection
    document.querySelectorAll('.gender-option').forEach(option => {
        option.addEventListener('click', function() {
            document.querySelectorAll('.gender-option').forEach(opt => opt.classList.remove('selected'));
            this.classList.add('selected');
            checkStartButtonState();
        });
    });

    // No name input needed anymore - using username

    // Start adventure button
    document.getElementById('startAdventure').addEventListener('click', startAdventure);

    // Story toggle button
    document.getElementById('storyToggle').addEventListener('click', function() {
        const panel = document.getElementById('storyPanel');
        panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
    });
}

function checkStartButtonState() {
    const selectedClass = document.querySelector('.class-option.selected');
    const selectedGender = document.querySelector('.gender-option.selected');

    const startButton = document.getElementById('startAdventure');
    startButton.disabled = !(selectedClass && selectedGender);
}

function startAdventure() {
    // Use the username from auth system instead of asking for character name
    const name = authSystem && authSystem.currentUser ? authSystem.currentUser.username : "Player";
    const selectedClass = document.querySelector('.class-option.selected').dataset.class;
    const selectedGender = document.querySelector('.gender-option.selected').dataset.gender;

    // Create character
    gameState.createCharacter(name, selectedClass, selectedGender);
    
    // Hide character creation modal
    document.getElementById('characterCreationModal').style.display = 'none';
    
    // Show story panel toggle
    document.getElementById('storyToggle').style.display = 'block';
    
    // Start the game
    gameState.gameStarted = true;

    console.log(`Adventure started for ${name} the ${selectedClass}!`);

    // Initialize the main game now that character is created
    if (typeof window.init === 'function') {
        // Call init again now that character is created
        setTimeout(() => {
            window.init();
        }, 100);
    }
}

// Export for global access
window.gameState = gameState;
window.initializeCharacterCreation = initializeCharacterCreation;
