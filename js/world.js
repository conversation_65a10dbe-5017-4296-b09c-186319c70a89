// === WORLD GENERATION ===
// Terrain generation, chunks, and biomes

// Game constants
const CHUNK_SIZE = 16;
const WORLD_HEIGHT = 24;
const SEA_LEVEL = 12;
const RENDER_DISTANCE = 3;

// Biomes
const BIOMES = {
    PLAINS: 'plains',
    FOREST: 'forest',
    DESERT: 'desert',
    SNOW: 'snow',
    MOUNTAINS: 'mountains'
};

function getBiome(x, z) {
    const temp = noise.octaveNoise(x * 0.01, z * 0.01, 2);
    const humidity = noise.octaveNoise(x * 0.008 + 1000, z * 0.008 + 1000, 2);
    const height = getHeightAt(x, z);
    
    if (height > 20) return BIOMES.MOUNTAINS;
    if (temp < 0.3) return BIOMES.SNOW;
    if (humidity < 0.3) return BIOMES.DESERT;
    if (humidity > 0.7) return BIOMES.FOREST;
    return BIOMES.PLAINS;
}

function getHeightAt(x, z) {
    // Check if there's a placed block at this position first
    if (window.world && window.world.heightMap) {
        const heightKey = `${Math.floor(x)},${Math.floor(z)}`;
        const placedHeight = window.world.heightMap.get(heightKey);
        if (placedHeight !== undefined) {
            return placedHeight;
        }
    }

    // Check all chunks for the highest block at this position
    if (window.world && window.world.chunks) {
        const chunkX = Math.floor(x / 16);
        const chunkZ = Math.floor(z / 16);
        const localX = Math.floor(x) - (chunkX * 16);
        const localZ = Math.floor(z) - (chunkZ * 16);

        const chunkKey = `${chunkX},${chunkZ}`;
        const chunk = window.world.chunks.get(chunkKey);

        if (chunk) {
            // Find highest non-air block at this position
            for (let y = WORLD_HEIGHT - 1; y >= 0; y--) {
                const block = chunk.getBlock(localX, y, localZ);
                if (block && block !== BLOCKS.AIR) {
                    return y;
                }
            }
        }
    }

    // Fallback to terrain generation
    let height = noise.octaveNoise(x * 0.005, z * 0.005, 4) * 15;
    height += noise.octaveNoise(x * 0.02, z * 0.02, 2) * 5;
    return Math.floor(height + SEA_LEVEL);
}

function getBlockType(x, y, z, biome) {
    const height = getHeightAt(x, z);
    
    // Water
    if (y <= SEA_LEVEL && y > height) {
        return BLOCKS.WATER;
    }
    
    // Air
    if (y > height) return BLOCKS.AIR;
    
    // Underground caves (simple 3D noise)
    if (y < height - 2 && y > 2) {
        const caveNoise1 = noise.octaveNoise(x * 0.03, y * 0.03, 2);
        const caveNoise2 = noise.octaveNoise(z * 0.03, y * 0.03, 2);
        const caveNoise3 = noise.octaveNoise(x * 0.02, z * 0.02, 2);
        
        // Combine noise for 3D cave system
        const caveValue = (caveNoise1 + caveNoise2 + caveNoise3) / 3;
        
        if (caveValue > 0.6) {
            return BLOCKS.AIR; // Cave space
        }
    }
    
    // Surface
    if (y === height) {
        switch(biome) {
            case BIOMES.DESERT: return BLOCKS.SAND;
            case BIOMES.SNOW: return BLOCKS.SNOW;
            default: return BLOCKS.GRASS;
        }
    }
    
    // Subsurface
    if (y > height - 3) {
        return biome === BIOMES.DESERT ? BLOCKS.SAND : BLOCKS.DIRT;
    }
    
    return BLOCKS.STONE;
}

// Chunk class - simplified for performance
class Chunk {
    constructor(chunkX, chunkZ) {
        this.x = chunkX;
        this.z = chunkZ;
        this.blocks = new Array(CHUNK_SIZE * CHUNK_SIZE * WORLD_HEIGHT);
        this.mesh = null;
        this.biome = getBiome(chunkX * CHUNK_SIZE + 8, chunkZ * CHUNK_SIZE + 8);
        
        this.generate();
    }
    
    generate() {
        const startX = this.x * CHUNK_SIZE;
        const startZ = this.z * CHUNK_SIZE;
        
        for (let x = 0; x < CHUNK_SIZE; x++) {
            for (let z = 0; z < CHUNK_SIZE; z++) {
                const worldX = startX + x;
                const worldZ = startZ + z;
                const biome = getBiome(worldX, worldZ);
                
                for (let y = 0; y < WORLD_HEIGHT; y++) {
                    const blockType = getBlockType(worldX, y, worldZ, biome);
                    this.setBlock(x, y, z, blockType);
                }
            }
        }
        
        // Add some trees (simplified)
        if (this.biome === BIOMES.FOREST || this.biome === BIOMES.PLAINS) {
            this.generateTrees();
        }
    }
    
    generateTrees() {
        for (let x = 2; x < CHUNK_SIZE - 2; x += 4) {
            for (let z = 2; z < CHUNK_SIZE - 2; z += 4) {
                const worldX = this.x * CHUNK_SIZE + x;
                const worldZ = this.z * CHUNK_SIZE + z;
                const treeNoise = noise.noise(worldX * 0.1, worldZ * 0.1);
                
                if (treeNoise > 0.6) {
                    const height = getHeightAt(worldX, worldZ);
                    if (height > SEA_LEVEL) {
                        this.generateTree(x, height + 1, z);
                    }
                }
            }
        }
    }
    
    generateTree(x, y, z) {
        const treeHeight = 4 + Math.floor(Math.random() * 3);
        
        // Generate trunk
        for (let i = 0; i < treeHeight; i++) {
            if (y + i < WORLD_HEIGHT) {
                this.setBlock(x, y + i, z, BLOCKS.WOOD);

                // Update height map for collision
                const worldX = this.x * CHUNK_SIZE + x;
                const worldZ = this.z * CHUNK_SIZE + z;
                if (!window.world.heightMap) {
                    window.world.heightMap = new Map();
                }
                const currentHeight = window.world.heightMap.get(`${worldX},${worldZ}`) || 0;
                if (y + i > currentHeight) {
                    window.world.heightMap.set(`${worldX},${worldZ}`, y + i);
                }
            }
        }
        
        // Generate beautiful leafy canopy
        const leafY = y + treeHeight;
        for (let dx = -2; dx <= 2; dx++) {
            for (let dz = -2; dz <= 2; dz++) {
                for (let dy = -1; dy <= 1; dy++) {
                    // Create a nice rounded tree shape
                    if (Math.abs(dx) + Math.abs(dz) + Math.abs(dy) <= 3) {
                        const leafX = x + dx;
                        const leafZ = z + dz;
                        const leafYPos = leafY + dy;
                        
                        if (leafX >= 0 && leafX < CHUNK_SIZE && 
                            leafZ >= 0 && leafZ < CHUNK_SIZE && 
                            leafYPos < WORLD_HEIGHT && leafYPos > 0) {
                            
                            if (this.getBlock(leafX, leafYPos, leafZ) === BLOCKS.AIR) {
                                this.setBlock(leafX, leafYPos, leafZ, BLOCKS.LEAVES);

                                // Update height map for leaf collision
                                const worldX = this.x * CHUNK_SIZE + leafX;
                                const worldZ = this.z * CHUNK_SIZE + leafZ;
                                if (!window.world.heightMap) {
                                    window.world.heightMap = new Map();
                                }
                                const currentHeight = window.world.heightMap.get(`${worldX},${worldZ}`) || 0;
                                if (leafYPos > currentHeight) {
                                    window.world.heightMap.set(`${worldX},${worldZ}`, leafYPos);
                                }
                            }
                        }
                    }
                }
            }
        }
        
        // Add extra leaves on top for better shape
        if (leafY + 2 < WORLD_HEIGHT) {
            this.setBlock(x, leafY + 2, z, BLOCKS.LEAVES);
        }
    }
    
    setBlock(x, y, z, blockType) {
        const index = x + z * CHUNK_SIZE + y * CHUNK_SIZE * CHUNK_SIZE;
        this.blocks[index] = blockType;
    }
    
    getBlock(x, y, z) {
        if (x < 0 || x >= CHUNK_SIZE || y < 0 || y >= WORLD_HEIGHT || z < 0 || z >= CHUNK_SIZE) {
            return BLOCKS.AIR;
        }
        const index = x + z * CHUNK_SIZE + y * CHUNK_SIZE * CHUNK_SIZE;
        return this.blocks[index] || BLOCKS.AIR;
    }
    
    createMesh() {
        if (this.mesh) {
            scene.remove(this.mesh);
            if (this.mesh.geometry) this.mesh.geometry.dispose();
        }
        
        const geometry = new THREE.BufferGeometry();
        const vertices = [];
        const colors = [];
        const indices = [];
        let vertexIndex = 0;
        
        const blockColors = {
            [BLOCKS.GRASS]: [0.3, 0.7, 0.3],
            [BLOCKS.DIRT]: [0.5, 0.3, 0.1],
            [BLOCKS.STONE]: [0.5, 0.5, 0.5],
            [BLOCKS.SAND]: [0.9, 0.7, 0.4],
            [BLOCKS.SNOW]: [1, 1, 1],
            [BLOCKS.WATER]: [0, 0.5, 0.8],
            [BLOCKS.WOOD]: [0.4, 0.2, 0.1],
            [BLOCKS.LEAVES]: [0.1, 0.5, 0.1]
        };
        
        for (let x = 0; x < CHUNK_SIZE; x++) {
            for (let y = 0; y < WORLD_HEIGHT; y++) {
                for (let z = 0; z < CHUNK_SIZE; z++) {
                    const blockType = this.getBlock(x, y, z);
                    
                    if (blockType === BLOCKS.AIR) continue;
                    
                    const worldX = this.x * CHUNK_SIZE + x;
                    const worldZ = this.z * CHUNK_SIZE + z;
                    const color = blockColors[blockType] || [0.5, 0.5, 0.5];
                    
                    // Check each face
                    const faces = [
                        { dir: [0, 1, 0], verts: [[0,1,1], [1,1,1], [1,1,0], [0,1,0]] }, // top
                        { dir: [0, -1, 0], verts: [[0,0,0], [1,0,0], [1,0,1], [0,0,1]] }, // bottom
                        { dir: [1, 0, 0], verts: [[1,0,0], [1,1,0], [1,1,1], [1,0,1]] }, // right
                        { dir: [-1, 0, 0], verts: [[0,0,1], [0,1,1], [0,1,0], [0,0,0]] }, // left
                        { dir: [0, 0, 1], verts: [[1,0,1], [1,1,1], [0,1,1], [0,0,1]] }, // front
                        { dir: [0, 0, -1], verts: [[0,0,0], [0,1,0], [1,1,0], [1,0,0]] }  // back
                    ];
                    
                    faces.forEach(face => {
                        const [dx, dy, dz] = face.dir;
                        const nx = x + dx;
                        const ny = y + dy;
                        const nz = z + dz;
                        
                        let neighborType = BLOCKS.AIR;
                        if (nx >= 0 && nx < CHUNK_SIZE && ny >= 0 && ny < WORLD_HEIGHT && nz >= 0 && nz < CHUNK_SIZE) {
                            neighborType = this.getBlock(nx, ny, nz);
                        }
                        
                        if (neighborType === BLOCKS.AIR || (blockType !== BLOCKS.WATER && neighborType === BLOCKS.WATER)) {
                            face.verts.forEach(vert => {
                                vertices.push(
                                    worldX + vert[0],
                                    y + vert[1],
                                    worldZ + vert[2]
                                );
                                colors.push(color[0], color[1], color[2]);
                            });
                            
                            indices.push(
                                vertexIndex, vertexIndex + 1, vertexIndex + 2,
                                vertexIndex, vertexIndex + 2, vertexIndex + 3
                            );
                            vertexIndex += 4;
                        }
                    });
                }
            }
        }
        
        if (vertices.length > 0) {
            geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
            geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));
            geometry.setIndex(indices);
            geometry.computeVertexNormals();
            
            const material = new THREE.MeshLambertMaterial({ 
                vertexColors: true,
                side: THREE.DoubleSide
            });
            
            this.mesh = new THREE.Mesh(geometry, material);
            scene.add(this.mesh);
        }
    }
}

// World class - simplified
class World {
    constructor() {
        this.chunks = new Map();
        this.orcs = [];
        this.maxOrcs = 5; // Reduced for performance
        this.orcSpawnTimer = 0;
        this.heightMap = new Map(); // For tracking placed blocks and tree collision

        // Initialize fixed world system
        this.fixedWorld = new FixedWorld();
        console.log('🗺️ Using fixed world system (500x500 blocks)');

        // Building collision system - prevents walking through buildings
        this.buildingCollisions = [];
        this.currentLocation = "Starting Area";
        this.locationCheckTimer = 0;

        // NPC system
        this.npcManager = null;

        // Animal system
        this.animalManager = null;

        // Use fixed world locations
        this.STORY_LOCATIONS = this.fixedWorld.locations;

        // Initialize story locations
        this.createLocationMarkers();

        // Initialize NPCs and Animals (after a short delay to ensure everything is loaded)
        setTimeout(() => {
            this.initializeNPCs();
            this.initializeAnimals();
        }, 2000);
    }

    getChunkKey(chunkX, chunkZ) {
        return `${chunkX},${chunkZ}`;
    }

    getChunk(chunkX, chunkZ) {
        const key = this.getChunkKey(chunkX, chunkZ);
        if (!this.chunks.has(key)) {
            this.chunks.set(key, new Chunk(chunkX, chunkZ));
        }
        return this.chunks.get(key);
    }

    updateChunks(playerX, playerZ) {
        const playerChunkX = Math.floor(playerX / CHUNK_SIZE);
        const playerChunkZ = Math.floor(playerZ / CHUNK_SIZE);

        const neededChunks = new Set();

        for (let x = playerChunkX - RENDER_DISTANCE; x <= playerChunkX + RENDER_DISTANCE; x++) {
            for (let z = playerChunkZ - RENDER_DISTANCE; z <= playerChunkZ + RENDER_DISTANCE; z++) {
                const key = this.getChunkKey(x, z);
                neededChunks.add(key);

                const chunk = this.getChunk(x, z);
                if (!chunk.mesh) {
                    chunk.createMesh();
                }
            }
        }

        // Remove distant chunks
        for (const [key, chunk] of this.chunks) {
            if (!neededChunks.has(key)) {
                if (chunk.mesh) {
                    scene.remove(chunk.mesh);
                    if (chunk.mesh.geometry) chunk.mesh.geometry.dispose();
                }
                this.chunks.delete(key);
            }
        }
    }

    updateEnemies(deltaTime, playerPosition) {
        // Update existing orcs
        this.orcs = this.orcs.filter(orc => {
            if (orc.health <= 0) {
                return false; // Remove dead orcs
            }
            orc.update(deltaTime, playerPosition);
            return true;
        });

        // Spawn new orcs occasionally
        this.orcSpawnTimer += deltaTime;
        if (this.orcSpawnTimer > 10 && this.orcs.length < this.maxOrcs) {
            this.spawnOrc(playerPosition);
            this.orcSpawnTimer = 0;
        }
    }

    spawnOrc(playerPosition) {
        // Spawn orc at a random location around the player
        const angle = Math.random() * Math.PI * 2;
        const distance = 20 + Math.random() * 20;

        const spawnX = playerPosition.x + Math.cos(angle) * distance;
        const spawnZ = playerPosition.z + Math.sin(angle) * distance;

        // Make sure spawn location is above ground
        const groundHeight = getHeightAt(spawnX, spawnZ);
        if (groundHeight > SEA_LEVEL) {
            const orc = new Orc(spawnX, spawnZ);
            this.orcs.push(orc);
            console.log(`Spawned orc at (${Math.floor(spawnX)}, ${Math.floor(spawnZ)})`);
        }
    }

    // Building collision system - prevents walking through buildings
    addBuildingCollision(x, z, groundHeight, width, height, depth) {
        const collision = {
            minX: x - width/2,
            maxX: x + width/2,
            minY: groundHeight,
            maxY: groundHeight + height,
            minZ: z - depth/2,
            maxZ: z + depth/2
        };
        this.buildingCollisions.push(collision);
    }

    checkBuildingCollision(position) {
        // Check collision with buildings (add player radius for better collision)
        const playerRadius = 0.5;
        for (const building of this.buildingCollisions) {
            if (position.x + playerRadius >= building.minX && position.x - playerRadius <= building.maxX &&
                position.z + playerRadius >= building.minZ && position.z - playerRadius <= building.maxZ &&
                position.y >= building.minY && position.y <= building.maxY) {
                return true; // Collision detected - player should be stopped
            }
        }
        return false;
    }

    // Create a nice big brown patch for building
    createLocationMarkers() {
        console.log('🟤 Creating brown building patch...');

        // Create a big brown patch at spawn area for future building
        this.createBrownPatch(30, 30, 20); // At (30, 30) with 20 block radius

        // Build the blacksmith house on the brown patch
        this.createBlacksmithHouse(30, 30);

        console.log('✅ Brown building patch and blacksmith house created');
    }

    createTown(x, z, groundHeight) {
        // Create compact town layout centered on the brown floor (within 12 blocks)
        const buildings = [
            // Main buildings in a tight circle around the center
            { x: 0, z: -10, width: 10, height: 8, depth: 8, color: 0xDEB887, name: "Town Hall" },
            { x: -8, z: -5, width: 6, height: 6, depth: 6, color: 0xD2B48C, name: "Shop" },
            { x: 8, z: -5, width: 6, height: 6, depth: 6, color: 0xF5DEB3, name: "House" },
            { x: -8, z: 5, width: 6, height: 6, depth: 6, color: 0xDEB887, name: "Inn" },
            { x: 8, z: 5, width: 6, height: 6, depth: 6, color: 0xD2B48C, name: "Bakery" },
            { x: 0, z: 10, width: 8, height: 7, depth: 6, color: 0xF5DEB3, name: "Blacksmith" }
        ];

        buildings.forEach(building => {
            const buildingX = x + building.x;
            const buildingZ = z + building.z;
            const buildingGroundHeight = getHeightAt(buildingX, buildingZ);

            // Create building
            const buildingGeometry = new THREE.BoxGeometry(building.width, building.height, building.depth);
            const buildingMaterial = new THREE.MeshLambertMaterial({ color: building.color });
            const buildingMesh = new THREE.Mesh(buildingGeometry, buildingMaterial);
            buildingMesh.position.set(buildingX, buildingGroundHeight + building.height/2, buildingZ);
            console.log(`🏢 Built ${building.name} at (${buildingX}, ${buildingGroundHeight + building.height/2}, ${buildingZ})`);
            scene.add(buildingMesh);

            // Add collision box - prevents walking through building
            this.addBuildingCollision(buildingX, buildingZ, buildingGroundHeight, building.width, building.height, building.depth);

            // Create pyramid-style roof
            this.createPyramidRoof(buildingX, buildingZ, buildingGroundHeight + building.height, building.width, building.depth);

            // Door
            const doorGeometry = new THREE.BoxGeometry(1.5, 3, 0.2);
            const doorMaterial = new THREE.MeshLambertMaterial({ color: 0x4A4A4A });
            const door = new THREE.Mesh(doorGeometry, doorMaterial);
            door.position.set(buildingX, buildingGroundHeight + 1.5, buildingZ - building.depth/2 + 0.1);
            scene.add(door);
        });

        // Create accessible fountain in the center (now that Town Hall is moved)
        this.createFountain(x, z, groundHeight);

        // Create town sign
        this.createTownSign(x, z, groundHeight, "TOWN");
    }

    createVillage(x, z, groundHeight) {
        // Smaller village organized around center where NPCs spawn
        const buildings = [
            { x: 0, z: -10, width: 10, height: 6, depth: 8, color: 0x654321, name: "Blacksmith" },
            { x: -8, z: 5, width: 6, height: 5, depth: 6, color: 0x8B4513, name: "House" },
            { x: 8, z: 5, width: 6, height: 5, depth: 6, color: 0xA0522D, name: "House" },
            { x: 0, z: 15, width: 6, height: 5, depth: 6, color: 0xDEB887, name: "House" }
        ];

        buildings.forEach(building => {
            const buildingGeometry = new THREE.BoxGeometry(building.width, building.height, building.depth);
            const buildingMaterial = new THREE.MeshLambertMaterial({ color: building.color });
            const buildingMesh = new THREE.Mesh(buildingGeometry, buildingMaterial);
            const finalX = x + building.x;
            const finalZ = z + building.z;
            const finalY = groundHeight + building.height/2;

            buildingMesh.position.set(finalX, finalY, finalZ);
            console.log(`🏠 Built ${building.name} at (${finalX}, ${finalY}, ${finalZ})`);
            scene.add(buildingMesh);

            // Add collision - prevents walking through building
            this.addBuildingCollision(x + building.x, z + building.z, groundHeight, building.width, building.height, building.depth);

            // Create pyramid-style roof
            this.createPyramidRoof(x + building.x, z + building.z, groundHeight + building.height, building.width, building.depth, 0x8B0000);
        });

        this.createTownSign(x, z, groundHeight, "VILLAGE");
    }

    createCaveEntrance(x, z, groundHeight) {
        // Create cave entrance
        const caveGeometry = new THREE.SphereGeometry(8, 8, 8, 0, Math.PI);
        const caveMaterial = new THREE.MeshLambertMaterial({ color: 0x2F2F2F });
        const cave = new THREE.Mesh(caveGeometry, caveMaterial);
        cave.position.set(x, groundHeight + 4, z);
        cave.rotation.x = Math.PI / 2;
        scene.add(cave);

        this.createTownSign(x, z + 15, groundHeight, "CAVE");
    }

    createSimpleMarker(x, z, groundHeight, name, key) {
        // Create a simple marker for other locations
        const markerGeometry = new THREE.BoxGeometry(2, 8, 2);
        const markerMaterial = new THREE.MeshLambertMaterial({ color: 0x654321 });
        const marker = new THREE.Mesh(markerGeometry, markerMaterial);
        marker.position.set(x, groundHeight + 4, z);
        scene.add(marker);

        this.createTownSign(x, z, groundHeight, name.toUpperCase());
    }

    createTownSign(x, z, groundHeight, text) {
        // Create a simple sign post
        const postGeometry = new THREE.BoxGeometry(0.3, 4, 0.3);
        const postMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const post = new THREE.Mesh(postGeometry, postMaterial);
        post.position.set(x - 8, groundHeight + 2, z - 8);
        scene.add(post);

        // Sign board
        const signGeometry = new THREE.BoxGeometry(4, 2, 0.2);
        const signMaterial = new THREE.MeshLambertMaterial({ color: 0xF5DEB3 });
        const sign = new THREE.Mesh(signGeometry, signMaterial);
        sign.position.set(x - 8, groundHeight + 3, z - 8);
        scene.add(sign);
    }

    createPyramidRoof(x, z, baseY, width, depth, color = 0x8B7355) {
        // Create a pyramid-style roof using blocks
        const roofMaterial = new THREE.MeshLambertMaterial({ color: color });

        // Calculate how many layers we need (smaller buildings = fewer layers)
        const maxSize = Math.max(width, depth);
        const layers = Math.ceil(maxSize / 3); // Roughly 1 layer per 3 blocks

        for (let layer = 0; layer < layers; layer++) {
            // Each layer gets smaller
            const layerWidth = Math.max(2, width - layer * 2);
            const layerDepth = Math.max(2, depth - layer * 2);

            // Stop if the layer would be too small
            if (layerWidth <= 0 || layerDepth <= 0) break;

            // Create the roof block for this layer
            const roofGeometry = new THREE.BoxGeometry(layerWidth, 1, layerDepth);
            const roofBlock = new THREE.Mesh(roofGeometry, roofMaterial);

            // Position each layer higher and more centered
            roofBlock.position.set(
                x,
                baseY + layer + 0.5,
                z
            );

            scene.add(roofBlock);
        }
    }

    createFountain(x, z, groundHeight) {
        // Create an accessible fountain in the town center
        // Water pool (blue block at ground level)
        const waterGeometry = new THREE.BoxGeometry(6, 0.3, 6);
        const waterMaterial = new THREE.MeshLambertMaterial({ color: 0x4169E1 });
        const water = new THREE.Mesh(waterGeometry, waterMaterial);
        water.position.set(x, groundHeight + 0.15, z);
        scene.add(water);

        // Fountain spout (gray cylinder in center)
        const spoutGeometry = new THREE.CylinderGeometry(0.5, 0.5, 2, 8);
        const spoutMaterial = new THREE.MeshLambertMaterial({ color: 0x708090 });
        const spout = new THREE.Mesh(spoutGeometry, spoutMaterial);
        spout.position.set(x, groundHeight + 1, z);
        scene.add(spout);

        // Add collision for fountain (smaller area so people can walk around it)
        this.addBuildingCollision(x, z, groundHeight, 4, 1, 4);
    }

    createOasis(x, z, groundHeight) {
        // Create desert oasis with palm trees and water
        // Large water pool
        const waterGeometry = new THREE.BoxGeometry(12, 0.5, 12);
        const waterMaterial = new THREE.MeshLambertMaterial({ color: 0x4169E1 });
        const water = new THREE.Mesh(waterGeometry, waterMaterial);
        water.position.set(x, groundHeight + 0.25, z);
        scene.add(water);

        // Palm trees around oasis
        const palmPositions = [
            { x: -8, z: -8 }, { x: 8, z: -8 }, { x: -8, z: 8 }, { x: 8, z: 8 },
            { x: 0, z: -10 }, { x: 0, z: 10 }, { x: -10, z: 0 }, { x: 10, z: 0 }
        ];

        palmPositions.forEach(pos => {
            // Palm trunk
            const trunkGeometry = new THREE.BoxGeometry(0.8, 8, 0.8);
            const trunkMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
            const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
            trunk.position.set(x + pos.x, groundHeight + 4, z + pos.z);
            scene.add(trunk);

            // Palm leaves
            const leavesGeometry = new THREE.BoxGeometry(4, 1, 4);
            const leavesMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 });
            const leaves = new THREE.Mesh(leavesGeometry, leavesMaterial);
            leaves.position.set(x + pos.x, groundHeight + 8.5, z + pos.z);
            scene.add(leaves);
        });

        this.createTownSign(x, z + 15, groundHeight, "OASIS");
    }

    createFortress(x, z, groundHeight) {
        // Create fortress with walls and towers
        // Main keep
        const keepGeometry = new THREE.BoxGeometry(16, 12, 16);
        const keepMaterial = new THREE.MeshLambertMaterial({ color: 0x696969 });
        const keep = new THREE.Mesh(keepGeometry, keepMaterial);
        keep.position.set(x, groundHeight + 6, z);
        scene.add(keep);

        // Corner towers
        const towerPositions = [
            { x: -12, z: -12 }, { x: 12, z: -12 }, { x: -12, z: 12 }, { x: 12, z: 12 }
        ];

        towerPositions.forEach(pos => {
            const towerGeometry = new THREE.BoxGeometry(6, 16, 6);
            const tower = new THREE.Mesh(towerGeometry, keepMaterial);
            tower.position.set(x + pos.x, groundHeight + 8, z + pos.z);
            scene.add(tower);
        });

        // Walls connecting towers
        const wallGeometry = new THREE.BoxGeometry(24, 8, 2);
        const wallMaterial = new THREE.MeshLambertMaterial({ color: 0x808080 });

        // North and south walls
        const northWall = new THREE.Mesh(wallGeometry, wallMaterial);
        northWall.position.set(x, groundHeight + 4, z - 12);
        scene.add(northWall);

        const southWall = new THREE.Mesh(wallGeometry, wallMaterial);
        southWall.position.set(x, groundHeight + 4, z + 12);
        scene.add(southWall);

        // East and west walls
        const eastWall = new THREE.Mesh(new THREE.BoxGeometry(2, 8, 24), wallMaterial);
        eastWall.position.set(x + 12, groundHeight + 4, z);
        scene.add(eastWall);

        const westWall = new THREE.Mesh(new THREE.BoxGeometry(2, 8, 24), wallMaterial);
        westWall.position.set(x - 12, groundHeight + 4, z);
        scene.add(westWall);

        // Add collision for fortress
        this.addBuildingCollision(x, z, groundHeight, 24, 12, 24);

        this.createTownSign(x, z + 20, groundHeight, "FORTRESS");
    }

    initializeNPCs() {
        // Initialize NPC manager if the class is available
        if (typeof NPCManager !== 'undefined') {
            this.npcManager = new NPCManager();

            // Don't spawn NPCs yet - we'll add them after we build houses
            // this.spawnNPCsAtTownLocations();

            console.log('NPCs initialized successfully!');
        } else {
            console.warn('NPCManager not available - NPCs not initialized');
        }
    }

    spawnNPCsAtTownLocations() {
        // Spawn NPCs at the actual fixed world town/village locations
        const locations = Object.values(this.STORY_LOCATIONS);

        locations.forEach(location => {
            if (location.type === 'village' || location.type === 'town' || location.type === 'outpost') {
                console.log(`Spawning NPCs at ${location.name} (${location.x}, ${location.z})`);

                // Spawn 3-5 NPCs per settlement
                const npcCount = 3 + Math.floor(Math.random() * 3);

                for (let i = 0; i < npcCount; i++) {
                    // Spawn NPCs close to the center (within 10 blocks)
                    const offsetX = (Math.random() - 0.5) * 20; // ±10 blocks
                    const offsetZ = (Math.random() - 0.5) * 20;

                    const spawnX = location.x + offsetX;
                    const spawnZ = location.z + offsetZ;

                    // Random brown clothing and appropriate names
                    const clothingColors = [0x654321, 0x8B4513, 0xA0522D, 0x964B00, 0x6F4E37, 0x8B7355];
                    const clothingColor = clothingColors[Math.floor(Math.random() * clothingColors.length)];

                    let names;
                    if (location.type === 'town') {
                        names = ["Town Guard", "Shopkeeper", "Merchant", "Citizen", "Mayor", "Clerk"];
                    } else if (location.type === 'outpost') {
                        names = ["Woodcutter", "Logger", "Forester", "Ranger", "Scout"];
                    } else {
                        names = ["Villager", "Farmer", "Blacksmith", "Baker", "Trader"];
                    }

                    const name = names[Math.floor(Math.random() * names.length)];

                    this.npcManager.spawnNPC(spawnX, spawnZ, clothingColor, name);
                }
            }
        });
    }

    updateNPCs(deltaTime, playerPosition) {
        if (this.npcManager) {
            this.npcManager.update(deltaTime, playerPosition);
        }
    }

    initializeAnimals() {
        console.log('🦌 Initializing animal system...');
        console.log('🦌 AnimalManager available:', typeof AnimalManager !== 'undefined');
        console.log('🦌 Animal class available:', typeof Animal !== 'undefined');

        // Initialize Animal manager if the class is available
        if (typeof AnimalManager !== 'undefined') {
            this.animalManager = new AnimalManager();
            console.log('🦌 AnimalManager created:', this.animalManager);

            // Spawn some initial animals in forest areas
            this.spawnInitialAnimals();

            console.log('🦌 Animals initialized successfully!');
            console.log('🦌 Current animal count:', this.animalManager.animals.length);
        } else {
            console.warn('❌ AnimalManager not available - Animals not initialized');
        }
    }

    spawnInitialAnimals() {
        console.log('🦌 Starting initial animal spawn...');

        // Try spawning animals in a wider area around spawn to find good spots
        let spawnedCount = 0;
        const maxAttempts = 20;

        for (let attempt = 0; attempt < maxAttempts && spawnedCount < 6; attempt++) {
            // Random location around spawn area
            const angle = Math.random() * Math.PI * 2;
            const distance = 30 + Math.random() * 50; // 30-80 blocks from spawn
            const x = Math.cos(angle) * distance;
            const z = Math.sin(angle) * distance;

            const biome = getBiome(Math.floor(x), Math.floor(z));
            console.log(`Attempt ${attempt}: (${Math.floor(x)}, ${Math.floor(z)}) - biome: ${biome}`);

            // Try to spawn using the normal spawn method (with tree detection)
            const animalTypes = ['deer', 'fox', 'squirrel', 'bird', 'boar'];
            const type = animalTypes[Math.floor(Math.random() * animalTypes.length)];

            const animal = this.animalManager.spawnAnimal(x, z, type);
            if (animal) {
                spawnedCount++;
                console.log(`✅ Successfully spawned ${type} at (${Math.floor(x)}, ${Math.floor(z)})`);
            }
        }

        // If we still haven't spawned enough, force spawn some near the player spawn
        if (spawnedCount < 3) {
            console.log('🎯 Force spawning some animals near spawn...');
            const forceLocations = [
                { x: 20, z: 20 },
                { x: -20, z: 20 },
                { x: 20, z: -20 },
            ];

            forceLocations.forEach(location => {
                if (spawnedCount < 6) {
                    const type = ['deer', 'fox', 'squirrel', 'bird', 'boar'][Math.floor(Math.random() * 5)];
                    const animal = new Animal(location.x, location.z, type);
                    this.animalManager.animals.push(animal);
                    spawnedCount++;
                    console.log(`🎯 Force spawned ${type} at (${location.x}, ${location.z})`);
                }
            });
        }

        console.log(`✅ Initial animal spawn complete: ${spawnedCount} animals spawned`);
    }

    updateAnimals(deltaTime, playerPosition) {
        if (this.animalManager) {
            this.animalManager.update(deltaTime, playerPosition);
        }
    }

    createBrownPatch(centerX, centerZ, radius) {
        // Create ONE BIG brown patch instead of many tiny blocks
        console.log(`🟤 Creating solid brown patch at (${centerX}, ${centerZ}) with radius ${radius}`);

        const brownColor = 0x8B4513; // Nice brown color
        const patchSize = radius * 2; // Diameter

        // Create ONE large brown plane
        const geometry = new THREE.PlaneGeometry(patchSize, patchSize);
        const material = new THREE.MeshLambertMaterial({ color: brownColor });
        const brownPatch = new THREE.Mesh(geometry, material);

        // Position it flat on the ground
        const groundHeight = getHeightAt(centerX, centerZ);
        brownPatch.position.set(centerX, groundHeight + 0.01, centerZ);
        brownPatch.rotation.x = -Math.PI / 2; // Rotate to be flat on ground

        scene.add(brownPatch);

        console.log(`🟤 Created solid brown patch at ground level`);
        return brownPatch;
    }

    createBlacksmithHouse(centerX, centerZ) {
        console.log('🔨 Building advanced blacksmith structure...');

        const groundHeight = getHeightAt(centerX, centerZ);

        // Structure dimensions (larger to match your image)
        const width = 12;
        const depth = 8;
        const wallHeight = 4;
        const upperLevel = 2; // Height of upper platform

        // Calculate corners
        const startX = centerX - Math.floor(width / 2);
        const startZ = centerZ - Math.floor(depth / 2);

        // 1. FOUNDATION - Stone brick floor
        for (let x = 0; x < width; x++) {
            for (let z = 0; z < depth; z++) {
                this.placeBlock(startX + x, groundHeight, startZ + z, 'stone_brick');
            }
        }

        // 2. MAIN WALLS - Mixed stone brick and sandstone
        for (let y = 1; y <= wallHeight; y++) {
            // Back wall - stone brick with sandstone sections
            for (let x = 0; x < width; x++) {
                if (x >= 3 && x <= 8 && y >= 2) {
                    this.placeBlock(startX + x, groundHeight + y, startZ + depth - 1, 'sandstone');
                } else {
                    this.placeBlock(startX + x, groundHeight + y, startZ + depth - 1, 'stone_brick');
                }
            }

            // Left wall - mostly stone brick
            for (let z = 0; z < depth; z++) {
                this.placeBlock(startX, groundHeight + y, startZ + z, 'stone_brick');
            }

            // Right wall - mixed materials
            for (let z = 0; z < depth; z++) {
                if (z >= 2 && z <= 5 && y >= 2) {
                    this.placeBlock(startX + width - 1, groundHeight + y, startZ + z, 'sandstone');
                } else {
                    this.placeBlock(startX + width - 1, groundHeight + y, startZ + z, 'stone_brick');
                }
            }

            // Front wall - partial with opening
            for (let x = 0; x < width; x++) {
                if (x >= 3 && x <= 8) {
                    // Opening for entrance
                    continue;
                }
                this.placeBlock(startX + x, groundHeight + y, startZ, 'stone_brick');
            }
        }

        // 3. UPPER PLATFORM - Raised area like in your image
        const platformStartX = startX + 2;
        const platformStartZ = startZ + 2;
        const platformWidth = width - 4;
        const platformDepth = depth - 4;

        // Platform floor
        for (let x = 0; x < platformWidth; x++) {
            for (let z = 0; z < platformDepth; z++) {
                this.placeBlock(platformStartX + x, groundHeight + wallHeight + 1, platformStartZ + z, 'stone_brick');
            }
        }

        // Platform border/rim
        for (let x = -1; x <= platformWidth; x++) {
            this.placeBlock(platformStartX + x, groundHeight + wallHeight + 1, platformStartZ - 1, 'stone_brick');
            this.placeBlock(platformStartX + x, groundHeight + wallHeight + 1, platformStartZ + platformDepth, 'stone_brick');
        }
        for (let z = 0; z < platformDepth; z++) {
            this.placeBlock(platformStartX - 1, groundHeight + wallHeight + 1, platformStartZ + z, 'stone_brick');
            this.placeBlock(platformStartX + platformWidth, groundHeight + wallHeight + 1, platformStartZ + z, 'stone_brick');
        }

        // 4. WOODEN SUPPORT PILLARS (like in your image)
        const pillarHeight = wallHeight + upperLevel;
        // Front pillars
        for (let y = 1; y <= pillarHeight; y++) {
            this.placeBlock(startX + 4, groundHeight + y, startZ + 1, 'wood_log');
            this.placeBlock(startX + 7, groundHeight + y, startZ + 1, 'wood_log');
        }

        // 5. ENTRANCE STEPS
        this.placeBlock(startX + 5, groundHeight + 1, startZ - 1, 'stone_brick');
        this.placeBlock(startX + 6, groundHeight + 1, startZ - 1, 'stone_brick');

        // 6. INTERIOR FEATURES
        this.createAdvancedBlacksmithInterior(startX, startZ, groundHeight, width, depth);

        console.log('🔨 Advanced blacksmith structure completed!');
    }

    createAdvancedBlacksmithInterior(startX, startZ, groundHeight, width, depth) {
        // Advanced interior matching your image

        // FURNACE with FIRE (left side, like in your image)
        const furnaceX = startX + 2;
        const furnaceZ = startZ + 3;
        this.placeBlock(furnaceX, groundHeight + 1, furnaceZ, 'furnace', 'furnace');

        // Fire effect coming out of furnace (orange glow)
        this.placeBlock(furnaceX - 1, groundHeight + 1, furnaceZ, 'lava', 'fire');
        this.placeBlock(furnaceX - 1, groundHeight + 2, furnaceZ, 'lava', 'fire');

        // ANVIL (center area)
        const anvilX = startX + 6;
        const anvilZ = startZ + 3;
        this.placeBlock(anvilX, groundHeight + 1, anvilZ, 'iron', 'anvil');

        // CRAFTING/WORK AREA (right side with sandstone)
        const workX = startX + width - 3;
        const workZ = startZ + 2;
        this.placeBlock(workX, groundHeight + 1, workZ, 'wood_plank', 'crafting_table');
        this.placeBlock(workX, groundHeight + 1, workZ + 1, 'wood_plank', 'storage');

        // STORAGE CHESTS (back area)
        this.placeBlock(startX + 4, groundHeight + 1, startZ + depth - 2, 'wood_plank', 'storage');
        this.placeBlock(startX + 5, groundHeight + 1, startZ + depth - 2, 'wood_plank', 'storage');

        // TOOL RACK (wooden planks on wall)
        this.placeBlock(startX + 1, groundHeight + 2, startZ + depth - 1, 'wood_plank', 'rack');
        this.placeBlock(startX + 2, groundHeight + 2, startZ + depth - 1, 'wood_plank', 'rack');

        console.log('🔨 Advanced blacksmith interior completed!');
    }

    placeBlock(x, y, z, blockType, itemType = null) {
        // Place a single block with proper color and collision - THINNER blocks
        let color;
        let size = { width: 0.8, height: 0.8, depth: 0.8 }; // Default thinner size

        switch(blockType) {
            case 'cobblestone':
                color = 0x7F7F7F; // Gray cobblestone (like Minecraft)
                size = { width: 0.95, height: 0.95, depth: 0.95 };
                break;
            case 'stone_brick':
                color = 0x999999; // Light gray stone brick (like your image)
                size = { width: 0.98, height: 0.98, depth: 0.98 };
                break;
            case 'sandstone':
                color = 0xF4E4BC; // Yellowish sandstone (like your image)
                size = { width: 0.98, height: 0.98, depth: 0.98 };
                break;
            case 'stone_slab':
                color = 0x999999; // Light gray stone slab
                size = { width: 1, height: 0.5, depth: 1 }; // Half-height slab
                break;
            case 'wood_log':
                color = 0x8B4513; // Dark brown wood log (pillars)
                size = { width: 0.4, height: 0.95, depth: 0.4 }; // Thin pillar
                break;
            case 'iron':
                color = 0x708090; // Steel gray for anvil
                if (itemType === 'anvil') {
                    size = { width: 0.8, height: 0.4, depth: 0.8 }; // Anvil shape
                }
                break;
            case 'furnace':
                color = 0x555555; // Dark gray furnace (like Minecraft)
                size = { width: 0.9, height: 0.9, depth: 0.9 };
                break;
            case 'lava':
                color = 0xFF4500; // Bright orange-red for fire/lava
                size = { width: 0.6, height: 0.6, depth: 0.6 }; // Small fire effect
                break;
            case 'wood_plank':
                color = 0xDEB887; // Light brown wood planks (like Minecraft)
                if (itemType === 'post') {
                    size = { width: 0.3, height: 0.9, depth: 0.3 }; // Thin vertical post
                } else if (itemType === 'storage') {
                    size = { width: 0.9, height: 0.9, depth: 0.9 }; // Storage blocks
                } else if (itemType === 'crafting_table') {
                    size = { width: 0.9, height: 0.7, depth: 0.9 }; // Crafting table
                } else if (itemType === 'rack') {
                    size = { width: 0.9, height: 0.2, depth: 0.2 }; // Wall rack
                }
                break;
            default:
                color = 0x654321; // Default brown
        }

        const geometry = new THREE.BoxGeometry(size.width, size.height, size.depth);
        const material = new THREE.MeshLambertMaterial({ color: color });
        const block = new THREE.Mesh(geometry, material);

        block.position.set(x, y + size.height / 2, z);
        scene.add(block);

        // Add to collision system
        const key = `${x},${z}`;
        if (!this.heightMap.has(key)) {
            this.heightMap.set(key, { height: y, hasTree: false, hasBuilding: false });
        }

        const existing = this.heightMap.get(key);
        if (y + size.height > existing.height) {
            existing.height = y + size.height;
            existing.hasBuilding = true;
        }
    }
}
