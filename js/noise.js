// === NOISE GENERATION ===
// Simple noise implementation for terrain generation

class NoiseGenerator {
    constructor(seed = 12345) {
        this.seed = seed;
    }
    
    hash(x, y) {
        let h = (x * 374761393 + y * 668265263) ^ this.seed;
        h = (h ^ (h >>> 13)) * 1274126177;
        return (h ^ (h >>> 16)) / 4294967296 + 0.5;
    }
    
    noise(x, y) {
        const ix = Math.floor(x);
        const iy = Math.floor(y);
        const fx = x - ix;
        const fy = y - iy;
        
        const a = this.hash(ix, iy);
        const b = this.hash(ix + 1, iy);
        const c = this.hash(ix, iy + 1);
        const d = this.hash(ix + 1, iy + 1);
        
        const i1 = a + (b - a) * fx;
        const i2 = c + (d - c) * fx;
        
        return i1 + (i2 - i1) * fy;
    }
    
    octaveNoise(x, y, octaves = 4) {
        let value = 0;
        let amplitude = 1;
        let frequency = 1;
        let maxValue = 0;
        
        for (let i = 0; i < octaves; i++) {
            value += this.noise(x * frequency, y * frequency) * amplitude;
            maxValue += amplitude;
            amplitude *= 0.5;
            frequency *= 2;
        }
        
        return value / maxValue;
    }
}

// Global noise instance
const noise = new NoiseGenerator();
