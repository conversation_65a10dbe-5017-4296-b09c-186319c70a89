const mysql = require('mysql2/promise');
require('dotenv').config();

async function testMariaDB() {
    console.log('🔍 Testing MariaDB Connection on Debian 12');
    console.log('==========================================');
    
    // Test different connection configurations
    const configs = [
        {
            name: 'Standard Config',
            config: {
                host: 'localhost',
                user: 'beetle',
                password: '4SrTF6CYdFhKYhG',
                database: 'beetle',
                port: 3306
            }
        },
        {
            name: 'MariaDB Specific Config',
            config: {
                host: 'localhost',
                user: 'beetle',
                password: '4SrTF6CYdFhKYhG',
                database: 'beetle',
                port: 3306,
                authPlugins: {
                    mysql_native_password: () => () => Buffer.alloc(0)
                },
                ssl: false,
                charset: 'utf8mb4'
            }
        },
        {
            name: 'Socket Connection (Debian default)',
            config: {
                socketPath: '/var/run/mysqld/mysqld.sock',
                user: 'beetle',
                password: '4SrTF6CYdFhKYhG',
                database: 'beetle'
            }
        }
    ];
    
    for (const test of configs) {
        console.log(`\n🧪 Testing: ${test.name}`);
        console.log('-----------------------------------');
        
        try {
            const connection = await mysql.createConnection(test.config);
            console.log('✅ Connection successful');
            
            // Test basic query
            const [result] = await connection.execute('SELECT VERSION() as version');
            console.log(`✅ MariaDB Version: ${result[0].version}`);
            
            // Test database access
            await connection.execute('USE beetle');
            console.log('✅ Database access successful');
            
            // Test table listing
            const [tables] = await connection.execute('SHOW TABLES');
            console.log(`✅ Tables found: ${tables.length}`);
            
            // Test user table
            const [users] = await connection.execute('SELECT COUNT(*) as count FROM users');
            console.log(`✅ Users table: ${users[0].count} records`);
            
            await connection.end();
            console.log(`✅ ${test.name} - ALL TESTS PASSED`);
            
            // If this config works, show it
            console.log('\n🎉 WORKING CONFIGURATION:');
            console.log(JSON.stringify(test.config, null, 2));
            break;
            
        } catch (error) {
            console.log(`❌ ${test.name} failed:`);
            console.log(`   Code: ${error.code}`);
            console.log(`   Message: ${error.message}`);
            
            if (error.code === 'ECONNREFUSED') {
                console.log('   💡 MariaDB might not be running');
            } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
                console.log('   💡 Username/password incorrect');
            } else if (error.code === 'ENOENT') {
                console.log('   💡 Socket file not found');
            }
        }
    }
    
    console.log('\n🔧 MariaDB Service Check');
    console.log('-------------------------');
    console.log('To check MariaDB status:');
    console.log('   sudo systemctl status mariadb');
    console.log('');
    console.log('To start MariaDB:');
    console.log('   sudo systemctl start mariadb');
    console.log('');
    console.log('To connect manually:');
    console.log('   mysql -u beetle -p4SrTF6CYdFhKYhG beetle');
    console.log('');
    console.log('To check socket location:');
    console.log('   sudo find /var -name "*.sock" 2>/dev/null | grep mysql');
}

testMariaDB().catch(console.error);
