-- Beetle3D Game Database Schema
-- Run this SQL to create the required tables

USE beetle;

-- Users table for authentication
CREATE TABLE IF NOT EXISTS users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    is_online BOOLEAN DEFAULT FALSE,
    INDEX idx_username (username),
    INDEX idx_email (email)
);

-- Game saves table for player progress
CREATE TABLE IF NOT EXISTS game_saves (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    save_name VARCHAR(100) DEFAULT 'Auto Save',
    position_x FLOAT DEFAULT 0,
    position_y FLOAT DEFAULT 20,
    position_z FLOAT DEFAULT 0,
    health INT DEFAULT 100,
    stamina INT DEFAULT 100,
    inventory_data JSON,
    hotbar_data JSON,
    kills INT DEFAULT 0,
    score INT DEFAULT 0,
    play_time INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_updated_at (updated_at)
);

-- Multiplayer rooms for game sessions
CREATE TABLE IF NOT EXISTS game_rooms (
    id INT PRIMARY KEY AUTO_INCREMENT,
    room_name VARCHAR(100) NOT NULL,
    host_user_id INT NOT NULL,
    max_players INT DEFAULT 4,
    current_players INT DEFAULT 1,
    is_public BOOLEAN DEFAULT TRUE,
    room_password VARCHAR(255) NULL,
    world_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (host_user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_host_user_id (host_user_id),
    INDEX idx_is_public (is_public)
);

-- Room participants for tracking who's in which room
CREATE TABLE IF NOT EXISTS room_participants (
    id INT PRIMARY KEY AUTO_INCREMENT,
    room_id INT NOT NULL,
    user_id INT NOT NULL,
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (room_id) REFERENCES game_rooms(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_room_user (room_id, user_id),
    INDEX idx_room_id (room_id),
    INDEX idx_user_id (user_id)
);

-- Player statistics
CREATE TABLE IF NOT EXISTS player_stats (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    total_play_time INT DEFAULT 0,
    total_kills INT DEFAULT 0,
    total_score INT DEFAULT 0,
    blocks_placed INT DEFAULT 0,
    blocks_broken INT DEFAULT 0,
    games_played INT DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_stats (user_id)
);

-- Insert default admin user (password: admin123)
INSERT IGNORE INTO users (username, password_hash) VALUES 
('admin', '$2b$10$rQZ8kHWKtGKVQZ8kHWKtGOuYQZ8kHWKtGKVQZ8kHWKtGKVQZ8kHWKt');

-- Create indexes for better performance
CREATE INDEX idx_users_online ON users(is_online);
CREATE INDEX idx_game_saves_updated ON game_saves(updated_at);
CREATE INDEX idx_rooms_public ON game_rooms(is_public, current_players);
