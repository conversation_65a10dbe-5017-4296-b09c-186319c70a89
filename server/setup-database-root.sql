-- Beetle3D Database Setup with Root Access
-- Run this as MySQL root user to create everything properly

-- Create database
CREATE DATABASE IF NOT EXISTS beetle;
USE beetle;

-- Create beetle user with full permissions
CREATE USER IF NOT EXISTS 'beetle'@'localhost' IDENTIFIED BY '4SrTF6CYdFhKYhG';
GRANT ALL PRIVILEGES ON beetle.* TO 'beetle'@'localhost';
FLUSH PRIVILEGES;

-- Users table for authentication
DROP TABLE IF EXISTS users;
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    is_online BOOLEAN DEFAULT FALSE,
    INDEX idx_username (username),
    INDEX idx_email (email)
);

-- Game saves table for player progress
DROP TABLE IF EXISTS game_saves;
CREATE TABLE game_saves (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    save_name VARCHAR(100) DEFAULT 'Auto Save',
    position_x FLOAT DEFAULT 0,
    position_y FLOAT DEFAULT 20,
    position_z FLOAT DEFAULT 0,
    health INT DEFAULT 100,
    stamina INT DEFAULT 100,
    inventory_data JSON,
    hotbar_data JSON,
    kills INT DEFAULT 0,
    score INT DEFAULT 0,
    play_time INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_updated_at (updated_at)
);

-- Multiplayer rooms for game sessions (ready for Ctrl+J feature)
DROP TABLE IF EXISTS game_rooms;
CREATE TABLE game_rooms (
    id INT PRIMARY KEY AUTO_INCREMENT,
    room_name VARCHAR(100) NOT NULL,
    room_code VARCHAR(10) UNIQUE NOT NULL,
    host_user_id INT NOT NULL,
    max_players INT DEFAULT 4,
    current_players INT DEFAULT 1,
    is_public BOOLEAN DEFAULT TRUE,
    room_password VARCHAR(255) NULL,
    world_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (host_user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_host_user_id (host_user_id),
    INDEX idx_room_code (room_code),
    INDEX idx_is_public (is_public)
);

-- Room participants for tracking who's in which room
DROP TABLE IF EXISTS room_participants;
CREATE TABLE room_participants (
    id INT PRIMARY KEY AUTO_INCREMENT,
    room_id INT NOT NULL,
    user_id INT NOT NULL,
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    player_position_x FLOAT DEFAULT 0,
    player_position_y FLOAT DEFAULT 20,
    player_position_z FLOAT DEFAULT 0,
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (room_id) REFERENCES game_rooms(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_room_user (room_id, user_id),
    INDEX idx_room_id (room_id),
    INDEX idx_user_id (user_id)
);

-- Player statistics
DROP TABLE IF EXISTS player_stats;
CREATE TABLE player_stats (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    total_play_time INT DEFAULT 0,
    total_kills INT DEFAULT 0,
    total_score INT DEFAULT 0,
    blocks_placed INT DEFAULT 0,
    blocks_broken INT DEFAULT 0,
    games_played INT DEFAULT 0,
    rooms_created INT DEFAULT 0,
    rooms_joined INT DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_stats (user_id)
);

-- Test data - create a test user (password: test123)
INSERT IGNORE INTO users (username, password_hash) VALUES 
('testuser', '$2b$10$rQZ8kHWKtGKVQZ8kHWKtGOuYQZ8kHWKtGKVQZ8kHWKtGKVQZ8kHWKt');

-- Show created tables
SHOW TABLES;

-- Show user permissions
SHOW GRANTS FOR 'beetle'@'localhost';

-- Test insert to verify permissions
INSERT IGNORE INTO users (username, password_hash) VALUES 
('permtest', '$2b$10$test.hash.for.permission.testing');

SELECT 'Database setup completed successfully!' as status;
SELECT COUNT(*) as user_count FROM users;
SELECT COUNT(*) as tables_count FROM information_schema.tables WHERE table_schema = 'beetle';
