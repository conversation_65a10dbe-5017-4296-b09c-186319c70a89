const mysql = require('mysql2/promise');
require('dotenv').config();

async function testVirtualMinDB() {
    console.log('🔍 Testing VirtualMin MariaDB Configuration');
    console.log('==========================================');
    
    // VirtualMin typically uses these naming patterns
    const possibleConfigs = [
        {
            name: 'Current Config (beetle/beetle)',
            config: {
                host: 'localhost',
                user: 'beetle',
                password: '4SrTF6CYdFhKYhG',
                database: 'beetle',
                port: 3306
            }
        },
        {
            name: 'VirtualMin Pattern 1 (beetle3d_beetle)',
            config: {
                host: 'localhost',
                user: 'beetle3d_beetle',
                password: '4SrTF6CYdFhKYhG',
                database: 'beetle3d_beetle',
                port: 3306
            }
        },
        {
            name: 'VirtualMin Pattern 2 (domain prefix)',
            config: {
                host: 'localhost',
                user: 'beetle3d_db',
                password: '4SrTF6CYdFhKYhG',
                database: 'beetle3d_db',
                port: 3306
            }
        },
        {
            name: 'VirtualMin with domain host',
            config: {
                host: 'beetle3d.com',
                user: 'beetle',
                password: '4SrTF6CYdFhKYhG',
                database: 'beetle',
                port: 3306
            }
        }
    ];
    
    console.log('🔍 Checking what databases exist...');
    
    // First, try to connect as root to see all databases
    try {
        const rootConnection = await mysql.createConnection({
            host: 'localhost',
            user: 'root',
            password: '', // VirtualMin sometimes has empty root password
            port: 3306
        });
        
        console.log('✅ Connected as root');
        
        const [databases] = await rootConnection.execute('SHOW DATABASES');
        console.log('\n📋 Available databases:');
        databases.forEach(db => {
            const dbName = Object.values(db)[0];
            if (dbName.includes('beetle') || dbName.includes('3d')) {
                console.log(`   🎯 ${dbName} (potential match)`);
            } else {
                console.log(`   - ${dbName}`);
            }
        });
        
        // Check users
        const [users] = await rootConnection.execute('SELECT User, Host FROM mysql.user WHERE User LIKE "%beetle%"');
        console.log('\n👤 Beetle-related users:');
        users.forEach(user => {
            console.log(`   - ${user.User}@${user.Host}`);
        });
        
        await rootConnection.end();
        
    } catch (error) {
        console.log('⚠️ Could not connect as root:', error.message);
        console.log('   (This is normal if root password is set)');
    }
    
    console.log('\n🧪 Testing connection configurations...');
    
    for (const test of possibleConfigs) {
        console.log(`\n🔧 Testing: ${test.name}`);
        console.log('-----------------------------------');
        
        try {
            const connection = await mysql.createConnection(test.config);
            console.log('✅ Connection successful');
            
            // Test database access
            const [result] = await connection.execute('SELECT DATABASE() as current_db');
            console.log(`✅ Current database: ${result[0].current_db}`);
            
            // Test table listing
            const [tables] = await connection.execute('SHOW TABLES');
            console.log(`✅ Tables found: ${tables.length}`);
            
            if (tables.length > 0) {
                console.log('   Tables:');
                tables.forEach(table => {
                    const tableName = Object.values(table)[0];
                    console.log(`   - ${tableName}`);
                });
                
                // If we find the users table, test it
                const userTable = tables.find(table => 
                    Object.values(table)[0] === 'users'
                );
                
                if (userTable) {
                    const [users] = await connection.execute('SELECT COUNT(*) as count FROM users');
                    console.log(`✅ Users table: ${users[0].count} records`);
                    
                    // Test insert capability
                    try {
                        await connection.execute(
                            'INSERT IGNORE INTO users (username, password_hash) VALUES (?, ?)',
                            ['test_virtualmin', '$2b$10$test.hash']
                        );
                        console.log('✅ Insert permission: Working');
                    } catch (insertError) {
                        console.log('❌ Insert permission:', insertError.message);
                    }
                }
            }
            
            await connection.end();
            console.log(`🎉 ${test.name} - WORKING!`);
            
            // Show the working config
            console.log('\n🎯 WORKING VIRTUALMIN CONFIG:');
            console.log('Update your .env file with:');
            console.log(`DB_HOST=${test.config.host}`);
            console.log(`DB_USER=${test.config.user}`);
            console.log(`DB_PASSWORD=${test.config.password}`);
            console.log(`DB_NAME=${test.config.database}`);
            console.log(`DB_PORT=${test.config.port}`);
            
            return; // Exit on first working config
            
        } catch (error) {
            console.log(`❌ ${test.name} failed:`);
            console.log(`   Code: ${error.code}`);
            console.log(`   Message: ${error.message}`);
        }
    }
    
    console.log('\n🔧 VirtualMin Database Troubleshooting');
    console.log('=====================================');
    console.log('1. Check VirtualMin database settings:');
    console.log('   - Login to VirtualMin panel');
    console.log('   - Go to beetle3d.com domain');
    console.log('   - Check "Edit Databases" section');
    console.log('');
    console.log('2. Common VirtualMin database patterns:');
    console.log('   - Database: domainname_dbname');
    console.log('   - User: domainname_username');
    console.log('   - Host: localhost or domain name');
    console.log('');
    console.log('3. Check MariaDB service:');
    console.log('   sudo systemctl status mariadb');
    console.log('');
    console.log('4. VirtualMin logs:');
    console.log('   tail -f /var/log/virtualmin/*');
}

testVirtualMinDB().catch(console.error);
