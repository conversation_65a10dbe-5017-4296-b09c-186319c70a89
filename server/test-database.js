const mysql = require('mysql2/promise');
require('dotenv').config();

async function testDatabase() {
    console.log('🔍 Testing Beetle3D Database Connection...');
    console.log('=====================================');
    
    const dbConfig = {
        host: process.env.DB_HOST || 'localhost',
        user: process.env.DB_USER || 'beetle',
        password: process.env.DB_PASSWORD || '4SrTF6CYdFhKYhG',
        database: process.env.DB_NAME || 'beetle'
    };
    
    console.log('📋 Database Config:');
    console.log(`   Host: ${dbConfig.host}`);
    console.log(`   User: ${dbConfig.user}`);
    console.log(`   Database: ${dbConfig.database}`);
    console.log(`   Password: ${'*'.repeat(dbConfig.password.length)}`);
    console.log('');
    
    try {
        // Test connection
        console.log('🔌 Testing connection...');
        const connection = await mysql.createConnection(dbConfig);
        console.log('✅ Connection successful!');
        
        // Test database selection
        console.log('🗄️ Testing database access...');
        await connection.execute('USE beetle');
        console.log('✅ Database access successful!');
        
        // Test table listing
        console.log('📋 Checking tables...');
        const [tables] = await connection.execute('SHOW TABLES');
        console.log(`✅ Found ${tables.length} tables:`);
        tables.forEach(table => {
            console.log(`   - ${Object.values(table)[0]}`);
        });
        console.log('');
        
        // Test user permissions
        console.log('🔐 Testing permissions...');
        
        // Test SELECT
        try {
            const [users] = await connection.execute('SELECT COUNT(*) as count FROM users');
            console.log(`✅ SELECT permission: Found ${users[0].count} users`);
        } catch (error) {
            console.log('❌ SELECT permission failed:', error.message);
        }
        
        // Test INSERT
        try {
            await connection.execute(
                'INSERT IGNORE INTO users (username, password_hash) VALUES (?, ?)',
                ['test_permission_user', '$2b$10$test.hash.for.testing']
            );
            console.log('✅ INSERT permission: Working');
        } catch (error) {
            console.log('❌ INSERT permission failed:', error.message);
        }
        
        // Test UPDATE
        try {
            await connection.execute(
                'UPDATE users SET last_login = NOW() WHERE username = ?',
                ['test_permission_user']
            );
            console.log('✅ UPDATE permission: Working');
        } catch (error) {
            console.log('❌ UPDATE permission failed:', error.message);
        }
        
        // Test user creation (for registration)
        console.log('');
        console.log('👤 Testing user registration simulation...');
        try {
            const testUsername = `test_${Date.now()}`;
            const testPassword = '$2b$10$test.hash.for.registration.test';
            
            const [result] = await connection.execute(
                'INSERT INTO users (username, password_hash) VALUES (?, ?)',
                [testUsername, testPassword]
            );
            
            console.log(`✅ User creation: Created user ID ${result.insertId}`);
            
            // Test user lookup (for login)
            const [foundUsers] = await connection.execute(
                'SELECT id, username FROM users WHERE username = ?',
                [testUsername]
            );
            
            if (foundUsers.length > 0) {
                console.log(`✅ User lookup: Found user ${foundUsers[0].username}`);
            } else {
                console.log('❌ User lookup: User not found after creation');
            }
            
            // Clean up test user
            await connection.execute('DELETE FROM users WHERE username = ?', [testUsername]);
            console.log('✅ Cleanup: Test user removed');
            
        } catch (error) {
            console.log('❌ User registration test failed:', error.message);
        }
        
        // Test game save simulation
        console.log('');
        console.log('💾 Testing game save simulation...');
        try {
            // Find a test user
            const [testUsers] = await connection.execute('SELECT id FROM users LIMIT 1');
            
            if (testUsers.length > 0) {
                const userId = testUsers[0].id;
                
                // Test save creation
                await connection.execute(`
                    INSERT INTO game_saves (
                        user_id, position_x, position_y, position_z,
                        health, stamina, inventory_data, hotbar_data,
                        kills, score, play_time
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ON DUPLICATE KEY UPDATE
                        position_x = VALUES(position_x),
                        updated_at = NOW()
                `, [userId, 10.5, 20.0, 5.5, 100, 100, '[]', '[]', 0, 0, 1000]);
                
                console.log('✅ Game save: Save operation successful');
                
                // Test save loading
                const [saves] = await connection.execute(
                    'SELECT * FROM game_saves WHERE user_id = ? ORDER BY updated_at DESC LIMIT 1',
                    [userId]
                );
                
                if (saves.length > 0) {
                    console.log('✅ Game load: Load operation successful');
                    console.log(`   Position: (${saves[0].position_x}, ${saves[0].position_y}, ${saves[0].position_z})`);
                } else {
                    console.log('❌ Game load: No save data found');
                }
            } else {
                console.log('⚠️ No test users found for save testing');
            }
        } catch (error) {
            console.log('❌ Game save test failed:', error.message);
        }
        
        await connection.end();
        
        console.log('');
        console.log('🎉 Database test completed!');
        console.log('=====================================');
        
    } catch (error) {
        console.error('❌ Database test failed:', error.message);
        console.log('');
        console.log('🔧 Possible solutions:');
        console.log('1. Run setup-database-root.sql as MySQL root user');
        console.log('2. Check if MySQL server is running');
        console.log('3. Verify beetle user exists and has permissions');
        console.log('4. Check if beetle database exists');
        console.log('');
        console.log('Commands to try:');
        console.log('   sudo mysql < server/setup-database-root.sql');
        console.log('   sudo systemctl start mysql');
        console.log('   mysql -u root -p');
    }
}

testDatabase();
