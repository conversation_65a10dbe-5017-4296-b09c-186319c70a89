const express = require('express');
const mysql = require('mysql2/promise');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const cors = require('cors');
const { Server } = require('socket.io');
const http = require('http');
require('dotenv').config();

const app = express();
const server = http.createServer(app);

// Socket.IO setup for real-time multiplayer
const io = new Server(server, {
    cors: {
        origin: process.env.CLIENT_URL || "http://localhost:8000",
        methods: ["GET", "POST"]
    }
});

// Middleware
app.use(cors({
    origin: process.env.CLIENT_URL || "http://localhost:8000",
    credentials: true
}));
app.use(express.json());
app.use(express.static('../')); // Serve game files

// Database connection - VirtualMin MariaDB configuration
const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'beetle',
    password: process.env.DB_PASSWORD || '4SrTF6CYdFhKYhG',
    database: process.env.DB_NAME || 'beetle',
    port: parseInt(process.env.DB_PORT) || 3306,

    // VirtualMin/MariaDB compatibility settings
    authPlugins: {
        mysql_native_password: () => () => Buffer.alloc(0),
        mysql_clear_password: () => () => Buffer.alloc(0),
        caching_sha2_password: () => () => Buffer.alloc(0)
    },

    // Connection pool settings (conservative for VirtualMin)
    waitForConnections: true,
    connectionLimit: 5,  // Lower limit for shared hosting
    queueLimit: 0,
    acquireTimeout: 30000,
    timeout: 30000,
    reconnect: true,

    // VirtualMin MariaDB compatibility
    supportBigNumbers: true,
    bigNumberStrings: true,
    dateStrings: true,

    // Charset for proper UTF-8 support
    charset: 'utf8mb4',

    // SSL settings (VirtualMin usually doesn't use SSL for local connections)
    ssl: false,

    // Additional VirtualMin compatibility
    multipleStatements: false,
    flags: ['-FOUND_ROWS']
};

const pool = mysql.createPool(dbConfig);

// Test database connection with detailed MariaDB debugging
async function testConnection() {
    console.log('🔍 Testing MariaDB connection...');
    console.log('📋 Connection config:');
    console.log(`   Host: ${dbConfig.host}`);
    console.log(`   Port: ${dbConfig.port}`);
    console.log(`   User: ${dbConfig.user}`);
    console.log(`   Database: ${dbConfig.database}`);
    console.log(`   SSL: ${dbConfig.ssl}`);

    try {
        const connection = await pool.getConnection();
        console.log('✅ MariaDB connection successful');

        // Test database selection
        await connection.execute('USE beetle');
        console.log('✅ Database selection successful');

        // Test table access
        const [tables] = await connection.execute('SHOW TABLES');
        console.log(`✅ Found ${tables.length} tables in database`);

        // Test user table specifically
        const [users] = await connection.execute('SELECT COUNT(*) as count FROM users');
        console.log(`✅ Users table accessible: ${users[0].count} users found`);

        connection.release();
        console.log('✅ Database test completed successfully');

    } catch (error) {
        console.error('❌ MariaDB connection failed:');
        console.error(`   Error Code: ${error.code}`);
        console.error(`   Error Message: ${error.message}`);
        console.error(`   SQL State: ${error.sqlState || 'N/A'}`);

        // Common MariaDB connection issues
        if (error.code === 'ER_ACCESS_DENIED_ERROR') {
            console.error('🔧 Fix: Check username/password in .env file');
        } else if (error.code === 'ECONNREFUSED') {
            console.error('🔧 Fix: Start MariaDB service - sudo systemctl start mariadb');
        } else if (error.code === 'ER_BAD_DB_ERROR') {
            console.error('🔧 Fix: Create database - CREATE DATABASE beetle;');
        }

        process.exit(1);
    }
}

// JWT middleware for protected routes
const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ error: 'Access token required' });
    }

    jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
        if (err) {
            return res.status(403).json({ error: 'Invalid token' });
        }
        req.user = user;
        next();
    });
};

// === AUTHENTICATION ROUTES ===

// Register new user
app.post('/api/register', async (req, res) => {
    console.log('\n📝 REGISTRATION ATTEMPT');
    console.log('=======================');

    try {
        const { username, password, email } = req.body;
        console.log(`👤 Username: "${username}"`);
        console.log(`📧 Email: "${email || 'none'}"`);
        console.log(`🔑 Password provided: ${password ? 'YES' : 'NO'} (length: ${password ? password.length : 0})`);

        // Validation
        if (!username || !password) {
            console.log('❌ Missing username or password');
            return res.status(400).json({ error: 'Username and password required' });
        }

        if (username.length < 3) {
            console.log('❌ Username too short');
            return res.status(400).json({ error: 'Username must be at least 3 characters' });
        }

        if (password.length < 4) {
            console.log('❌ Password too short');
            return res.status(400).json({ error: 'Password must be at least 4 characters' });
        }

        // Check if user exists
        console.log('🔍 Checking if user already exists...');
        const [existing] = await pool.execute(
            'SELECT id, username FROM users WHERE username = ? OR email = ?',
            [username, email || '']
        );

        console.log(`📊 Found ${existing.length} existing users with same username/email`);

        if (existing.length > 0) {
            console.log(`❌ User already exists: ${existing[0].username}`);
            return res.status(400).json({ error: 'Username or email already exists' });
        }

        // Hash password
        console.log('🔐 Hashing password...');
        const saltRounds = 10;
        const passwordHash = await bcrypt.hash(password, saltRounds);
        console.log(`🔐 Password hashed: ${passwordHash.substring(0, 30)}...`);

        // Create user
        console.log('💾 Creating user in database...');
        const [result] = await pool.execute(
            'INSERT INTO users (username, email, password_hash) VALUES (?, ?, ?)',
            [username, email || null, passwordHash]
        );

        console.log(`✅ User created with ID: ${result.insertId}`);

        // Create default player stats
        console.log('📊 Creating player stats...');
        await pool.execute(
            'INSERT INTO player_stats (user_id) VALUES (?)',
            [result.insertId]
        );

        // Generate JWT token
        console.log('🎫 Generating JWT token...');
        const token = jwt.sign(
            { userId: result.insertId, username },
            process.env.JWT_SECRET,
            { expiresIn: '7d' }
        );

        console.log('✅ REGISTRATION SUCCESSFUL!');
        console.log(`🎫 Token generated: ${token.substring(0, 30)}...`);

        res.status(201).json({
            success: true,
            message: 'User created successfully',
            token,
            user: {
                id: result.insertId,
                username,
                email: email || null
            }
        });

    } catch (error) {
        console.error('❌ REGISTRATION ERROR:', error);
        console.error('Stack trace:', error.stack);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Login user
app.post('/api/login', async (req, res) => {
    console.log('\n🔐 LOGIN ATTEMPT');
    console.log('================');

    try {
        const { username, password } = req.body;
        console.log(`👤 Username: "${username}"`);
        console.log(`🔑 Password provided: ${password ? 'YES' : 'NO'} (length: ${password ? password.length : 0})`);

        if (!username || !password) {
            console.log('❌ Missing username or password');
            return res.status(400).json({ error: 'Username and password required' });
        }

        // Find user
        console.log('🔍 Searching database for user...');
        const [users] = await pool.execute(
            'SELECT id, username, email, password_hash, created_at FROM users WHERE username = ?',
            [username]
        );

        console.log(`📊 Database query result: ${users.length} users found`);

        if (users.length === 0) {
            console.log('❌ User not found in database');

            // Show all users for debugging
            const [allUsers] = await pool.execute('SELECT username FROM users');
            console.log(`📋 Available users in database: ${allUsers.map(u => u.username).join(', ')}`);

            return res.status(401).json({ error: 'Invalid credentials' });
        }

        const user = users[0];
        console.log(`✅ User found: ID ${user.id}, created ${user.created_at}`);
        console.log(`🔐 Stored hash: ${user.password_hash.substring(0, 30)}...`);

        // Verify password
        console.log('🔍 Verifying password with bcrypt...');
        const validPassword = await bcrypt.compare(password, user.password_hash);
        console.log(`🔐 Password verification result: ${validPassword}`);

        if (!validPassword) {
            console.log('❌ Password verification failed');

            // Debug: try hashing the provided password to see what it would look like
            const testHash = await bcrypt.hash(password, 10);
            console.log(`🧪 Test hash of provided password: ${testHash.substring(0, 30)}...`);

            return res.status(401).json({ error: 'Invalid credentials' });
        }

        // Update last login and online status
        console.log('📝 Updating user login status...');
        await pool.execute(
            'UPDATE users SET last_login = NOW(), is_online = TRUE WHERE id = ?',
            [user.id]
        );

        // Generate JWT token
        console.log('🎫 Generating JWT token...');
        const token = jwt.sign(
            { userId: user.id, username: user.username },
            process.env.JWT_SECRET,
            { expiresIn: '7d' }
        );

        console.log('✅ LOGIN SUCCESSFUL!');
        console.log(`🎫 Token generated: ${token.substring(0, 30)}...`);

        res.json({
            success: true,
            message: 'Login successful',
            token,
            user: {
                id: user.id,
                username: user.username,
                email: user.email
            }
        });

    } catch (error) {
        console.error('❌ LOGIN ERROR:', error);
        console.error('Stack trace:', error.stack);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Logout user
app.post('/api/logout', authenticateToken, async (req, res) => {
    try {
        await pool.execute(
            'UPDATE users SET is_online = FALSE WHERE id = ?',
            [req.user.userId]
        );

        res.json({ success: true, message: 'Logged out successfully' });
    } catch (error) {
        console.error('Logout error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// === GAME SAVE ROUTES ===

// Get user's game save
app.get('/api/save', authenticateToken, async (req, res) => {
    try {
        const [saves] = await pool.execute(
            'SELECT * FROM game_saves WHERE user_id = ? ORDER BY updated_at DESC LIMIT 1',
            [req.user.userId]
        );

        if (saves.length === 0) {
            return res.json({ success: true, save: null });
        }

        const save = saves[0];
        res.json({
            success: true,
            save: {
                position: {
                    x: save.position_x,
                    y: save.position_y,
                    z: save.position_z
                },
                health: save.health,
                stamina: save.stamina,
                inventory: save.inventory_data,
                hotbar: save.hotbar_data,
                kills: save.kills,
                score: save.score,
                playTime: save.play_time,
                lastSaved: save.updated_at
            }
        });

    } catch (error) {
        console.error('Load save error:', error);
        res.status(500).json({ error: 'Failed to load save' });
    }
});

// Save user's game progress
app.post('/api/save', authenticateToken, async (req, res) => {
    try {
        const {
            position,
            health,
            stamina,
            inventory,
            hotbar,
            kills,
            score,
            playTime
        } = req.body;

        // Insert or update save
        await pool.execute(`
            INSERT INTO game_saves (
                user_id, position_x, position_y, position_z,
                health, stamina, inventory_data, hotbar_data,
                kills, score, play_time
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
                position_x = VALUES(position_x),
                position_y = VALUES(position_y),
                position_z = VALUES(position_z),
                health = VALUES(health),
                stamina = VALUES(stamina),
                inventory_data = VALUES(inventory_data),
                hotbar_data = VALUES(hotbar_data),
                kills = VALUES(kills),
                score = VALUES(score),
                play_time = VALUES(play_time),
                updated_at = NOW()
        `, [
            req.user.userId,
            position.x, position.y, position.z,
            health, stamina,
            JSON.stringify(inventory),
            JSON.stringify(hotbar),
            kills, score, playTime
        ]);

        res.json({ success: true, message: 'Game saved successfully' });

    } catch (error) {
        console.error('Save game error:', error);
        res.status(500).json({ error: 'Failed to save game' });
    }
});

// Start server
const PORT = process.env.PORT || 3001;

async function startServer() {
    await testConnection();
    
    server.listen(PORT, () => {
        console.log(`🚀 Beetle3D Server running on port ${PORT}`);
        console.log(`🌐 Game URL: http://localhost:${PORT}`);
        console.log(`🎮 API URL: http://localhost:${PORT}/api`);
    });
}

startServer().catch(console.error);
