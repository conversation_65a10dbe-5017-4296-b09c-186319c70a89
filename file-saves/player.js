// === PLAYER SYSTEM ===
// Player character, controls, and combat

// Player class - simplified but with stamina and combat
class Player {
    constructor() {
        this.position = new THREE.Vector3(0, 20, 0);
        this.velocity = new THREE.Vector3(0, 0, 0);
        this.onGround = false;
        this.height = 1.8;
        this.health = 100;
        this.maxHealth = 100;
        this.stamina = 100;
        this.maxStamina = 100;
        this.isRunning = false;
        this.inventory = new Inventory();
        this.isDead = false;

        // Block placement system
        this.blockPlacementHighlight = null;
        this.placementRange = 5; // How far player can place blocks

        // Mining system
        this.miningHighlight = null;
        this.miningRange = 4; // How far player can mine blocks
        this.blockParticles = []; // For block breaking animation
        
        this.keys = {};
        this.rotation = { x: 0, y: 0 };
        this.mesh = null;
        this.isMoving = false;

        this.setupControls();
        this.createPlayerMesh();
        this.setupBlockPlacement();
    }

    createPlayerMesh() {
        // Remove old mesh if it exists
        if (this.mesh) {
            scene.remove(this.mesh);
        }

        // Create detailed player character
        this.mesh = createDetailedPlayerMesh();
        this.mesh.position.copy(this.position);
        scene.add(this.mesh);
    }
    
    setupControls() {
        document.addEventListener('keydown', (e) => {
            // Don't prevent default for inventory key when inventory is closed
            if (e.code !== 'KeyI' || this.inventory.isOpen) {
                this.keys[e.code] = true;
                if (!this.inventory.isOpen) {
                    e.preventDefault();
                }
            }
        });
        
        document.addEventListener('keyup', (e) => {
            this.keys[e.code] = false;
            if (!this.inventory.isOpen) {
                e.preventDefault();
            }
        });
        
        document.addEventListener('click', () => {
            if (!document.pointerLockElement && !this.inventory.isOpen) {
                document.body.requestPointerLock();
            }
        });
        
        document.addEventListener('mousemove', (e) => {
            if (document.pointerLockElement && !this.inventory.isOpen) {
                // Only allow yaw (left/right) and pitch (up/down) - NO roll (head tilt)
                this.rotation.y -= e.movementX * 0.002; // Yaw (left/right)
                this.rotation.x -= e.movementY * 0.002; // Pitch (up/down)

                // Clamp pitch to reasonable limits (prevent looking too far up/down)
                const maxPitch = Math.PI / 3; // 60 degrees up
                const minPitch = -Math.PI / 3; // 60 degrees down
                this.rotation.x = Math.max(minPitch, Math.min(maxPitch, this.rotation.x));

                // Ensure no roll rotation (Z-axis) - keep camera level
                // This prevents head tilting and upside-down glitches
            }
        });
        
        // Mouse click for attacking/mining
        document.addEventListener('mousedown', (e) => {
            if (document.pointerLockElement && e.button === 0 && !this.inventory.isOpen) {
                // Left-click: Check if we have a pickaxe selected for mining
                const selectedItem = this.inventory.hotbarSlots[this.inventory.selectedHotbarSlot];
                if (selectedItem && selectedItem.id === 'PICKAXE') {
                    this.mineBlock();
                } else {
                    this.attack();
                }
            }
            // Right-click for block placement
            if (document.pointerLockElement && e.button === 2 && !this.inventory.isOpen) {
                e.preventDefault();
                this.placeBlock();
            }
        });

        // Prevent context menu on right-click
        document.addEventListener('contextmenu', (e) => {
            if (document.pointerLockElement) {
                e.preventDefault();
            }
        });
    }
    
    attack() {
        // Check what weapon we're using
        const selectedItem = this.inventory.hotbarSlots[this.inventory.selectedHotbarSlot];
        let attackDamage = this.inventory.getWeaponDamage();
        let attackRange = 6;

        // Pickaxe is weaker than sword but can still attack
        if (selectedItem && selectedItem.id === 'PICKAXE') {
            attackDamage = Math.floor(attackDamage * 0.7); // 70% of normal damage
            attackRange = 4; // Shorter range
        }
        let hitSomething = false;
        
        // Find nearby orcs and attack them
        if (window.world) {
            window.world.orcs.forEach(orc => {
                if (orc.health > 0) {
                    const distance = this.position.distanceTo(orc.position);
                    if (distance <= attackRange) {
                        const wasAlive = orc.health > 0;
                        orc.takeDamage(attackDamage);
                        hitSomething = true;
                        
                        // If orc died, give reward
                        if (wasAlive && orc.health <= 0) {
                            this.inventory.addKill();
                            
                            // Random rewards
                            const rewards = ['sword', 'health_potion', 'stone_block', 'wood_block'];
                            const randomReward = rewards[Math.floor(Math.random() * rewards.length)];
                            this.inventory.addItem(randomReward.toUpperCase());
                            
                            // Show reward message
                            const rewardName = ITEMS[randomReward.toUpperCase()].name;
                            this.showMessage(`Orc defeated! +1 ${rewardName}!`, 'gold');
                        }
                    }
                }
            });
        }
        
        // Visual feedback
        if (hitSomething) {
            const flash = document.createElement('div');
            flash.style.position = 'fixed';
            flash.style.top = '0';
            flash.style.left = '0';
            flash.style.width = '100%';
            flash.style.height = '100%';
            flash.style.backgroundColor = 'rgba(255, 255, 0, 0.3)';
            flash.style.pointerEvents = 'none';
            flash.style.zIndex = '1000';
            document.body.appendChild(flash);
            
            setTimeout(() => {
                document.body.removeChild(flash);
            }, 100);
        }
    }
    
    showMessage(text, color = 'white') {
        const message = document.createElement('div');
        message.style.position = 'fixed';
        message.style.top = '30%';
        message.style.left = '50%';
        message.style.transform = 'translate(-50%, -50%)';
        message.style.color = color;
        message.style.fontSize = '24px';
        message.style.fontWeight = 'bold';
        message.style.textShadow = '2px 2px 4px rgba(0,0,0,0.8)';
        message.style.zIndex = '1000';
        message.style.pointerEvents = 'none';
        message.textContent = text;
        document.body.appendChild(message);
        
        setTimeout(() => {
            document.body.removeChild(message);
        }, 2000);
    }
    
    takeDamage(damage) {
        this.health -= damage;
        if (this.health < 0) this.health = 0;
        
        // Visual feedback - flash screen red
        const flash = document.createElement('div');
        flash.style.position = 'fixed';
        flash.style.top = '0';
        flash.style.left = '0';
        flash.style.width = '100%';
        flash.style.height = '100%';
        flash.style.backgroundColor = 'rgba(255, 0, 0, 0.3)';
        flash.style.pointerEvents = 'none';
        flash.style.zIndex = '1000';
        document.body.appendChild(flash);
        
        setTimeout(() => {
            document.body.removeChild(flash);
        }, 200);
        
        if (this.health <= 0) {
            this.die();
        }
    }
    
    heal(amount) {
        this.health += amount;
        if (this.health > this.maxHealth) {
            this.health = this.maxHealth;
        }
        
        // Visual feedback - flash screen green
        const flash = document.createElement('div');
        flash.style.position = 'fixed';
        flash.style.top = '0';
        flash.style.left = '0';
        flash.style.width = '100%';
        flash.style.height = '100%';
        flash.style.backgroundColor = 'rgba(0, 255, 0, 0.2)';
        flash.style.pointerEvents = 'none';
        flash.style.zIndex = '1000';
        document.body.appendChild(flash);
        
        setTimeout(() => {
            document.body.removeChild(flash);
        }, 200);
    }
    
    die() {
        this.isDead = true;
        
        // Create death screen
        const deathScreen = document.createElement('div');
        deathScreen.style.position = 'fixed';
        deathScreen.style.top = '0';
        deathScreen.style.left = '0';
        deathScreen.style.width = '100%';
        deathScreen.style.height = '100%';
        deathScreen.style.backgroundColor = 'black';
        deathScreen.style.zIndex = '2000';
        deathScreen.style.display = 'flex';
        deathScreen.style.flexDirection = 'column';
        deathScreen.style.justifyContent = 'center';
        deathScreen.style.alignItems = 'center';
        
        const gameOver = document.createElement('div');
        gameOver.style.color = 'red';
        gameOver.style.fontSize = '64px';
        gameOver.style.fontWeight = 'bold';
        gameOver.style.marginBottom = '30px';
        gameOver.textContent = 'GAME OVER';
        
        const stats = document.createElement('div');
        stats.style.color = 'white';
        stats.style.fontSize = '20px';
        stats.style.textAlign = 'center';
        stats.style.marginBottom = '40px';
        stats.innerHTML = `
            <div>Orcs Killed: ${this.inventory.kills}</div>
            <div>Swords Collected: ${this.inventory.swords}</div>
            <div>Final Score: ${this.inventory.score}</div>
        `;
        
        const restart = document.createElement('button');
        restart.style.padding = '15px 30px';
        restart.style.fontSize = '24px';
        restart.style.backgroundColor = '#ff4444';
        restart.style.color = 'white';
        restart.style.border = 'none';
        restart.style.borderRadius = '10px';
        restart.style.cursor = 'pointer';
        restart.textContent = 'RESTART GAME';
        restart.onclick = () => location.reload();
        
        deathScreen.appendChild(gameOver);
        deathScreen.appendChild(stats);
        deathScreen.appendChild(restart);
        document.body.appendChild(deathScreen);
        
        // Hide UI
        document.getElementById('ui').style.display = 'none';
        document.getElementById('inventory').style.display = 'none';
        document.getElementById('crosshair').style.display = 'none';
        document.getElementById('instructions').style.display = 'none';
    }

    setupBlockPlacement() {
        // Create placement highlight mesh
        this.createPlacementHighlight();

        // Create mining highlight mesh
        this.createMiningHighlight();
    }

    createPlacementHighlight() {
        // Create a wireframe cube to show where block will be placed
        const geometry = new THREE.BoxGeometry(1, 1, 1);
        const material = new THREE.MeshBasicMaterial({
            color: 0xFFD700, // Gold color
            wireframe: true,
            transparent: true,
            opacity: 0.8
        });

        this.blockPlacementHighlight = new THREE.Mesh(geometry, material);
        this.blockPlacementHighlight.visible = false;
        scene.add(this.blockPlacementHighlight);
    }

    createMiningHighlight() {
        // Create a red wireframe cube to show which block will be mined
        const geometry = new THREE.BoxGeometry(1.02, 1.02, 1.02); // Slightly larger
        const material = new THREE.MeshBasicMaterial({
            color: 0xFF0000, // Red color
            wireframe: true,
            transparent: true,
            opacity: 0.6
        });

        this.miningHighlight = new THREE.Mesh(geometry, material);
        this.miningHighlight.visible = false;
        scene.add(this.miningHighlight);
    }

    updateBlockPlacementHighlight() {
        if (!this.blockPlacementHighlight) return;

        // Get selected hotbar item
        const selectedItem = this.inventory.hotbarSlots[this.inventory.selectedHotbarSlot];

        // Only show highlight if we have a placeable block
        if (selectedItem && this.isPlaceableBlock(selectedItem.id)) {
            // Calculate position 2 blocks in front of player
            const direction = new THREE.Vector3();
            camera.getWorldDirection(direction);

            // Position 2 blocks forward from player
            const targetPosition = this.position.clone();
            targetPosition.add(direction.multiplyScalar(2));

            // Snap to grid (whole block positions)
            const blockX = Math.floor(targetPosition.x);
            const blockY = Math.floor(targetPosition.y);
            const blockZ = Math.floor(targetPosition.z);

            // Check if position is within range
            const distance = this.position.distanceTo(new THREE.Vector3(blockX + 0.5, blockY + 0.5, blockZ + 0.5));
            if (distance <= this.placementRange) {
                // Check if target position is empty and has adjacent solid block
                if (this.canPlaceBlockAt(blockX, blockY, blockZ)) {
                    this.blockPlacementHighlight.position.set(blockX + 0.5, blockY + 0.5, blockZ + 0.5);
                    this.blockPlacementHighlight.visible = true;
                } else {
                    this.blockPlacementHighlight.visible = false;
                }
            } else {
                this.blockPlacementHighlight.visible = false;
            }
        } else {
            this.blockPlacementHighlight.visible = false;
        }
    }

    canPlaceBlockAt(x, y, z) {
        if (!window.world) return false;

        // Temporarily simplified - just check if we're near the ground
        // This will allow highlighting to work while we debug
        const groundHeight = getHeightAt(x, z);
        const targetHeight = y;

        // Allow placement if we're within 3 blocks of the ground
        if (Math.abs(targetHeight - groundHeight) <= 3) {
            return true;
        }

        return false;
    }

    isPlaceableBlock(itemId) {
        // Check if item is a placeable block
        const placeableBlocks = [
            'DIRT_BLOCK',
            'WOOD_BLOCK',
            'STONE_BLOCK',
            'GRASS_BLOCK'
        ];
        return placeableBlocks.includes(itemId);
    }

    placeBlock() {
        // Get selected hotbar item
        const selectedItem = this.inventory.hotbarSlots[this.inventory.selectedHotbarSlot];

        if (!selectedItem || !this.isPlaceableBlock(selectedItem.id)) {
            console.log('No placeable block selected');
            return;
        }

        if (!this.blockPlacementHighlight.visible) {
            console.log('Cannot place block here');
            return;
        }

        // Get placement position
        const placePos = this.blockPlacementHighlight.position.clone();

        // Convert to world coordinates
        const worldX = Math.floor(placePos.x);
        const worldY = Math.floor(placePos.y);
        const worldZ = Math.floor(placePos.z);

        // Place block in world
        if (window.world) {
            const blockType = this.getBlockTypeFromItem(selectedItem.id);

            // Find which chunk contains this position
            const chunkX = Math.floor(worldX / 16); // CHUNK_SIZE = 16
            const chunkZ = Math.floor(worldZ / 16);
            const localX = worldX - (chunkX * 16);
            const localZ = worldZ - (chunkZ * 16);

            // Get the chunk
            const chunkKey = `${chunkX},${chunkZ}`;
            const chunk = window.world.chunks.get(chunkKey);

            if (chunk) {
                // Set block in chunk
                chunk.setBlock(localX, worldY, localZ, blockType);

                // IMPORTANT: Update the global height map for collision
                if (!window.world.heightMap) {
                    window.world.heightMap = new Map();
                }

                // Always update height map for placed blocks (don't check if higher)
                const heightKey = `${Math.floor(worldX)},${Math.floor(worldZ)}`;
                const currentHeight = window.world.heightMap.get(heightKey) || getHeightAt(worldX, worldZ);

                // Update if this block is higher than current height
                if (worldY >= currentHeight) {
                    window.world.heightMap.set(heightKey, worldY);
                }

                // Regenerate chunk mesh to show the new block
                chunk.createMesh();

                // Reduce item count
                selectedItem.count--;
                if (selectedItem.count <= 0) {
                    this.inventory.hotbarSlots[this.inventory.selectedHotbarSlot] = null;
                }

                // Update displays
                this.inventory.updateHotbarDisplay();

                console.log(`Placed ${selectedItem.id} at (${worldX}, ${worldY}, ${worldZ}) in chunk (${chunkX}, ${chunkZ})`);
                this.showMessage(`Placed ${selectedItem.id}`, 'green');
            } else {
                console.log(`No chunk found at (${chunkX}, ${chunkZ})`);
            }
        }
    }

    getBlockTypeFromItem(itemId) {
        // Convert item ID to block type using BLOCKS constants
        const blockMap = {
            'DIRT_BLOCK': BLOCKS.DIRT,
            'WOOD_BLOCK': BLOCKS.WOOD,
            'STONE_BLOCK': BLOCKS.STONE,
            'GRASS_BLOCK': BLOCKS.GRASS
        };
        return blockMap[itemId] || BLOCKS.DIRT;
    }

    updateMiningHighlight() {
        if (!this.miningHighlight) return;

        // Get selected hotbar item
        const selectedItem = this.inventory.hotbarSlots[this.inventory.selectedHotbarSlot];

        // Only show mining highlight if we have a pickaxe
        if (selectedItem && selectedItem.id === 'PICKAXE') {
            // Calculate position 2 blocks in front of player
            const direction = new THREE.Vector3();
            camera.getWorldDirection(direction);

            // Position 2 blocks forward from player
            const targetPosition = this.position.clone();
            targetPosition.add(direction.multiplyScalar(2));

            // Snap to grid (whole block positions)
            const blockX = Math.floor(targetPosition.x);
            const blockY = Math.floor(targetPosition.y);
            const blockZ = Math.floor(targetPosition.z);

            // Check if position is within range and has a block to mine
            const distance = this.position.distanceTo(new THREE.Vector3(blockX + 0.5, blockY + 0.5, blockZ + 0.5));
            if (distance <= this.miningRange && this.canMineBlockAt(blockX, blockY, blockZ)) {
                this.miningHighlight.position.set(blockX + 0.5, blockY + 0.5, blockZ + 0.5);
                this.miningHighlight.visible = true;
            } else {
                this.miningHighlight.visible = false;
            }
        } else {
            this.miningHighlight.visible = false;
        }
    }

    canMineBlockAt(x, y, z) {
        if (!window.world) return false;

        // Check if there's a block to mine at this position
        const chunkX = Math.floor(x / 16);
        const chunkZ = Math.floor(z / 16);
        const localX = x - (chunkX * 16);
        const localZ = z - (chunkZ * 16);

        const chunkKey = `${chunkX},${chunkZ}`;
        const chunk = window.world.chunks.get(chunkKey);

        if (chunk) {
            const block = chunk.getBlock(localX, y, localZ);
            return block && block !== BLOCKS.AIR;
        }

        return false;
    }

    mineBlock() {
        // Get selected hotbar item
        const selectedItem = this.inventory.hotbarSlots[this.inventory.selectedHotbarSlot];

        if (!selectedItem || selectedItem.id !== 'PICKAXE') {
            console.log('No pickaxe selected');
            return;
        }

        if (!this.miningHighlight.visible) {
            console.log('No block to mine');
            return;
        }

        // Get mining position
        const minePos = this.miningHighlight.position.clone();
        const worldX = Math.floor(minePos.x);
        const worldY = Math.floor(minePos.y);
        const worldZ = Math.floor(minePos.z);

        // Mine block from world
        if (window.world) {
            const chunkX = Math.floor(worldX / 16);
            const chunkZ = Math.floor(worldZ / 16);
            const localX = worldX - (chunkX * 16);
            const localZ = worldZ - (chunkZ * 16);

            const chunkKey = `${chunkX},${chunkZ}`;
            const chunk = window.world.chunks.get(chunkKey);

            if (chunk) {
                // Get the block type before removing it
                const blockType = chunk.getBlock(localX, worldY, localZ);

                if (blockType && blockType !== BLOCKS.AIR) {
                    // Create block breaking particles
                    this.createBlockParticles(worldX + 0.5, worldY + 0.5, worldZ + 0.5, blockType);

                    // Remove block from chunk
                    chunk.setBlock(localX, worldY, localZ, BLOCKS.AIR);

                    // Update height map
                    if (window.world.heightMap) {
                        const heightKey = `${worldX},${worldZ}`;
                        const currentHeight = window.world.heightMap.get(heightKey);
                        if (currentHeight === worldY) {
                            // Find new height by checking blocks below
                            let newHeight = worldY - 1;
                            while (newHeight >= 0) {
                                const belowBlock = chunk.getBlock(localX, newHeight, localZ);
                                if (belowBlock && belowBlock !== BLOCKS.AIR) {
                                    break;
                                }
                                newHeight--;
                            }
                            window.world.heightMap.set(heightKey, newHeight);
                        }
                    }

                    // Regenerate chunk mesh
                    chunk.createMesh();

                    // Add block to inventory
                    const itemId = this.getItemFromBlockType(blockType);
                    this.inventory.addItem(itemId, 1);

                    console.log(`Mined ${itemId} at (${worldX}, ${worldY}, ${worldZ})`);
                    this.showMessage(`Mined ${itemId}`, 'orange');
                }
            }
        }
    }

    getItemFromBlockType(blockType) {
        // Convert block type to item ID
        const itemMap = {
            [BLOCKS.DIRT]: 'DIRT_BLOCK',
            [BLOCKS.WOOD]: 'WOOD_BLOCK',
            [BLOCKS.STONE]: 'STONE_BLOCK',
            [BLOCKS.GRASS]: 'DIRT_BLOCK', // Grass becomes dirt when mined
            [BLOCKS.LEAVES]: 'WOOD_BLOCK' // Leaves become wood (simplified)
        };
        return itemMap[blockType] || 'DIRT_BLOCK';
    }

    createBlockParticles(x, y, z, blockType) {
        // Create small cube particles that fall when block is broken
        const particleCount = 8;
        const colors = {
            [BLOCKS.DIRT]: 0x8B4513,
            [BLOCKS.WOOD]: 0x8B4513,
            [BLOCKS.STONE]: 0x808080,
            [BLOCKS.GRASS]: 0x228B22,
            [BLOCKS.LEAVES]: 0x228B22
        };

        const color = colors[blockType] || 0x8B4513;

        for (let i = 0; i < particleCount; i++) {
            const geometry = new THREE.BoxGeometry(0.1, 0.1, 0.1);
            const material = new THREE.MeshBasicMaterial({ color: color });
            const particle = new THREE.Mesh(geometry, material);

            // Random position within the block
            particle.position.set(
                x + (Math.random() - 0.5) * 0.8,
                y + (Math.random() - 0.5) * 0.8,
                z + (Math.random() - 0.5) * 0.8
            );

            // Random velocity
            particle.velocity = new THREE.Vector3(
                (Math.random() - 0.5) * 4,
                Math.random() * 3 + 1,
                (Math.random() - 0.5) * 4
            );

            particle.life = 2.0; // 2 seconds
            particle.maxLife = 2.0;

            scene.add(particle);
            this.blockParticles.push(particle);
        }
    }

    updateBlockParticles(deltaTime) {
        for (let i = this.blockParticles.length - 1; i >= 0; i--) {
            const particle = this.blockParticles[i];

            // Update position
            particle.position.add(particle.velocity.clone().multiplyScalar(deltaTime));

            // Apply gravity
            particle.velocity.y -= 9.8 * deltaTime;

            // Update life
            particle.life -= deltaTime;

            // Fade out
            const alpha = particle.life / particle.maxLife;
            particle.material.opacity = alpha;
            particle.material.transparent = true;

            // Remove dead particles
            if (particle.life <= 0) {
                scene.remove(particle);
                this.blockParticles.splice(i, 1);
            }
        }
    }

    update(deltaTime) {
        if (this.isDead) return;

        // Don't update movement if inventory is open
        if (this.inventory.isOpen) return;

        // Apply gravity
        this.velocity.y -= 25 * deltaTime;

        // Check if running
        const wantsToRun = this.keys['KeyR'] &&
            (this.keys['KeyW'] || this.keys['KeyS'] || this.keys['KeyA'] || this.keys['KeyD']);
        this.isRunning = wantsToRun && this.stamina > 5; // Lower stamina requirement

        // Update stamina with smoother calculations
        if (this.isRunning) {
            this.stamina -= 15 * deltaTime; // Consistent stamina drain
            if (this.stamina <= 0) {
                this.stamina = 0;
                this.isRunning = false;
            }
        } else {
            this.stamina += 30 * deltaTime; // Consistent stamina recovery
            if (this.stamina >= this.maxStamina) {
                this.stamina = this.maxStamina;
            }
        }

        // Clamp stamina to valid range to prevent glitches
        this.stamina = Math.max(0, Math.min(this.maxStamina, this.stamina));

        // Movement - Camera relative (W=forward, S=backward, A=left, D=right)
        const moveVector = new THREE.Vector3();

        // Get camera direction vectors
        const forward = new THREE.Vector3();
        const right = new THREE.Vector3();

        // Get forward direction from camera (where camera is looking)
        camera.getWorldDirection(forward);
        forward.y = 0; // Remove vertical component for ground movement
        forward.normalize();

        // Get right direction (perpendicular to forward)
        right.crossVectors(forward, new THREE.Vector3(0, 1, 0));
        right.normalize();

        // Apply movement based on camera directions
        if (this.keys['KeyW']) moveVector.add(forward);  // Forward
        if (this.keys['KeyS']) moveVector.sub(forward);  // Backward
        if (this.keys['KeyA']) moveVector.sub(right);    // Left
        if (this.keys['KeyD']) moveVector.add(right);    // Right

        // Check if player is moving for animation
        this.isMoving = moveVector.length() > 0;

        if (moveVector.length() > 0) {
            moveVector.normalize();
            const speed = this.isRunning ? 90 : 60;
            moveVector.multiplyScalar(speed * deltaTime);

            this.velocity.x = moveVector.x;
            this.velocity.z = moveVector.z;
        } else {
            this.velocity.x *= 0.95;
            this.velocity.z *= 0.95;
        }

        // Jump
        if (this.keys['Space'] && this.onGround) {
            this.velocity.y = 12; // Higher jump for more dynamic movement
            this.onGround = false;
        }

        // Store old position for collision rollback
        const oldPosition = this.position.clone();
        const movement = this.velocity.clone().multiplyScalar(deltaTime);

        // Try horizontal movement first
        this.position.x += movement.x;
        this.position.z += movement.z;

        // Check building collision and rollback if needed
        if (window.world && window.world.checkBuildingCollision && window.world.checkBuildingCollision(this.position)) {
            this.position.x = oldPosition.x;
            this.position.z = oldPosition.z;
        }

        // Then try vertical movement
        this.position.y += movement.y;

        // Ground collision (back to simple and working)
        const groundHeight = getHeightAt(Math.floor(this.position.x), Math.floor(this.position.z)) + this.height;

        if (this.position.y <= groundHeight) {
            this.position.y = groundHeight;
            this.velocity.y = 0;
            this.onGround = true;
        } else {
            this.onGround = false;
        }

        // Update player mesh position and animation
        if (this.mesh) {
            this.mesh.position.copy(this.position);
            this.mesh.position.y -= this.height; // Adjust for character height

            // Animate character
            const time = Date.now() * 0.001;
            animateCharacter(this.mesh, time, this.isMoving);

            // Make character face movement direction when moving
            if (this.isMoving) {
                const direction = new THREE.Vector3(this.velocity.x, 0, this.velocity.z);
                if (direction.length() > 0) {
                    direction.normalize();
                    const angle = Math.atan2(direction.x, direction.z);
                    this.mesh.rotation.y = angle;
                }
            }
        }

        // Update camera
        camera.position.copy(this.position);
        camera.rotation.x = this.rotation.x; // Pitch (up/down)
        camera.rotation.y = this.rotation.y; // Yaw (left/right)
        camera.rotation.z = 0; // Force Z rotation to 0 (no head tilt/roll)

        // Update block placement highlight
        this.updateBlockPlacementHighlight();

        // Update mining highlight
        this.updateMiningHighlight();

        // Update block particles
        this.updateBlockParticles(deltaTime);
    }
}
