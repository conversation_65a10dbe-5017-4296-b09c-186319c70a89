<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minecraft Adventure - Modular</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #87CEEB;
            overflow: hidden;
            font-family: 'Courier New', monospace;
        }
        
        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #ui {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            z-index: 100;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }
        
        #crosshair {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            pointer-events: none;
            z-index: 100;
        }
        
        #crosshair::before,
        #crosshair::after {
            content: '';
            position: absolute;
            background: white;
            box-shadow: 0 0 2px rgba(0,0,0,0.8);
        }
        
        #crosshair::before {
            width: 2px;
            height: 20px;
            left: 9px;
            top: 0;
        }
        
        #crosshair::after {
            width: 20px;
            height: 2px;
            left: 0;
            top: 9px;
        }
        
        #instructions {
            position: absolute;
            bottom: 10px;
            left: 10px;
            color: white;
            font-size: 12px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }

        #inventory {
            position: absolute;
            top: 10px;
            right: 10px;
            color: white;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            z-index: 100;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }

        #inventoryPanel {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #8B4513;
            border: 4px solid #A0522D;
            border-style: outset;
            padding: 16px;
            z-index: 300;
            display: none;
            color: white;
            font-family: 'Courier New', monospace;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
            box-shadow:
                inset 2px 2px 0px #D2B48C,
                inset -2px -2px 0px #654321,
                4px 4px 8px rgba(0,0,0,0.5);
        }

        #inventoryContent {
            display: flex;
            gap: 20px;
        }

        #inventoryGrid {
            display: grid;
            grid-template-columns: repeat(5, 64px);
            grid-template-rows: repeat(4, 64px);
            gap: 2px;
            background: #654321;
            padding: 8px;
            border: 2px solid #A0522D;
            border-style: inset;
        }

        .inventory-slot {
            width: 64px;
            height: 64px;
            border: 2px solid #A0522D;
            border-style: inset;
            background: #8B4513;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            position: relative;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
        }

        .inventory-slot:hover {
            background: #A0522D;
            border-color: #D2B48C;
        }

        .inventory-slot.selected {
            border: 2px solid #ffff00;
            border-style: outset;
            background: #B8860B;
            box-shadow:
                inset 1px 1px 0px #ffff88,
                inset -1px -1px 0px #888800;
        }

        .inventory-slot.has-item {
            background: #A0522D;
            border-style: outset;
        }

        .item-icon {
            font-size: 32px;
            text-shadow: 2px 2px 0px rgba(0,0,0,1);
            image-rendering: pixelated;
            filter: contrast(1.2) saturate(1.1);
        }

        .item-count {
            position: absolute;
            bottom: 4px;
            right: 6px;
            font-size: 12px;
            color: white;
            text-shadow: 1px 1px 0px rgba(0,0,0,1);
            font-weight: bold;
            background: rgba(0,0,0,0.7);
            padding: 1px 3px;
            border: 1px solid #666;
        }

        #inventoryInfo {
            text-align: center;
            margin-bottom: 12px;
            font-size: 16px;
            font-weight: bold;
            text-shadow: 2px 2px 0px rgba(0,0,0,1);
        }

        #selectedItemInfo {
            min-height: 48px;
            padding: 12px;
            background: #654321;
            border: 2px solid #A0522D;
            border-style: inset;
            margin-bottom: 12px;
            font-size: 14px;
            box-shadow: inset 1px 1px 2px rgba(0,0,0,0.5);
        }

        #characterPreview {
            width: 120px;
            text-align: center;
        }

        #characterPreviewTitle {
            color: #F5DEB3;
            font-size: 14px;
            margin-bottom: 8px;
            font-weight: bold;
        }

        #characterPreviewCanvas {
            border: 2px solid #A0522D;
            border-style: inset;
            background: #654321;
        }

        #inventoryHotbar {
            display: flex;
            gap: 2px;
            margin-top: 16px;
            padding: 8px;
            background: #654321;
            border: 2px solid #A0522D;
            border-style: inset;
            justify-content: center;
        }

        .hotbar-slot {
            width: 48px;
            height: 48px;
            border: 2px solid #A0522D;
            border-style: inset;
            background: #8B4513;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            position: relative;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
        }

        .hotbar-slot:hover {
            background: #A0522D;
            border-color: #D2B48C;
        }

        .hotbar-slot.selected {
            border: 2px solid #ffff00;
            border-style: outset;
            background: #B8860B;
            box-shadow:
                inset 1px 1px 0px #ffff88,
                inset -1px -1px 0px #888800;
        }

        .hotbar-slot.has-item {
            background: #A0522D;
            border-style: outset;
        }

        .hotbar-slot .item-icon {
            font-size: 24px;
        }

        .hotbar-slot .item-count {
            font-size: 10px;
            bottom: 2px;
            right: 4px;
        }

        .inventory-controls {
            text-align: center;
            font-size: 12px;
            color: #F5DEB3;
            text-shadow: 1px 1px 0px rgba(0,0,0,1);
            background: #8B4513;
            padding: 8px;
            border: 1px solid #A0522D;
            border-style: inset;
            margin-top: 12px;
        }

        /* Hotbar (always visible at bottom) */
        #hotbar {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 2px;
            padding: 8px;
            background: rgba(139, 69, 19, 0.9);
            border: 2px solid #A0522D;
            border-style: outset;
            border-radius: 4px;
            z-index: 200;
            box-shadow: 0 4px 8px rgba(0,0,0,0.5);
        }

        /* Minecraft-style pixelated item icons */
        .item-icon {
            width: 32px;
            height: 32px;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Sword icon - pixelated blade */
        .item-icon.sword {
            background: linear-gradient(
                to bottom,
                #C0C0C0 0%, #C0C0C0 20%,
                #A9A9A9 20%, #A9A9A9 80%,
                #8B4513 80%, #8B4513 100%
            );
            border: 1px solid #696969;
        }

        /* Grass block icon */
        .item-icon.grass_block {
            background: linear-gradient(
                to bottom,
                #228B22 0%, #228B22 25%,
                #8B4513 25%, #8B4513 100%
            );
            border: 1px solid #006400;
        }

        /* Dirt block icon */
        .item-icon.dirt_block {
            background: #8B4513;
            border: 1px solid #654321;
            position: relative;
        }
        .item-icon.dirt_block::before {
            content: '';
            position: absolute;
            width: 4px;
            height: 4px;
            background: #A0522D;
            top: 8px;
            left: 8px;
            box-shadow:
                8px 0px 0px #A0522D,
                16px 0px 0px #A0522D,
                0px 8px 0px #A0522D,
                8px 8px 0px #654321,
                16px 8px 0px #A0522D,
                0px 16px 0px #654321,
                8px 16px 0px #A0522D,
                16px 16px 0px #654321;
        }

        /* Stone block icon */
        .item-icon.stone_block {
            background: #808080;
            border: 1px solid #696969;
            position: relative;
        }
        .item-icon.stone_block::before {
            content: '';
            position: absolute;
            width: 4px;
            height: 4px;
            background: #A9A9A9;
            top: 6px;
            left: 6px;
            box-shadow:
                8px 0px 0px #696969,
                16px 0px 0px #A9A9A9,
                0px 8px 0px #696969,
                8px 8px 0px #A9A9A9,
                16px 8px 0px #696969,
                0px 16px 0px #A9A9A9,
                8px 16px 0px #696969,
                16px 16px 0px #A9A9A9;
        }

        /* Wood block icon */
        .item-icon.wood_block {
            background: #DEB887;
            border: 1px solid #8B7355;
            position: relative;
        }
        .item-icon.wood_block::before {
            content: '';
            position: absolute;
            width: 30px;
            height: 2px;
            background: #8B7355;
            top: 8px;
            left: 1px;
            box-shadow:
                0px 6px 0px #8B7355,
                0px 12px 0px #8B7355,
                0px 18px 0px #8B7355;
        }

        /* Health potion icon */
        .item-icon.health_potion {
            background: linear-gradient(
                to bottom,
                #FF69B4 0%, #FF69B4 20%,
                #FF1493 20%, #FF1493 80%,
                #8B4513 80%, #8B4513 100%
            );
            border: 1px solid #DC143C;
            border-radius: 4px;
        }

        /* Pickaxe icon */
        .item-icon.pickaxe {
            background: linear-gradient(
                45deg,
                #696969 0%, #696969 60%,
                #8B4513 60%, #8B4513 100%
            );
            border: 1px solid #2F4F4F;
        }

        /* Fist icon */
        .item-icon.fist {
            background: #DDBEA9;
            border: 1px solid #CB997E;
            border-radius: 2px;
        }

        /* Bow icon */
        .item-icon.bow {
            background: linear-gradient(
                to right,
                #8B4513 0%, #8B4513 20%,
                transparent 20%, transparent 80%,
                #8B4513 80%, #8B4513 100%
            );
            border: 1px solid #654321;
            position: relative;
        }
        .item-icon.bow::before {
            content: '';
            position: absolute;
            width: 2px;
            height: 24px;
            background: #D2B48C;
            left: 15px;
            top: 4px;
            border-radius: 50%;
        }

        /* Rendered 3D icons */
        .item-icon.rendered-icon {
            width: 56px !important;
            height: 56px !important;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
            border: none;
            background: none;
            margin: 4px;
        }

        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 24px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            z-index: 200;
        }

        /* Character Creation Modal */
        #characterCreationModal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Courier New', monospace;
        }

        .character-creation-panel {
            background: linear-gradient(135deg, #8B4513, #A0522D);
            border: 4px solid #D2B48C;
            border-radius: 10px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            color: white;
            text-align: center;
            box-shadow: 0 0 20px rgba(0,0,0,0.8);
        }

        .character-creation-panel h1 {
            color: #FFD700;
            font-size: 28px;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }

        .character-creation-panel h2 {
            color: #F5DEB3;
            font-size: 20px;
            margin: 20px 0 10px 0;
        }

        .character-creation-panel input[type="text"] {
            width: 80%;
            padding: 10px;
            font-size: 16px;
            border: 2px solid #654321;
            border-radius: 5px;
            background: #F5DEB3;
            color: #654321;
            font-family: 'Courier New', monospace;
        }

        .character-creation-panel .class-selection,
        .character-creation-panel .gender-selection {
            display: flex;
            justify-content: space-around;
            margin: 15px 0;
        }

        .character-creation-panel .class-option,
        .character-creation-panel .gender-option {
            background: #654321;
            border: 2px solid #8B4513;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }

        .character-creation-panel .class-option:hover,
        .character-creation-panel .gender-option:hover {
            background: #8B4513;
            border-color: #D2B48C;
            transform: scale(1.05);
        }

        .character-creation-panel .class-option.selected,
        .character-creation-panel .gender-option.selected {
            background: #B8860B;
            border-color: #FFD700;
            box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }

        .character-creation-panel .start-button {
            background: #228B22;
            border: 3px solid #32CD32;
            color: white;
            padding: 15px 30px;
            font-size: 18px;
            border-radius: 8px;
            cursor: pointer;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            transition: all 0.3s;
        }

        .character-creation-panel .start-button:hover {
            background: #32CD32;
            transform: scale(1.05);
        }

        .character-creation-panel .start-button:disabled {
            background: #666;
            border-color: #999;
            cursor: not-allowed;
            transform: none;
        }

        /* Story UI Panel */
        #storyPanel {
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            background: rgba(139, 69, 19, 0.95);
            border: 3px solid #D2B48C;
            border-radius: 8px;
            padding: 15px;
            color: white;
            font-family: 'Courier New', monospace;
            z-index: 150;
        }

        #storyPanel h3 {
            color: #FFD700;
            margin: 0 0 10px 0;
            text-align: center;
            font-size: 18px;
        }

        #storyPanel .character-info {
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
        }

        #storyPanel .character-info div {
            margin: 5px 0;
            font-size: 14px;
        }

        .story-toggle {
            position: fixed;
            top: 10px;
            right: 320px;
            background: #8B4513;
            border: 2px solid #D2B48C;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-family: 'Courier New', monospace;
            z-index: 160;
        }

        .story-toggle:hover {
            background: #A0522D;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <div id="loading">Loading Adventure World...</div>
        
        <!-- Character Creation Modal -->
        <div id="characterCreationModal" style="display: none;">
            <div class="character-creation-panel">
                <h1>🐛 BEETLE ADVENTURE 🐛</h1>
                <h2>Create Your Character</h2>

                <div style="margin: 20px 0;">
                    <label for="characterName" style="display: block; margin-bottom: 10px;">Character Name:</label>
                    <input type="text" id="characterName" placeholder="Enter your name..." maxlength="20">
                </div>

                <h2>Choose Your Class</h2>
                <div class="class-selection">
                    <div class="class-option" data-class="Warrior">
                        <div>⚔️ Warrior</div>
                        <small>High Strength & Defense</small>
                    </div>
                    <div class="class-option" data-class="Mage">
                        <div>🔮 Mage</div>
                        <small>High Magic Power</small>
                    </div>
                    <div class="class-option" data-class="Rogue">
                        <div>🗡️ Rogue</div>
                        <small>Balanced Stats</small>
                    </div>
                </div>

                <h2>Choose Your Gender</h2>
                <div class="gender-selection">
                    <div class="gender-option" data-gender="male">
                        <div>👨 Male</div>
                    </div>
                    <div class="gender-option" data-gender="female">
                        <div>👩 Female</div>
                    </div>
                </div>

                <button class="start-button" id="startAdventure" disabled>Start Adventure!</button>
            </div>
        </div>

        <!-- Story Panel Toggle Button -->
        <button class="story-toggle" id="storyToggle" style="display: none;">📖 Story</button>

        <!-- Story Panel -->
        <div id="storyPanel" style="display: none;">
            <h3>Character Info</h3>
            <div class="character-info">
                <div id="charName">Name: -</div>
                <div id="charClass">Class: -</div>
                <div id="charLevel">Level: 1</div>
                <div id="charExp">Experience: 0/100</div>
                <div id="charGold">Gold: 150</div>
            </div>
            <div class="character-info">
                <div id="charStrength">⚔️ Strength: -</div>
                <div id="charDefense">🛡️ Defense: -</div>
                <div id="charMagic">🔮 Magic: -</div>
            </div>
        </div>
        
        <div id="ui" style="display: none;">
            <div>❤️ Health: <span id="health">100</span>/100</div>
            <div style="display: flex; align-items: center; margin: 5px 0;">
                <span style="margin-right: 10px;">⚡ Stamina:</span>
                <div id="staminaBarContainer" style="width: 100px; height: 12px; background: rgba(0,0,0,0.5); border: 1px solid #666; border-radius: 2px; position: relative;">
                    <div id="staminaBar" style="width: 100%; height: 100%; background: linear-gradient(to right, #00ff00, #ffff00, #ff0000); border-radius: 1px; transition: width 0.1s ease;"></div>
                </div>
                <span id="staminaText" style="margin-left: 8px; font-size: 12px;">100</span>
            </div>
            <div>🏔️ Biome: <span id="biome">Plains</span></div>
            <div>🧭 Town: <span id="townDirection">→ 100m East</span></div>
            <div>⏱️ FPS: <span id="fps">0</span></div>
            <div>🗡️ Orcs: <span id="orcs">0</span></div>
            <div>⏰ Time: <span id="time">12:00</span></div>
        </div>

        <div id="inventory" style="display: none;">
            <div>🗡️ Weapon: <span id="currentWeapon">Fist</span></div>
            <div>⚔️ Swords: <span id="swordCount">0</span></div>
            <div>💀 Kills: <span id="killCount">0</span></div>
            <div>🏆 Score: <span id="score">0</span></div>
        </div>
        
        <div id="crosshair" style="display: none;"></div>
        
        <div id="instructions" style="display: none;">
            WASD: Move | R: Run | Mouse: Look | Space: Jump | Left Click: Attack | I: Inventory | Ctrl+S: Save | Ctrl+L: Load | Mouse to lock
        </div>

        <!-- Inventory Panel -->
        <div id="inventoryPanel">
            <div id="inventoryInfo">
                <h3>📦 Inventory</h3>
            </div>
            <div id="selectedItemInfo">
                <div id="selectedItemName">Select an item to see details</div>
                <div id="selectedItemDesc"></div>
            </div>
            <div id="inventoryContent">
                <div id="inventoryGrid">
                    <!-- Grid slots will be generated by JavaScript -->
                </div>
                <div id="characterPreview">
                    <div id="characterPreviewTitle">Your Character</div>
                    <canvas id="characterPreviewCanvas" width="120" height="150"></canvas>
                </div>
            </div>
            <div id="inventoryHotbar">
                <!-- Hotbar slots in inventory will be generated by JavaScript -->
            </div>
            <div class="inventory-controls">
                Drag & Drop: Move Items | I or ESC: Close | 1-6: Select Hotbar
            </div>
        </div>

        <!-- Hotbar (always visible) -->
        <div id="hotbar">
            <!-- Hotbar slots will be generated by JavaScript -->
        </div>
    </div>

    <!-- Three.js Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    
    <!-- Game Modules -->
    <script src="js/auth.js"></script>
    <script src="js/saveload.js"></script>
    <script src="js/noise.js"></script>
    <script src="js/items.js"></script>
    <script src="js/characters.js"></script>
    <script src="js/inventory.js"></script>
    <script src="js/world.js"></script>
    <script src="js/enemies.js"></script>
    <script src="js/npcs.js"></script>
    <script src="js/player.js"></script>
    <script src="js/ui.js"></script>
    <script src="js/story.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
