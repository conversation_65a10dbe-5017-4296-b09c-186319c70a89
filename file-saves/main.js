// === MAIN GAME LOOP ===
// Game initialization and main loop

// Global variables
let scene, camera, renderer;
let world, player;
let directionalLight, ambientLight;
let frameCount = 0;
let lastTime = 0;

// Initialize the game
async function init() {
    console.log('Initializing Minecraft Adventure...');

    // Don't initialize if not logged in
    if (!authSystem || !authSystem.isLoggedIn) {
        console.log('Waiting for user login...');
        return;
    }

    // Initialize character creation system
    if (typeof initializeCharacterCreation === 'function') {
        initializeCharacterCreation();
    }

    // If character creation is needed, wait for it to complete
    if (!gameState.isCharacterCreated) {
        console.log('Waiting for character creation...');
        return;
    }
    
    // Create scene
    scene = new THREE.Scene();
    scene.background = new THREE.Color(0x87CEEB); // Sky blue
    
    // Create camera
    camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    
    // Create renderer
    renderer = new THREE.WebGLRenderer({ antialias: false });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    document.getElementById('gameContainer').appendChild(renderer.domElement);
    
    // Create lighting
    ambientLight = new THREE.AmbientLight(0x404040, 0.4);
    scene.add(ambientLight);
    
    directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(50, 50, 50);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    directionalLight.shadow.camera.near = 0.5;
    directionalLight.shadow.camera.far = 500;
    directionalLight.shadow.camera.left = -50;
    directionalLight.shadow.camera.right = 50;
    directionalLight.shadow.camera.top = 50;
    directionalLight.shadow.camera.bottom = -50;
    scene.add(directionalLight);
    
    // Make lights globally accessible
    window.directionalLight = directionalLight;
    window.ambientLight = ambientLight;
    
    // Create world
    world = new World();
    window.world = world;
    
    // Create player
    player = new Player();
    window.player = player;
    
    // Initialize item icon renderer
    itemIconRenderer = new ItemIconRenderer();

    // Initialize save/load system
    if (!saveLoadSystem) {
        saveLoadSystem = new SaveLoadSystem();
    }

    // Hide loading, show UI
    document.getElementById('loading').style.display = 'none';
    document.getElementById('ui').style.display = 'block';
    document.getElementById('inventory').style.display = 'block';
    document.getElementById('crosshair').style.display = 'block';
    document.getElementById('instructions').style.display = 'block';
    
    // Load saved game data or initialize with starter items
    if (window.savedGameData) {
        console.log('Loading saved game data...');
        // Load saved position
        if (savedGameData.position) {
            player.position.set(savedGameData.position.x, savedGameData.position.y, savedGameData.position.z);
        }
        // Load saved health and stamina
        if (savedGameData.health !== undefined) player.health = savedGameData.health;
        if (savedGameData.stamina !== undefined) player.stamina = savedGameData.stamina;
        // Load saved inventory and hotbar
        if (savedGameData.inventory) {
            // New format (separate inventory and hotbar)
            if (Array.isArray(savedGameData.inventory)) {
                player.inventory.slots = savedGameData.inventory;
            } else if (savedGameData.inventory.slots) {
                // Old format compatibility
                player.inventory.slots = savedGameData.inventory.slots;
            }
        }

        if (savedGameData.hotbar) {
            player.inventory.hotbarSlots = savedGameData.hotbar;
        }

        // Load stats
        if (savedGameData.kills !== undefined) player.inventory.kills = savedGameData.kills;
        if (savedGameData.score !== undefined) player.inventory.score = savedGameData.score;
    } else {
        console.log('New game - adding starter items...');
        // Initialize inventory UI and add starter items for new players
        player.inventory.addItem('SWORD', 1);
        player.inventory.addItem('HEALTH_POTION', 3);
        player.inventory.addItem('DIRT_BLOCK', 20);
        player.inventory.addItem('WOOD_BLOCK', 15);
        player.inventory.addItem('STONE_BLOCK', 10);
        player.inventory.addItem('PICKAXE', 1);
    }

    player.inventory.updateUI();
    
    console.log('Game initialized successfully!');
    
    // Start game loop
    animate();
}

// Main game loop
function animate(currentTime = 0) {
    requestAnimationFrame(animate);
    
    const deltaTime = Math.min((currentTime - lastTime) / 1000, 0.1); // Cap at 100ms
    lastTime = currentTime;
    
    if (deltaTime > 0) {
        // Update player
        player.update(deltaTime);
        
        // Update world chunks around player
        world.updateChunks(player.position.x, player.position.z);
        
        // Update enemies
        world.updateEnemies(deltaTime, player.position);

        // Update NPCs
        world.updateNPCs(deltaTime, player.position);
        
        // Update UI every 30 frames for smooth stamina display
        frameCount++;
        if (frameCount % 30 === 0) {
            updateUI(player, world, deltaTime);
        }

        // Auto-save every 30 seconds (1800 frames at 60fps)
        if (frameCount % 1800 === 0 && authSystem && authSystem.isLoggedIn) {
            autoSaveGame();
        }

        // Update character preview if inventory is open
        if (player.inventory.isOpen && characterPreviewRenderer) {
            characterPreviewRenderer.update();
        }

        // Render the scene
        renderer.render(scene, camera);
    }
}

// Handle window resize
function onWindowResize() {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
}

window.addEventListener('resize', onWindowResize);

// Handle pointer lock
document.addEventListener('pointerlockchange', () => {
    if (document.pointerLockElement) {
        document.getElementById('instructions').style.display = 'none';
    } else {
        document.getElementById('instructions').style.display = 'block';
    }
});

// Auto-save function
function autoSaveGame() {
    if (!player || !authSystem || !authSystem.isLoggedIn) return;

    const gameData = {
        position: {
            x: player.position.x,
            y: player.position.y,
            z: player.position.z
        },
        health: player.health,
        stamina: player.stamina,
        inventory: player.inventory.slots,
        hotbar: player.inventory.hotbarSlots,
        kills: player.inventory.kills,
        score: player.inventory.score,
        playTime: Date.now() - new Date(authSystem.currentUser.loginTime).getTime()
    };

    authSystem.saveGameData(gameData);
}

// Initialize authentication system when page loads
window.addEventListener('load', () => {
    console.log('Page loaded - initializing auth system');

    // Hide game container and show loading screen
    document.getElementById('gameContainer').style.display = 'none';
    document.getElementById('loading').style.display = 'block';

    // Make init function available globally
    window.init = init;

    // Initialize auth system
    try {
        authSystem = new AuthSystem();
        console.log('Auth system initialized');
    } catch (error) {
        console.error('Auth system initialization error:', error);
        // Show error message
        document.getElementById('loading').textContent = 'Error loading game. Please refresh the page.';
    }
});
