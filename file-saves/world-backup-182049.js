// === WORLD GENERATION ===
// Terrain generation, chunks, and biomes

// Game constants
const CHUNK_SIZE = 16;
const WORLD_HEIGHT = 24;
const SEA_LEVEL = 12;
const RENDER_DISTANCE = 3;

// Biomes
const BIOMES = {
    PLAINS: 'plains',
    FOREST: 'forest',
    DESERT: 'desert',
    SNOW: 'snow',
    MOUNTAINS: 'mountains'
};

function getBiome(x, z) {
    const temp = noise.octaveNoise(x * 0.01, z * 0.01, 2);
    const humidity = noise.octaveNoise(x * 0.008 + 1000, z * 0.008 + 1000, 2);
    const height = getHeightAt(x, z);
    
    if (height > 20) return BIOMES.MOUNTAINS;
    if (temp < 0.3) return BIOMES.SNOW;
    if (humidity < 0.3) return BIOMES.DESERT;
    if (humidity > 0.7) return BIOMES.FOREST;
    return BIOMES.PLAINS;
}

function getHeightAt(x, z) {
    // Check if there's a placed block at this position first
    if (window.world && window.world.heightMap) {
        const heightKey = `${Math.floor(x)},${Math.floor(z)}`;
        const placedHeight = window.world.heightMap.get(heightKey);
        if (placedHeight !== undefined) {
            return placedHeight;
        }
    }

    // Check all chunks for the highest block at this position
    if (window.world && window.world.chunks) {
        const chunkX = Math.floor(x / 16);
        const chunkZ = Math.floor(z / 16);
        const localX = Math.floor(x) - (chunkX * 16);
        const localZ = Math.floor(z) - (chunkZ * 16);

        const chunkKey = `${chunkX},${chunkZ}`;
        const chunk = window.world.chunks.get(chunkKey);

        if (chunk) {
            // Find highest non-air block at this position
            for (let y = WORLD_HEIGHT - 1; y >= 0; y--) {
                const block = chunk.getBlock(localX, y, localZ);
                if (block && block !== BLOCKS.AIR) {
                    return y;
                }
            }
        }
    }

    // Fallback to terrain generation
    let height = noise.octaveNoise(x * 0.005, z * 0.005, 4) * 15;
    height += noise.octaveNoise(x * 0.02, z * 0.02, 2) * 5;
    return Math.floor(height + SEA_LEVEL);
}

function getBlockType(x, y, z, biome) {
    const height = getHeightAt(x, z);
    
    // Water
    if (y <= SEA_LEVEL && y > height) {
        return BLOCKS.WATER;
    }
    
    // Air
    if (y > height) return BLOCKS.AIR;
    
    // Underground caves (simple 3D noise)
    if (y < height - 2 && y > 2) {
        const caveNoise1 = noise.octaveNoise(x * 0.03, y * 0.03, 2);
        const caveNoise2 = noise.octaveNoise(z * 0.03, y * 0.03, 2);
        const caveNoise3 = noise.octaveNoise(x * 0.02, z * 0.02, 2);
        
        // Combine noise for 3D cave system
        const caveValue = (caveNoise1 + caveNoise2 + caveNoise3) / 3;
        
        if (caveValue > 0.6) {
            return BLOCKS.AIR; // Cave space
        }
    }
    
    // Surface
    if (y === height) {
        switch(biome) {
            case BIOMES.DESERT: return BLOCKS.SAND;
            case BIOMES.SNOW: return BLOCKS.SNOW;
            default: return BLOCKS.GRASS;
        }
    }
    
    // Subsurface
    if (y > height - 3) {
        return biome === BIOMES.DESERT ? BLOCKS.SAND : BLOCKS.DIRT;
    }
    
    return BLOCKS.STONE;
}

// Chunk class - simplified for performance
class Chunk {
    constructor(chunkX, chunkZ) {
        this.x = chunkX;
        this.z = chunkZ;
        this.blocks = new Array(CHUNK_SIZE * CHUNK_SIZE * WORLD_HEIGHT);
        this.mesh = null;
        this.biome = getBiome(chunkX * CHUNK_SIZE + 8, chunkZ * CHUNK_SIZE + 8);
        
        this.generate();
    }
    
    generate() {
        const startX = this.x * CHUNK_SIZE;
        const startZ = this.z * CHUNK_SIZE;
        
        for (let x = 0; x < CHUNK_SIZE; x++) {
            for (let z = 0; z < CHUNK_SIZE; z++) {
                const worldX = startX + x;
                const worldZ = startZ + z;
                const biome = getBiome(worldX, worldZ);
                
                for (let y = 0; y < WORLD_HEIGHT; y++) {
                    const blockType = getBlockType(worldX, y, worldZ, biome);
                    this.setBlock(x, y, z, blockType);
                }
            }
        }
        
        // Add some trees (simplified)
        if (this.biome === BIOMES.FOREST || this.biome === BIOMES.PLAINS) {
            this.generateTrees();
        }
    }
    
    generateTrees() {
        for (let x = 2; x < CHUNK_SIZE - 2; x += 4) {
            for (let z = 2; z < CHUNK_SIZE - 2; z += 4) {
                const worldX = this.x * CHUNK_SIZE + x;
                const worldZ = this.z * CHUNK_SIZE + z;
                const treeNoise = noise.noise(worldX * 0.1, worldZ * 0.1);
                
                if (treeNoise > 0.6) {
                    const height = getHeightAt(worldX, worldZ);
                    if (height > SEA_LEVEL) {
                        this.generateTree(x, height + 1, z);
                    }
                }
            }
        }
    }
    
    generateTree(x, y, z) {
        const treeHeight = 4 + Math.floor(Math.random() * 3);
        
        // Generate trunk
        for (let i = 0; i < treeHeight; i++) {
            if (y + i < WORLD_HEIGHT) {
                this.setBlock(x, y + i, z, BLOCKS.WOOD);

                // Update height map for collision
                const worldX = this.x * CHUNK_SIZE + x;
                const worldZ = this.z * CHUNK_SIZE + z;
                if (!window.world.heightMap) {
                    window.world.heightMap = new Map();
                }
                const currentHeight = window.world.heightMap.get(`${worldX},${worldZ}`) || 0;
                if (y + i > currentHeight) {
                    window.world.heightMap.set(`${worldX},${worldZ}`, y + i);
                }
            }
        }
        
        // Generate beautiful leafy canopy
        const leafY = y + treeHeight;
        for (let dx = -2; dx <= 2; dx++) {
            for (let dz = -2; dz <= 2; dz++) {
                for (let dy = -1; dy <= 1; dy++) {
                    // Create a nice rounded tree shape
                    if (Math.abs(dx) + Math.abs(dz) + Math.abs(dy) <= 3) {
                        const leafX = x + dx;
                        const leafZ = z + dz;
                        const leafYPos = leafY + dy;
                        
                        if (leafX >= 0 && leafX < CHUNK_SIZE && 
                            leafZ >= 0 && leafZ < CHUNK_SIZE && 
                            leafYPos < WORLD_HEIGHT && leafYPos > 0) {
                            
                            if (this.getBlock(leafX, leafYPos, leafZ) === BLOCKS.AIR) {
                                this.setBlock(leafX, leafYPos, leafZ, BLOCKS.LEAVES);

                                // Update height map for leaf collision
                                const worldX = this.x * CHUNK_SIZE + leafX;
                                const worldZ = this.z * CHUNK_SIZE + leafZ;
                                if (!window.world.heightMap) {
                                    window.world.heightMap = new Map();
                                }
                                const currentHeight = window.world.heightMap.get(`${worldX},${worldZ}`) || 0;
                                if (leafYPos > currentHeight) {
                                    window.world.heightMap.set(`${worldX},${worldZ}`, leafYPos);
                                }
                            }
                        }
                    }
                }
            }
        }
        
        // Add extra leaves on top for better shape
        if (leafY + 2 < WORLD_HEIGHT) {
            this.setBlock(x, leafY + 2, z, BLOCKS.LEAVES);
        }
    }
    
    setBlock(x, y, z, blockType) {
        const index = x + z * CHUNK_SIZE + y * CHUNK_SIZE * CHUNK_SIZE;
        this.blocks[index] = blockType;
    }
    
    getBlock(x, y, z) {
        if (x < 0 || x >= CHUNK_SIZE || y < 0 || y >= WORLD_HEIGHT || z < 0 || z >= CHUNK_SIZE) {
            return BLOCKS.AIR;
        }
        const index = x + z * CHUNK_SIZE + y * CHUNK_SIZE * CHUNK_SIZE;
        return this.blocks[index] || BLOCKS.AIR;
    }
    
    createMesh() {
        if (this.mesh) {
            scene.remove(this.mesh);
            if (this.mesh.geometry) this.mesh.geometry.dispose();
        }
        
        const geometry = new THREE.BufferGeometry();
        const vertices = [];
        const colors = [];
        const indices = [];
        let vertexIndex = 0;
        
        const blockColors = {
            [BLOCKS.GRASS]: [0.3, 0.7, 0.3],
            [BLOCKS.DIRT]: [0.5, 0.3, 0.1],
            [BLOCKS.STONE]: [0.5, 0.5, 0.5],
            [BLOCKS.SAND]: [0.9, 0.7, 0.4],
            [BLOCKS.SNOW]: [1, 1, 1],
            [BLOCKS.WATER]: [0, 0.5, 0.8],
            [BLOCKS.WOOD]: [0.4, 0.2, 0.1],
            [BLOCKS.LEAVES]: [0.1, 0.5, 0.1]
        };
        
        for (let x = 0; x < CHUNK_SIZE; x++) {
            for (let y = 0; y < WORLD_HEIGHT; y++) {
                for (let z = 0; z < CHUNK_SIZE; z++) {
                    const blockType = this.getBlock(x, y, z);
                    
                    if (blockType === BLOCKS.AIR) continue;
                    
                    const worldX = this.x * CHUNK_SIZE + x;
                    const worldZ = this.z * CHUNK_SIZE + z;
                    const color = blockColors[blockType] || [0.5, 0.5, 0.5];
                    
                    // Check each face
                    const faces = [
                        { dir: [0, 1, 0], verts: [[0,1,1], [1,1,1], [1,1,0], [0,1,0]] }, // top
                        { dir: [0, -1, 0], verts: [[0,0,0], [1,0,0], [1,0,1], [0,0,1]] }, // bottom
                        { dir: [1, 0, 0], verts: [[1,0,0], [1,1,0], [1,1,1], [1,0,1]] }, // right
                        { dir: [-1, 0, 0], verts: [[0,0,1], [0,1,1], [0,1,0], [0,0,0]] }, // left
                        { dir: [0, 0, 1], verts: [[1,0,1], [1,1,1], [0,1,1], [0,0,1]] }, // front
                        { dir: [0, 0, -1], verts: [[0,0,0], [0,1,0], [1,1,0], [1,0,0]] }  // back
                    ];
                    
                    faces.forEach(face => {
                        const [dx, dy, dz] = face.dir;
                        const nx = x + dx;
                        const ny = y + dy;
                        const nz = z + dz;
                        
                        let neighborType = BLOCKS.AIR;
                        if (nx >= 0 && nx < CHUNK_SIZE && ny >= 0 && ny < WORLD_HEIGHT && nz >= 0 && nz < CHUNK_SIZE) {
                            neighborType = this.getBlock(nx, ny, nz);
                        }
                        
                        if (neighborType === BLOCKS.AIR || (blockType !== BLOCKS.WATER && neighborType === BLOCKS.WATER)) {
                            face.verts.forEach(vert => {
                                vertices.push(
                                    worldX + vert[0],
                                    y + vert[1],
                                    worldZ + vert[2]
                                );
                                colors.push(color[0], color[1], color[2]);
                            });
                            
                            indices.push(
                                vertexIndex, vertexIndex + 1, vertexIndex + 2,
                                vertexIndex, vertexIndex + 2, vertexIndex + 3
                            );
                            vertexIndex += 4;
                        }
                    });
                }
            }
        }
        
        if (vertices.length > 0) {
            geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
            geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));
            geometry.setIndex(indices);
            geometry.computeVertexNormals();
            
            const material = new THREE.MeshLambertMaterial({ 
                vertexColors: true,
                side: THREE.DoubleSide
            });
            
            this.mesh = new THREE.Mesh(geometry, material);
            scene.add(this.mesh);
        }
    }
}

// World class - simplified
class World {
    constructor() {
        this.chunks = new Map();
        this.orcs = [];
        this.maxOrcs = 5; // Reduced for performance
        this.orcSpawnTimer = 0;
        this.heightMap = new Map(); // For tracking placed blocks and tree collision

        // Building collision system - prevents walking through buildings
        this.buildingCollisions = [];
        this.currentLocation = "Starting Area";
        this.locationCheckTimer = 0;

        // NPC system
        this.npcManager = null;

        // Story locations from the new features
        this.STORY_LOCATIONS = {
            SPAWN: { x: 0, z: 0, name: "Starting Area", description: "Where your adventure begins..." },
            TOWN: { x: 100, z: 50, name: "Town", description: "A peaceful town with cobblestone streets and shops." },
            FOREST: { x: -80, z: 100, name: "Forest", description: "A dense forest with tall trees." },
            CAVE: { x: 150, z: -50, name: "Cave", description: "A dark cave with mysterious sounds." },
            VILLAGE: { x: -150, z: 200, name: "Village", description: "A peaceful village with friendly blacksmiths." }
        };

        // Initialize story locations
        this.createLocationMarkers();

        // Initialize NPCs (after a short delay to ensure everything is loaded)
        setTimeout(() => {
            this.initializeNPCs();
        }, 2000);
    }

    getChunkKey(chunkX, chunkZ) {
        return `${chunkX},${chunkZ}`;
    }

    getChunk(chunkX, chunkZ) {
        const key = this.getChunkKey(chunkX, chunkZ);
        if (!this.chunks.has(key)) {
            this.chunks.set(key, new Chunk(chunkX, chunkZ));
        }
        return this.chunks.get(key);
    }

    updateChunks(playerX, playerZ) {
        const playerChunkX = Math.floor(playerX / CHUNK_SIZE);
        const playerChunkZ = Math.floor(playerZ / CHUNK_SIZE);

        const neededChunks = new Set();

        for (let x = playerChunkX - RENDER_DISTANCE; x <= playerChunkX + RENDER_DISTANCE; x++) {
            for (let z = playerChunkZ - RENDER_DISTANCE; z <= playerChunkZ + RENDER_DISTANCE; z++) {
                const key = this.getChunkKey(x, z);
                neededChunks.add(key);

                const chunk = this.getChunk(x, z);
                if (!chunk.mesh) {
                    chunk.createMesh();
                }
            }
        }

        // Remove distant chunks
        for (const [key, chunk] of this.chunks) {
            if (!neededChunks.has(key)) {
                if (chunk.mesh) {
                    scene.remove(chunk.mesh);
                    if (chunk.mesh.geometry) chunk.mesh.geometry.dispose();
                }
                this.chunks.delete(key);
            }
        }
    }

    updateEnemies(deltaTime, playerPosition) {
        // Update existing orcs
        this.orcs = this.orcs.filter(orc => {
            if (orc.health <= 0) {
                return false; // Remove dead orcs
            }
            orc.update(deltaTime, playerPosition);
            return true;
        });

        // Spawn new orcs occasionally
        this.orcSpawnTimer += deltaTime;
        if (this.orcSpawnTimer > 10 && this.orcs.length < this.maxOrcs) {
            this.spawnOrc(playerPosition);
            this.orcSpawnTimer = 0;
        }
    }

    spawnOrc(playerPosition) {
        // Spawn orc at a random location around the player
        const angle = Math.random() * Math.PI * 2;
        const distance = 20 + Math.random() * 20;

        const spawnX = playerPosition.x + Math.cos(angle) * distance;
        const spawnZ = playerPosition.z + Math.sin(angle) * distance;

        // Make sure spawn location is above ground
        const groundHeight = getHeightAt(spawnX, spawnZ);
        if (groundHeight > SEA_LEVEL) {
            const orc = new Orc(spawnX, spawnZ);
            this.orcs.push(orc);
            console.log(`Spawned orc at (${Math.floor(spawnX)}, ${Math.floor(spawnZ)})`);
        }
    }

    // Building collision system - prevents walking through buildings
    addBuildingCollision(x, z, groundHeight, width, height, depth) {
        const collision = {
            minX: x - width/2,
            maxX: x + width/2,
            minY: groundHeight,
            maxY: groundHeight + height,
            minZ: z - depth/2,
            maxZ: z + depth/2
        };
        this.buildingCollisions.push(collision);
    }

    checkBuildingCollision(position) {
        // Check collision with buildings (add player radius for better collision)
        const playerRadius = 0.5;
        for (const building of this.buildingCollisions) {
            if (position.x + playerRadius >= building.minX && position.x - playerRadius <= building.maxX &&
                position.z + playerRadius >= building.minZ && position.z - playerRadius <= building.maxZ &&
                position.y >= building.minY && position.y <= building.maxY) {
                return true; // Collision detected - player should be stopped
            }
        }
        return false;
    }

    // Create story location markers
    createLocationMarkers() {
        // Create structures for each story location
        Object.entries(this.STORY_LOCATIONS).forEach(([key, location]) => {
            if (key === 'SPAWN') return; // Skip spawn point

            const groundHeight = getHeightAt(location.x, location.z);

            if (key === 'TOWN') {
                this.createTown(location.x, location.z, groundHeight);
            } else if (key === 'VILLAGE') {
                this.createVillage(location.x, location.z, groundHeight);
            } else if (key === 'CAVE') {
                this.createCaveEntrance(location.x, location.z, groundHeight);
            } else {
                // Create simple marker for other locations
                this.createSimpleMarker(location.x, location.z, groundHeight, location.name, key);
            }

            console.log(`Created ${location.name} at (${location.x}, ${location.z})`);
        });
    }

    createTown(x, z, groundHeight) {
        // Create town center with multiple buildings (moved Town Hall away from fountain)
        const buildings = [
            { x: -25, z: 0, width: 12, height: 8, depth: 12, color: 0xDEB887, name: "Town Hall" },
            { x: -20, z: -15, width: 8, height: 6, depth: 10, color: 0xD2B48C, name: "Shop" },
            { x: 20, z: -15, width: 8, height: 6, depth: 8, color: 0xF5DEB3, name: "House" },
            { x: -15, z: 20, width: 6, height: 5, depth: 8, color: 0xDEB887, name: "House" },
            { x: 15, z: 20, width: 7, height: 5, depth: 9, color: 0xF5DEB3, name: "House" },
            { x: 0, z: -30, width: 10, height: 7, depth: 8, color: 0xD2B48C, name: "Inn" }
        ];

        buildings.forEach(building => {
            const buildingX = x + building.x;
            const buildingZ = z + building.z;
            const buildingGroundHeight = getHeightAt(buildingX, buildingZ);

            // Create building
            const buildingGeometry = new THREE.BoxGeometry(building.width, building.height, building.depth);
            const buildingMaterial = new THREE.MeshLambertMaterial({ color: building.color });
            const buildingMesh = new THREE.Mesh(buildingGeometry, buildingMaterial);
            buildingMesh.position.set(buildingX, buildingGroundHeight + building.height/2, buildingZ);
            scene.add(buildingMesh);

            // Add collision box - prevents walking through building
            this.addBuildingCollision(buildingX, buildingZ, buildingGroundHeight, building.width, building.height, building.depth);

            // Create pyramid-style roof
            this.createPyramidRoof(buildingX, buildingZ, buildingGroundHeight + building.height, building.width, building.depth);

            // Door
            const doorGeometry = new THREE.BoxGeometry(1.5, 3, 0.2);
            const doorMaterial = new THREE.MeshLambertMaterial({ color: 0x4A4A4A });
            const door = new THREE.Mesh(doorGeometry, doorMaterial);
            door.position.set(buildingX, buildingGroundHeight + 1.5, buildingZ - building.depth/2 + 0.1);
            scene.add(door);
        });

        // Create accessible fountain in the center (now that Town Hall is moved)
        this.createFountain(x, z, groundHeight);

        // Create town sign
        this.createTownSign(x, z, groundHeight, "TOWN");
    }

    createVillage(x, z, groundHeight) {
        // Smaller village with blacksmith
        const buildings = [
            { x: 0, z: 0, width: 10, height: 6, depth: 8, color: 0x654321, name: "Blacksmith" },
            { x: -15, z: 10, width: 6, height: 5, depth: 6, color: 0x8B4513, name: "House" },
            { x: 15, z: -10, width: 6, height: 5, depth: 6, color: 0xA0522D, name: "House" }
        ];

        buildings.forEach(building => {
            const buildingGeometry = new THREE.BoxGeometry(building.width, building.height, building.depth);
            const buildingMaterial = new THREE.MeshLambertMaterial({ color: building.color });
            const buildingMesh = new THREE.Mesh(buildingGeometry, buildingMaterial);
            buildingMesh.position.set(x + building.x, groundHeight + building.height/2, z + building.z);
            scene.add(buildingMesh);

            // Add collision - prevents walking through building
            this.addBuildingCollision(x + building.x, z + building.z, groundHeight, building.width, building.height, building.depth);

            // Create pyramid-style roof
            this.createPyramidRoof(x + building.x, z + building.z, groundHeight + building.height, building.width, building.depth, 0x8B0000);
        });

        this.createTownSign(x, z, groundHeight, "VILLAGE");
    }

    createCaveEntrance(x, z, groundHeight) {
        // Create cave entrance
        const caveGeometry = new THREE.SphereGeometry(8, 8, 8, 0, Math.PI);
        const caveMaterial = new THREE.MeshLambertMaterial({ color: 0x2F2F2F });
        const cave = new THREE.Mesh(caveGeometry, caveMaterial);
        cave.position.set(x, groundHeight + 4, z);
        cave.rotation.x = Math.PI / 2;
        scene.add(cave);

        this.createTownSign(x, z + 15, groundHeight, "CAVE");
    }

    createSimpleMarker(x, z, groundHeight, name, key) {
        // Create a simple marker for other locations
        const markerGeometry = new THREE.BoxGeometry(2, 8, 2);
        const markerMaterial = new THREE.MeshLambertMaterial({ color: 0x654321 });
        const marker = new THREE.Mesh(markerGeometry, markerMaterial);
        marker.position.set(x, groundHeight + 4, z);
        scene.add(marker);

        this.createTownSign(x, z, groundHeight, name.toUpperCase());
    }

    createTownSign(x, z, groundHeight, text) {
        // Create a simple sign post
        const postGeometry = new THREE.BoxGeometry(0.3, 4, 0.3);
        const postMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const post = new THREE.Mesh(postGeometry, postMaterial);
        post.position.set(x - 8, groundHeight + 2, z - 8);
        scene.add(post);

        // Sign board
        const signGeometry = new THREE.BoxGeometry(4, 2, 0.2);
        const signMaterial = new THREE.MeshLambertMaterial({ color: 0xF5DEB3 });
        const sign = new THREE.Mesh(signGeometry, signMaterial);
        sign.position.set(x - 8, groundHeight + 3, z - 8);
        scene.add(sign);
    }

    createPyramidRoof(x, z, baseY, width, depth, color = 0x8B7355) {
        // Create a pyramid-style roof using blocks
        const roofMaterial = new THREE.MeshLambertMaterial({ color: color });

        // Calculate how many layers we need (smaller buildings = fewer layers)
        const maxSize = Math.max(width, depth);
        const layers = Math.ceil(maxSize / 3); // Roughly 1 layer per 3 blocks

        for (let layer = 0; layer < layers; layer++) {
            // Each layer gets smaller
            const layerWidth = Math.max(2, width - layer * 2);
            const layerDepth = Math.max(2, depth - layer * 2);

            // Stop if the layer would be too small
            if (layerWidth <= 0 || layerDepth <= 0) break;

            // Create the roof block for this layer
            const roofGeometry = new THREE.BoxGeometry(layerWidth, 1, layerDepth);
            const roofBlock = new THREE.Mesh(roofGeometry, roofMaterial);

            // Position each layer higher and more centered
            roofBlock.position.set(
                x,
                baseY + layer + 0.5,
                z
            );

            scene.add(roofBlock);
        }
    }

    createFountain(x, z, groundHeight) {
        // Create an accessible fountain in the town center
        // Water pool (blue block at ground level)
        const waterGeometry = new THREE.BoxGeometry(6, 0.3, 6);
        const waterMaterial = new THREE.MeshLambertMaterial({ color: 0x4169E1 });
        const water = new THREE.Mesh(waterGeometry, waterMaterial);
        water.position.set(x, groundHeight + 0.15, z);
        scene.add(water);

        // Fountain spout (gray cylinder in center)
        const spoutGeometry = new THREE.CylinderGeometry(0.5, 0.5, 2, 8);
        const spoutMaterial = new THREE.MeshLambertMaterial({ color: 0x708090 });
        const spout = new THREE.Mesh(spoutGeometry, spoutMaterial);
        spout.position.set(x, groundHeight + 1, z);
        scene.add(spout);

        // Add collision for fountain (smaller area so people can walk around it)
        this.addBuildingCollision(x, z, groundHeight, 4, 1, 4);
    }

    initializeNPCs() {
        // Initialize NPC manager if the class is available
        if (typeof NPCManager !== 'undefined') {
            this.npcManager = new NPCManager();

            // Spawn some initial NPCs in the town with brown clothing
            this.npcManager.spawnNPC(95, 45, 0x654321, "Town Guard");
            this.npcManager.spawnNPC(105, 55, 0x8B4513, "Shopkeeper");
            this.npcManager.spawnNPC(110, 50, 0xA0522D, "Villager");

            // Spawn some NPCs in the village with brown clothing
            this.npcManager.spawnNPC(-145, 195, 0x964B00, "Blacksmith");
            this.npcManager.spawnNPC(-155, 205, 0x6F4E37, "Farmer");

            console.log('NPCs initialized successfully!');
        } else {
            console.warn('NPCManager not available - NPCs not initialized');
        }
    }

    updateNPCs(deltaTime, playerPosition) {
        if (this.npcManager) {
            this.npcManager.update(deltaTime, playerPosition);
        }
    }
}
