// === NPC SYSTEM ===
// Basic NPCs that walk around as blocks

class NPC {
    constructor(x, z, clothingColor = 0x8B4513, name = "Villager") {
        this.name = name;
        this.startX = x;
        this.startZ = z;
        this.position = new THREE.Vector3(x, 0, z);
        this.velocity = new THREE.Vector3(0, 0, 0);
        this.clothingColor = clothingColor;
        this.skinColor = 0xFFDBB5; // Peach skin color
        
        // Movement properties
        this.speed = 2; // blocks per second
        this.wanderRadius = 15; // how far from spawn point they wander
        this.directionChangeTimer = 0;
        this.directionChangeInterval = 3 + Math.random() * 4; // 3-7 seconds
        this.currentDirection = new THREE.Vector3(0, 0, 0);
        
        // Animation properties
        this.bobOffset = Math.random() * Math.PI * 2; // Random start for bob animation
        this.walkAnimationSpeed = 4;
        
        // Create the NPC mesh
        this.createMesh();
        
        // Set initial position on ground
        this.updateGroundPosition();
        
        // Choose initial random direction
        this.chooseNewDirection();
        
        console.log(`Created NPC ${this.name} at (${Math.floor(x)}, ${Math.floor(z)})`);
    }
    
    createMesh() {
        // Create a simple block character
        const group = new THREE.Group();

        // Body (main block) - brown clothing
        const bodyGeometry = new THREE.BoxGeometry(1.2, 1.8, 0.8);
        const bodyMaterial = new THREE.MeshLambertMaterial({ color: this.clothingColor });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.y = 0.9; // Center the body
        group.add(body);

        // Head (smaller block on top) - peach skin
        const headGeometry = new THREE.BoxGeometry(0.8, 0.8, 0.8);
        const headMaterial = new THREE.MeshLambertMaterial({ color: this.skinColor });
        const head = new THREE.Mesh(headGeometry, headMaterial);
        head.position.y = 2.2; // On top of body
        group.add(head);
        
        // Simple face (two white eyes)
        const eyeGeometry = new THREE.BoxGeometry(0.1, 0.1, 0.1);
        const eyeMaterial = new THREE.MeshLambertMaterial({ color: 0xffffff });
        
        const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
        leftEye.position.set(-0.2, 2.3, 0.41);
        group.add(leftEye);
        
        const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
        rightEye.position.set(0.2, 2.3, 0.41);
        group.add(rightEye);
        
        // Arms with sleeves
        const armGeometry = new THREE.BoxGeometry(0.4, 1.2, 0.4);

        // Left arm with sleeve
        const leftArmSkin = new THREE.Mesh(armGeometry, new THREE.MeshLambertMaterial({ color: this.skinColor }));
        leftArmSkin.position.set(-0.8, 1.2, 0);
        group.add(leftArmSkin);

        const leftSleeve = new THREE.Mesh(
            new THREE.BoxGeometry(0.42, 0.8, 0.42),
            new THREE.MeshLambertMaterial({ color: this.clothingColor })
        );
        leftSleeve.position.set(-0.8, 1.0, 0); // Upper part of arm
        group.add(leftSleeve);

        // Right arm with sleeve
        const rightArmSkin = new THREE.Mesh(armGeometry, new THREE.MeshLambertMaterial({ color: this.skinColor }));
        rightArmSkin.position.set(0.8, 1.2, 0);
        group.add(rightArmSkin);

        const rightSleeve = new THREE.Mesh(
            new THREE.BoxGeometry(0.42, 0.8, 0.42),
            new THREE.MeshLambertMaterial({ color: this.clothingColor })
        );
        rightSleeve.position.set(0.8, 1.0, 0); // Upper part of arm
        group.add(rightSleeve);

        // Legs (small blocks at bottom) - brown clothing
        const legGeometry = new THREE.BoxGeometry(0.4, 0.8, 0.4);
        const legMaterial = new THREE.MeshLambertMaterial({ color: this.clothingColor });

        const leftLeg = new THREE.Mesh(legGeometry, legMaterial);
        leftLeg.position.set(-0.3, 0.4, 0);
        group.add(leftLeg);

        const rightLeg = new THREE.Mesh(legGeometry, legMaterial);
        rightLeg.position.set(0.3, 0.4, 0);
        group.add(rightLeg);
        
        this.mesh = group;
        this.body = body;
        this.head = head;
        this.leftArm = leftArmSkin;
        this.rightArm = rightArmSkin;
        this.leftLeg = leftLeg;
        this.rightLeg = rightLeg;
        
        scene.add(this.mesh);
    }
    
    updateGroundPosition() {
        // Set Y position to ground level
        const groundHeight = getHeightAt(Math.floor(this.position.x), Math.floor(this.position.z));
        this.position.y = groundHeight + 1; // 1 block above ground
    }
    
    chooseNewDirection() {
        // Choose a random direction to walk
        const angle = Math.random() * Math.PI * 2;
        this.currentDirection.set(
            Math.cos(angle),
            0,
            Math.sin(angle)
        );
        this.currentDirection.normalize();
        
        // Reset timer
        this.directionChangeTimer = 0;
        this.directionChangeInterval = 3 + Math.random() * 4; // 3-7 seconds
    }
    
    update(deltaTime, playerPosition) {
        // Update direction change timer
        this.directionChangeTimer += deltaTime;
        
        // Change direction periodically or if too far from spawn
        const distanceFromSpawn = Math.sqrt(
            Math.pow(this.position.x - this.startX, 2) + 
            Math.pow(this.position.z - this.startZ, 2)
        );
        
        if (this.directionChangeTimer >= this.directionChangeInterval || distanceFromSpawn > this.wanderRadius) {
            if (distanceFromSpawn > this.wanderRadius) {
                // Head back towards spawn point
                this.currentDirection.set(
                    this.startX - this.position.x,
                    0,
                    this.startZ - this.position.z
                );
                this.currentDirection.normalize();
            } else {
                // Choose random direction
                this.chooseNewDirection();
            }
        }
        
        // Move in current direction
        this.velocity.copy(this.currentDirection);
        this.velocity.multiplyScalar(this.speed);
        
        // Apply movement
        const oldPosition = this.position.clone();
        this.position.add(this.velocity.clone().multiplyScalar(deltaTime));
        
        // Check building collision and rollback if needed
        if (window.world && window.world.checkBuildingCollision && window.world.checkBuildingCollision(this.position)) {
            this.position.copy(oldPosition);
            // Choose new direction if we hit something
            this.chooseNewDirection();
        }
        
        // Update ground position
        this.updateGroundPosition();
        
        // Update mesh position
        if (this.mesh) {
            this.mesh.position.copy(this.position);
            
            // Face movement direction
            if (this.velocity.length() > 0.1) {
                const angle = Math.atan2(this.velocity.x, this.velocity.z);
                this.mesh.rotation.y = angle;
            }
            
            // Walking animation (bob up and down)
            const time = Date.now() * 0.001;
            const isMoving = this.velocity.length() > 0.1;
            
            if (isMoving) {
                // Bob the whole character
                const bobAmount = Math.sin((time + this.bobOffset) * this.walkAnimationSpeed) * 0.1;
                this.mesh.position.y += bobAmount;
                
                // Swing arms
                const armSwing = Math.sin((time + this.bobOffset) * this.walkAnimationSpeed * 2) * 0.3;
                this.leftArm.rotation.x = armSwing;
                this.rightArm.rotation.x = -armSwing;
                
                // Move legs
                const legSwing = Math.sin((time + this.bobOffset) * this.walkAnimationSpeed * 2) * 0.2;
                this.leftLeg.rotation.x = legSwing;
                this.rightLeg.rotation.x = -legSwing;
            } else {
                // Reset animations when not moving
                this.leftArm.rotation.x = 0;
                this.rightArm.rotation.x = 0;
                this.leftLeg.rotation.x = 0;
                this.rightLeg.rotation.x = 0;
            }
        }
    }
    
    destroy() {
        if (this.mesh) {
            scene.remove(this.mesh);
        }
    }
}

// NPC Manager
class NPCManager {
    constructor() {
        this.npcs = [];
        this.maxNPCs = 8; // Maximum number of NPCs in the world
        this.spawnTimer = 0;
        this.spawnInterval = 5; // Spawn new NPC every 5 seconds if under max
    }
    
    spawnNPC(x, z, clothingColor, name) {
        if (this.npcs.length >= this.maxNPCs) {
            return null;
        }

        const npc = new NPC(x, z, clothingColor, name);
        this.npcs.push(npc);
        return npc;
    }
    
    spawnRandomNPC() {
        // Spawn NPCs near story locations
        const locations = [
            { x: 100, z: 50, name: "Town" },      // Town
            { x: -150, z: 200, name: "Village" }, // Village
            { x: 0, z: 0, name: "Spawn" }         // Spawn area
        ];
        
        const location = locations[Math.floor(Math.random() * locations.length)];
        
        // Random position near the location
        const offsetX = (Math.random() - 0.5) * 40; // ±20 blocks
        const offsetZ = (Math.random() - 0.5) * 40;
        
        const spawnX = location.x + offsetX;
        const spawnZ = location.z + offsetZ;
        
        // Random brown clothing shades and names
        const clothingColors = [
            0x8B4513, // Saddle brown
            0xA0522D, // Sienna
            0x654321, // Dark brown
            0x964B00, // Traditional brown
            0x6F4E37, // Coffee brown
            0x8B7355  // Burlywood
        ];
        const names = ["Villager", "Trader", "Farmer", "Guard", "Citizen", "Wanderer"];

        const clothingColor = clothingColors[Math.floor(Math.random() * clothingColors.length)];
        const name = names[Math.floor(Math.random() * names.length)];

        return this.spawnNPC(spawnX, spawnZ, clothingColor, name);
    }
    
    update(deltaTime, playerPosition) {
        // Update existing NPCs
        this.npcs.forEach(npc => {
            npc.update(deltaTime, playerPosition);
        });
        
        // Spawn new NPCs if under max
        this.spawnTimer += deltaTime;
        if (this.spawnTimer >= this.spawnInterval && this.npcs.length < this.maxNPCs) {
            this.spawnRandomNPC();
            this.spawnTimer = 0;
        }
    }
    
    removeNPC(npc) {
        const index = this.npcs.indexOf(npc);
        if (index > -1) {
            npc.destroy();
            this.npcs.splice(index, 1);
        }
    }
    
    removeAllNPCs() {
        this.npcs.forEach(npc => npc.destroy());
        this.npcs = [];
    }
}

// Export for global access
window.NPCManager = NPCManager;
window.NPC = NPC;
