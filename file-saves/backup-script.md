# Automatic Backup System

This folder contains backups of all game files. Files are automatically backed up before any major changes.

## Current Backup Files:
- index.html (main HTML file)
- main.js (main game loop)
- world.js (world generation and management)
- player.js (player controls and mechanics)
- npcs.js (NPC system)
- ui.js (user interface)
- story.js (character creation and RPG system)
- auth.js (authentication system)
- inventory.js (inventory management)
- enemies.js (enemy AI)
- items.js (item definitions)
- characters.js (character system)
- noise.js (terrain generation)
- saveload.js (save/load system)

## Backup Strategy:
Files are backed up automatically before any modifications to ensure we can always revert changes if something goes wrong.

## Last Backup: 
Current session - all files backed up before NPC color changes.
