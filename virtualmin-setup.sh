#!/bin/bash

echo "🚀 Beetle3D Server Setup for VirtualMin"
echo "========================================"

# Function to test command
test_command() {
    if command -v "$1" >/dev/null 2>&1; then
        echo "✅ $1 found: $(command -v $1)"
        return 0
    else
        echo "❌ $1 not found"
        return 1
    fi
}

# Test Node.js installation
echo "🔍 Checking Node.js installation..."
NODE_CMD=""
if test_command node; then
    NODE_CMD="node"
elif test_command nodejs; then
    NODE_CMD="nodejs"
else
    echo ""
    echo "❌ Node.js not detected on this VirtualMin server"
    echo ""
    echo "🔧 To install Node.js on VirtualMin:"
    echo "1. Via VirtualMin Panel:"
    echo "   - Go to System Settings → Software Packages"
    echo "   - Search for 'nodejs' and install"
    echo ""
    echo "2. Via command line:"
    echo "   sudo apt update"
    echo "   sudo apt install nodejs npm"
    echo ""
    echo "3. Via NodeSource (recommended):"
    echo "   curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -"
    echo "   sudo apt-get install -y nodejs"
    echo ""
    exit 1
fi

# Test npm
echo "🔍 Checking npm..."
NPM_CMD=""
if test_command npm; then
    NPM_CMD="npm"
else
    echo "❌ npm not found. Installing npm..."
    sudo apt install npm -y
    NPM_CMD="npm"
fi

# Show versions
echo ""
echo "✅ Node.js version: $($NODE_CMD --version)"
echo "✅ npm version: $($NPM_CMD --version)"

# Test MariaDB/MySQL
echo ""
echo "🔍 Checking database..."
DB_CMD=""
if test_command mysql; then
    DB_CMD="mysql"
    echo "✅ MySQL client found"
elif test_command mariadb; then
    DB_CMD="mariadb"
    echo "✅ MariaDB client found"
else
    echo "❌ Database client not found"
    echo "Installing MariaDB client..."
    sudo apt install mariadb-client -y
    DB_CMD="mysql"
fi

# Test database connection
echo ""
echo "🔍 Testing database connection..."
echo "Database: beetle"
echo "User: beetle"
echo "Password: 4SrTF6CYdFhKYhG"

if $DB_CMD -u beetle -p4SrTF6CYdFhKYhG -e "SELECT 1;" 2>/dev/null; then
    echo "✅ Database connection successful"
    
    # Check if database exists
    if $DB_CMD -u beetle -p4SrTF6CYdFhKYhG -e "USE beetle; SELECT 1;" 2>/dev/null; then
        echo "✅ Database 'beetle' exists"
    else
        echo "⚠️  Database 'beetle' doesn't exist. Creating..."
        $DB_CMD -u beetle -p4SrTF6CYdFhKYhG -e "CREATE DATABASE beetle;" 2>/dev/null
    fi
else
    echo "❌ Database connection failed"
    echo ""
    echo "🔧 Database setup required:"
    echo "1. Make sure MariaDB/MySQL is running"
    echo "2. Create database and user:"
    echo "   sudo mysql"
    echo "   CREATE DATABASE beetle;"
    echo "   CREATE USER 'beetle'@'localhost' IDENTIFIED BY '4SrTF6CYdFhKYhG';"
    echo "   GRANT ALL PRIVILEGES ON beetle.* TO 'beetle'@'localhost';"
    echo "   FLUSH PRIVILEGES;"
    echo "   EXIT;"
fi

# Navigate to server directory
echo ""
echo "📁 Setting up server directory..."
if [ ! -d "server" ]; then
    echo "❌ Server directory not found. Please upload the server files first."
    exit 1
fi

cd server

# Check for required files
echo "🔍 Checking server files..."
if [ ! -f "package.json" ]; then
    echo "❌ package.json not found"
    exit 1
fi

if [ ! -f "server.js" ]; then
    echo "❌ server.js not found"
    exit 1
fi

if [ ! -f ".env" ]; then
    echo "❌ .env file not found"
    exit 1
fi

echo "✅ All server files found"

# Install dependencies
echo ""
echo "📦 Installing Node.js dependencies..."
$NPM_CMD install

if [ $? -eq 0 ]; then
    echo "✅ Dependencies installed successfully"
else
    echo "❌ Failed to install dependencies"
    exit 1
fi

# Setup database schema
echo ""
echo "🗄️ Setting up database schema..."
if [ -f "database.sql" ]; then
    echo "📥 Importing database schema..."
    if $DB_CMD -u beetle -p4SrTF6CYdFhKYhG beetle < database.sql 2>/dev/null; then
        echo "✅ Database schema imported"
    else
        echo "⚠️  Database import had warnings (this is usually OK)"
    fi
else
    echo "⚠️  database.sql not found"
fi

# Final check
echo ""
echo "🔍 Final system check..."
echo "✅ Node.js: $($NODE_CMD --version)"
echo "✅ npm: $($NPM_CMD --version)"

if $DB_CMD -u beetle -p4SrTF6CYdFhKYhG -e "USE beetle; SHOW TABLES;" >/dev/null 2>&1; then
    echo "✅ Database: Connected and ready"
else
    echo "⚠️  Database: Connection issues"
fi

echo ""
echo "🚀 Setup complete! Starting server..."
echo "📡 Server will run on: http://beetle3d.com:3001"
echo "🎮 Game will be at: http://beetle3d.com:3001"
echo "🌐 API endpoints: http://beetle3d.com:3001/api"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

# Start the server
$NODE_CMD server.js
